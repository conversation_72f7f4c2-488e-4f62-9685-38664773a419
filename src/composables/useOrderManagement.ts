import { computed, nextTick, ref, watch, shallowRef, markRaw } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { orderApi, type OrderListRequest, type UserPurchaseOrderListVO } from '@/api/modules/order'

// 防抖和节流工具函数
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void => {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<T>) => {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};

export const throttle = <T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
};

export function useOrderManagement() {
    const { t } = useI18n()

    // 响应式状态 - 优化性能
    const loading = ref(false)
    const loadingTime = ref(0)
    const loadingTimer = ref<NodeJS.Timeout | null>(null)
    const orders = shallowRef<UserPurchaseOrderListVO[]>([])
    const searchKeyword = ref('')
    const searchType = ref('default')
    const currentStatus = ref<number | null>(null)
    const selectedOrders = ref<string[]>([])
    const startDate = ref<string | null>(null)
    const endDate = ref<string | null>(null)
    const currentQuickRange = ref<string>('7days')

    // 订单商品数据管理 - 使用 markRaw 避免不必要的响应式
    const orderProductsData = ref(markRaw(new Map<string, any>()))
    const expandedOrders = ref(new Set<string>())

    // 分页数据
    const pagination = ref({
        page: 1,
        size: 10,
        totalElements: 0,
        totalPages: 1,
    })

    // 性能监控
    const performanceMetrics = ref({
        lastLoadTime: 0,
        renderTime: 0,
        itemsCount: 0
    })

    // 快捷时间范围选项
    const quickDateRanges = computed(() => [
        { key: '7days', label: t('orders.dateFilter.last7days', '七天') },
        { key: '15days', label: t('orders.dateFilter.last15days', '十五天') },
        { key: '30days', label: t('orders.dateFilter.last30days', '三十天') },
    ])

    // 订单状态筛选选项
    const statusFilters = computed(() => [
        { label: t('orders.status.all'), value: null, count: undefined },
        { label: t('orders.status.pending'), value: 1, count: undefined },
        { label: t('orders.status.paid'), value: 2, count: undefined },
        { label: t('orders.status.purchasing'), value: 4, count: undefined },
        { label: t('orders.status.shipped'), value: 7, count: undefined },
        { label: t('orders.status.inWarehouse'), value: 10, count: undefined },
        { label: t('orders.status.completed'), value: 13, count: undefined },
        { label: t('orders.status.cancelled'), value: 11, count: undefined },
    ])

    // 计算属性
    const isAnyOrderSelected = computed(() => selectedOrders.value.length > 0)

    // 根据搜索类型获取占位符文本
    const getSearchPlaceholder = (): string => {
        switch (searchType.value) {
            case 'purchaseOrder':
                return t('orders.search.placeholders.purchaseOrder', '输入采购单号进行搜索')
            case 'skuId':
                return t('orders.search.placeholders.skuId', '输入SKU ID进行搜索')
            case 'platformProductId':
                return t('orders.search.placeholders.platformProductId', '输入平台商品ID进行搜索')
            case 'default':
            default:
                return t('orders.search.placeholders.default', '输入商品名称或收货人名称')
        }
    }

    // 原始加载订单数据函数
    const _loadOrders = async (params: OrderListRequest = {}) => {
        loading.value = true
        loadingTime.value = 0
        
        // 启动加载计时器
        loadingTimer.value = setInterval(() => {
            loadingTime.value += 0.1;
        }, 100);
        
        try {
            const startTime = performance.now();
            // 构建请求参数
            const requestParams: OrderListRequest = {
                page: pagination.value.page,
                size: pagination.value.size,
                status: currentStatus.value === null ? undefined : currentStatus.value,
                startTime: startDate.value ? `${startDate.value}T00:00:00` : undefined,
                endTime: endDate.value ? `${endDate.value}T23:59:59` : undefined,
                ...params,
            }

            // 根据搜索类型设置搜索参数
            if (searchKeyword.value) {
                const searchValue = searchKeyword.value.trim()

                switch (searchType.value) {
                    case 'purchaseOrder':
                        requestParams.purchaseOrderNo = searchValue
                        break
                    case 'skuId':
                        requestParams.skuId = searchValue
                        break
                    case 'platformProductId':
                        requestParams.platformProductId = searchValue
                        break
                    case 'default':
                    default:
                        // 默认搜索：智能判断搜索类型
                        if (/^\d+$/.test(searchValue)) {
                            // 纯数字，同时搜索SKU ID和平台商品ID
                            requestParams.skuId = searchValue
                            requestParams.platformProductId = searchValue
                        } else if (/^[CN][0-9]+$/.test(searchValue)) {
                            // 以C或N开头的字母数字组合，作为采购单号搜索
                            requestParams.purchaseOrderNo = searchValue
                        } else {
                            // 包含字母或其他字符，作为关键词搜索（订单号、商品名称）
                            requestParams.keyword = searchValue
                        }
                        break
                }
            }

            // 添加日期范围参数
            if (startDate.value || endDate.value) {
                if (startDate.value) {
                    requestParams.startTime = `${startDate.value}T00:00:00`
                }
                if (endDate.value) {
                    requestParams.endTime = `${endDate.value}T23:59:59`
                }
            }

            const result = await orderApi.getOrderList(requestParams)
            
            // 记录性能指标
            const endTime = performance.now();
            performanceMetrics.value = {
                lastLoadTime: endTime - startTime,
                renderTime: 0,
                itemsCount: Array.isArray(result?.records) ? result.records.length : 0
            };

            if (result && typeof result.total === 'number' && Array.isArray(result.records)) {
                orders.value = result.records
                pagination.value.page = result.pageIndex
                pagination.value.size = result.pageSize
                pagination.value.totalElements = result.total
                pagination.value.totalPages = result.pageSize > 0 ? Math.ceil(result.total / result.pageSize) : 1

                // 设置订单默认展开状态并直接使用接口返回的商品数据
                nextTick(() => {
                    for (const order of result.records) {
                        if (order.orderNo) {
                            expandedOrders.value.add(order.orderNo)

                            // 直接使用接口返回的订单数据
                            if (!orderProductsData.value.has(order.orderNo)) {
                                orderProductsData.value.set(order.orderNo, order)
                            }
                        }
                    }
                })
            } else {
                orders.value = []
                pagination.value.totalElements = 0
                pagination.value.page = 1
                pagination.value.totalPages = 1
            }
        } catch (error) {
            console.error(t('orders.error.loadOrderListFailed', '加载订单列表失败:'), error)
            ElMessage.error(t('orders.error.loadFailed', '加载订单列表失败'))
        } finally {
            // 清理计时器
            if (loadingTimer.value) {
                clearInterval(loadingTimer.value);
                loadingTimer.value = null;
            }
            
            // 最少显示 300ms 加载状态，避免闪烁
            const minLoadingTime = Math.max(0, 300 - loadingTime.value * 1000);
            setTimeout(() => {
                loading.value = false;
            }, minLoadingTime);
        }
    }
    
    // 防抖的加载函数
    const loadOrders = debounce(_loadOrders, 300)

    // 设置状态筛选 - 防抖
    const setStatusFilter = debounce((status: number | null) => {
        currentStatus.value = status
        pagination.value.page = 1
        loadOrders()
    }, 300)

    // 搜索订单 - 节流
    const searchOrders = throttle(() => {
        pagination.value.page = 1
        loadOrders()
    }, 500)

    // 清空搜索条件 - 防抖
    const clearSearchConditions = debounce(() => {
        searchKeyword.value = ''
        pagination.value.page = 1
        loadOrders()
    }, 200)

    // 处理日期变更
    const handleDateChange = () => {
        currentQuickRange.value = ''
        pagination.value.page = 1
        loadOrders()
    }

    // 设置快捷时间范围
    const setQuickDateRange = (rangeKey: string) => {
        const today = new Date()
        const formatDate = (date: Date) => {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        let startDateObj: Date

        switch (rangeKey) {
            case '7days':
                startDateObj = new Date()
                startDateObj.setDate(today.getDate() - 7)
                break
            case '15days':
                startDateObj = new Date()
                startDateObj.setDate(today.getDate() - 15)
                break
            case '30days':
                startDateObj = new Date()
                startDateObj.setDate(today.getDate() - 30)
                break
            default:
                return
        }

        startDate.value = formatDate(startDateObj)
        endDate.value = formatDate(today)
        currentQuickRange.value = rangeKey

        pagination.value.page = 1
        loadOrders()
    }

    // 分页处理
    const changePage = (page: number) => {
        pagination.value.page = page
        loadOrders()
    }

    // 获取订单商品数据
    const getOrderProducts = (orderNo: string) => {
        const orderData = orderProductsData.value.get(orderNo)
        return orderData?.items || orderData?.orderItems || orderData?.products || []
    }

    // 获取扁平化的订单商品列表（按productId分组，但保持个别SKU行）
    const getGroupedOrderProducts = (orderNo: string) => {
        const products = getOrderProducts(orderNo)
        if (!products || products.length === 0) return []

        // 按productId分组，但保持扁平结构
        const productGroups = new Map<string, any[]>()
        
        for (const product of products) {
            const productId = product.productId || product.platformProductId || ''
            const productName = product.productName || product.name || ''
            const groupKey = `${productId}-${productName}`
            
            if (!productGroups.has(groupKey)) {
                productGroups.set(groupKey, [])
            }
            
            productGroups.get(groupKey)!.push(product)
        }
        
        // 创建扁平列表，标记每个项目是否为组内第一个
        const flatList: any[] = []
        
        for (const [groupKey, groupProducts] of productGroups) {
            const [productId, productName] = groupKey.split('-', 2)
            
            groupProducts.forEach((product, index) => {
                flatList.push({
                    ...product,
                    // 标记组信息
                    isFirstInGroup: index === 0,
                    groupProductId: productId,
                    groupProductName: productName,
                    groupSize: groupProducts.length,
                    groupIndex: index
                })
            })
        }
        
        return flatList
    }

    // 初始化默认时间范围
    const initializeDefaultDateRange = () => {
        const today = new Date()
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(today.getDate() - 7)

        // 格式化日期为 YYYY-MM-DD
        const formatDate = (date: Date) => {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        // 设置默认7天时间范围
        startDate.value = formatDate(sevenDaysAgo)
        endDate.value = formatDate(today)
    }

    // 监听订单和状态变化，清空选项 - 优化性能
    watch([orders, currentStatus], () => {
        selectedOrders.value = []
    }, { flush: 'post' })

    return {
        // 响应式状态
        loading,
        loadingTime,
        orders,
        searchKeyword,
        searchType,
        currentStatus,
        selectedOrders,
        startDate,
        endDate,
        currentQuickRange,
        orderProductsData,
        expandedOrders,
        pagination,
        performanceMetrics,

        // 计算属性
        quickDateRanges,
        statusFilters,
        isAnyOrderSelected,

        // 方法
        getSearchPlaceholder,
        loadOrders,
        setStatusFilter,
        searchOrders,
        clearSearchConditions,
        handleDateChange,
        setQuickDateRange,
        changePage,
        getOrderProducts,
        getGroupedOrderProducts,
        initializeDefaultDateRange,
    }
}