<template>
    <div class="order-table-container">
        <div v-for="order in orders" :key="order.orderNo" class="order-section">
            <!-- 订单头部信息 -->
            <div class="order-header">
                <div class="order-header-left" @click="viewOrderDetail(order.orderNo)" style="cursor: pointer">
                    <!-- 选择框 -->
                    <el-checkbox v-if="order.canCancel && Number(order.orderStatus) !== 11"
                        :model-value="selectedOrderNos.includes(order.orderNo)"
                        @change="(checked: boolean) => handleSingleOrderSelection(order.orderNo, checked)" @click.stop
                        class="order-checkbox" />
                    <div class="order-info">
                        <span class="order-label">{{ t('orders.orderNo', '订单号') }}：</span>
                        <span class="order-number clickable-order-number"
                            :title="t('orders.clickToViewDetail', '点击查看订单详情')">
                            {{ order.orderNo }}
                        </span>
                        <!-- 复制订单号图标 -->
                        <button class="copy-order-btn" @click.stop="copyOrderNo(order.orderNo)"
                            :title="t('orders.copyOrderHint', '点击复制订单号')">
                            <svg class="copy-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="order-header-center">
                    <span class="order-label">{{ t('orders.orderTime', '下单时间') }}：</span>
                    <span class="order-time">{{ formatDate(order.createTime) }}</span>
                </div>
                <div class="order-header-right">
                    <!-- 订单操作下拉菜单 -->
                    <el-dropdown @command="(action: string) => handleOrderAction(action, order.orderNo)"
                        placement="bottom-end">
                        <el-button type="primary" size="small">
                            {{ t('orders.actions.title', '操作') }} <el-icon>
                                <ArrowDown />
                            </el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="pay" v-if="order.canPay">
                                    <el-icon>
                                        <CreditCard />
                                    </el-icon>
                                    {{ t('orders.trackOrders.pay', '立即支付') }}
                                </el-dropdown-item>
                                <el-dropdown-item command="cancel" v-if="order.canCancel">
                                    <el-icon>
                                        <Close />
                                    </el-icon>
                                    {{ t('orders.actions.cancel', '取消订单') }}
                                </el-dropdown-item>
                                <el-dropdown-item command="refund" v-if="order.orderStatus === 2">
                                    <el-icon>
                                        <RefreshLeft />
                                    </el-icon>
                                    {{ t('orders.actions.refund', '申请退款') }}
                                </el-dropdown-item>
                                <el-dropdown-item command="reorder" v-if="order.orderStatus === 13">
                                    <el-icon>
                                        <Refresh />
                                    </el-icon>
                                    {{ t('orders.actions.reorder', '再次购买') }}
                                </el-dropdown-item>
                                <el-dropdown-item command="detail" divided>
                                    <el-icon>
                                        <View />
                                    </el-icon>
                                    {{ t('orders.actions.viewDetail', '查看详情') }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>

            <!-- 商品表格 -->
            <el-table :data="getProcessedOrderProducts(order)" :span-method="getSpanMethod(order)" stripe border
                class="order-products-table">
                <!-- 产品名称 -->
                <el-table-column :label="t('orders.tableHeaders.productName', '产品名称')" min-width="200">
                    <template #default="scope">
                        <div class="product-info-cell">
                            <div class="product-name clickable-product-name" @click="viewProduct(scope.row.productId)"
                                :title="t('orders.tableHeaders.viewProduct', '查看产品')">
                                {{ getProductName(scope.row) }}
                            </div>
                            <div class="product-id-row">
                                <span class="product-id-label">ID: </span>
                                <span class="product-id-value">{{ scope.row.productId }}</span>
                                <button class="copy-product-id-btn" @click="copyProductId(scope.row.productId)"
                                    :title="t('orders.copyProductIdHint', '点击复制产品ID')">
                                    <svg class="copy-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 产品图片 -->
                <el-table-column :label="t('orders.tableHeaders.productImage', '产品图片')" width="150" align="center">
                    <template #default="scope">
                        <div class="product-image">
                            <img :src="scope.row.productImage || '/default-product.png'"
                                :alt="getProductName(scope.row)" @error="handleImageError"
                                @mouseenter="(e) => handleImageHover(e, true)"
                                @mouseleave="(e) => handleImageHover(e, false)" />
                        </div>
                    </template>
                </el-table-column>

                <!-- SKU -->
                <el-table-column :label="t('orders.tableHeaders.sku', 'SKU')" min-width="150">
                    <template #default="scope">
                        <div class="sku-id-cell">
                            <span class="sku-id">{{ scope.row.skuId }}</span>
                            <button class="copy-sku-btn" @click="copySkuId(scope.row.skuId)"
                                :title="t('orders.copySkuHint', '点击复制SKU')">
                                <svg class="copy-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                </el-table-column>

                <!-- 规格 -->
                <el-table-column :label="t('orders.tableHeaders.specification', '规格')" min-width="200">
                    <template #default="scope">
                        <div class="product-specs" v-if="scope.row.skuSpecs">
                            {{ getProductSpecs(scope.row.skuSpecs) }}
                        </div>
                    </template>
                </el-table-column>

                <!-- 数量 -->
                <el-table-column :label="t('orders.tableHeaders.quantity', '数量')" width="90" align="center">
                    <template #default="scope">
                        <span class="quantity">{{ scope.row.orderedQuantity }}</span>
                    </template>
                </el-table-column>

                <!-- 单价 -->
                <el-table-column :label="t('orders.tableHeaders.unitPrice', '单价')" min-width="100" align="right">
                    <template #default="scope">
                        <span class="unit-price">{{ getProductUnitPrice(scope.row) }}</span>
                    </template>
                </el-table-column>

                <!-- 小计 -->
                <el-table-column :label="t('orders.tableHeaders.subTotal', '小计')" min-width="90" align="right">
                    <template #default="scope">
                        <span class="line-total">{{ formatPriceWithSymbol(getProductTotalPrice(scope.row)) }}</span>
                    </template>
                </el-table-column>

                <!-- 订单项状态 -->
                <el-table-column :label="t('orders.tableHeaders.orderItemStatus', '订单项状态')" min-width="130"
                    align="center">
                    <template #default="scope">
                        <div class="status-cell">
                            <el-tag :type="getItemStatusTagType(scope.row.itemStatus)" size="small">
                                {{ getOrderItemStatusText(scope.row) }}
                            </el-tag>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 订单汇总信息 -->
            <div class="order-summary">
                <div class="summary-row">
                    <!-- 采购订单状态信息 -->
                    <span class="info-item">
                        <span class="info-label">{{ t('orders.purchaseOrder.status', '采购订单状态') }}:</span>
                        <el-tag :type="getPurchaseOrderStatusTagType(order.orderStatus)" size="small">
                            {{ getPurchaseOrderStatusText(order.orderStatus) }}
                        </el-tag>
                    </span>
                    <span class="info-separator">|</span>
                    <span class="info-item">
                        <span class="info-label">{{ t('orders.purchaseOrder.createTime', '创建时间') }}:</span>
                        <span class="info-value">{{ formatDate(order.createTime)
                            }}</span>
                    </span>
                    <span class="info-separator">|</span>
                    <span class="info-item">
                        <span class="info-label">{{ t('orders.purchaseOrder.updateTime', '更新时间') }}:</span>
                        <span class="info-value">{{ formatDate(order.updateTime)
                            }}</span>
                    </span>
                    <span class="info-separator">|</span>
                    <!-- 费用信息 -->
                    <span class="cost-item">
                        <span class="cost-label">{{ t('orders.cost.productAmount', '商品金额') }}:</span>
                        <span class="cost-value">{{ formatPriceWithSymbol(getProductAmount(order)) }}</span>
                    </span>
                    <span class="info-separator">|</span>
                    <span class="cost-item">
                        <span class="cost-label">{{ t('orders.cost.shippingFee', '运费') }}:</span>
                        <span class="cost-value">{{ formatPriceWithSymbol(getShippingFee(order)) }}</span>
                    </span>
                    <span class="info-separator">|</span>
                    <span class="cost-item">
                        <span class="cost-label">{{ t('orders.cost.serviceFee', '服务费') }}:</span>
                        <span class="cost-value">{{ formatPriceWithSymbol(getServiceFee(order)) }}</span>
                    </span>
                    <span class="info-separator">|</span>
                    <span class="cost-item total-item">
                        <span class="cost-label">{{ t('orders.cost.orderTotal', '订单总额') }}:</span>
                        <span class="cost-value total-price">{{ formatPriceWithSymbol(getCurrentOrderAmount(order))
                            }}</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { OrderItemInfo, UserPurchaseOrderListVO } from '@/api/modules/order'
import { ArrowDown, Close, CreditCard, Refresh, RefreshLeft, View } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

interface Props {
    orders: UserPurchaseOrderListVO[]
    selectedOrderNos: string[]
    getOrderProducts: (orderNo: string) => any[]
    getProductName: (product: any) => string
    getProductSpecs: (specs: any) => string
    getProductUnitPrice: (product: any) => string
    getProductTotalPrice: (product: any) => number
    getOrderItemStatusText: (product: any) => string
    getItemStatusTagType: (status: string) => string
    formatDate: (date: string) => string
    formatPriceWithSymbol: (price: number) => string
    getCurrentOrderAmount: (order: any) => number
    getProductAmount: (order: any) => number
    getShippingFee: (order: any) => number
    getServiceFee: (order: any) => number
    getPurchaseOrderStatusText: (status: string | number) => string
    getPurchaseOrderStatusTagType: (status: string | number) => 'success' | 'warning' | 'info' | 'primary' | 'danger'
}

interface Emits {
    (e: 'single-order-selection', orderNo: string, checked: boolean): void
    (e: 'view-order-detail', orderNo: string): void
    (e: 'copy-order-no', orderNo: string): void
    (e: 'copy-sku-id', skuId: string): void
    (e: 'copy-product-id', productId: number): void
    (e: 'view-product', productId: number): void
    (e: 'image-error', event: Event): void
    (e: 'image-hover', event: Event, show: boolean): void
    (e: 'order-action', action: string, orderNo: string): void
}

const { t } = useI18n()
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 产品行合并数据预处理
interface ProcessedProduct extends OrderItemInfo {
    productRowSpan?: number
    isFirstProductRow?: boolean
}

// 处理订单商品数据，计算行合并
const getProcessedOrderProducts = (order: UserPurchaseOrderListVO) => {
    const products = props.getOrderProducts(order.orderNo) || []
    const processedProducts: ProcessedProduct[] = []

    // 按产品ID分组
    const productGroups = new Map<number, ProcessedProduct[]>()

    products.forEach((product: OrderItemInfo) => {
        const productId = product.productId
        if (!productGroups.has(productId)) {
            productGroups.set(productId, [])
        }
        productGroups.get(productId)!.push({ ...product })
    })

    // 处理每个产品组
    productGroups.forEach((group) => {
        group.forEach((product, index) => {
            if (index === 0) {
                // 第一行显示产品信息并设置行合并数量
                product.productRowSpan = group.length
                product.isFirstProductRow = true
            } else {
                // 其他行不显示产品信息
                product.productRowSpan = 0
                product.isFirstProductRow = false
            }
        })
        processedProducts.push(...group)
    })

    return processedProducts
}

// 表格行合并方法
const getSpanMethod = (order: UserPurchaseOrderListVO) => {
    return ({ row, column, rowIndex, columnIndex }: any) => {
        // 产品ID/名称列需要合并
        if (columnIndex === 0) {
            if (row.productRowSpan === 0) {
                return { rowspan: 0, colspan: 0 }
            } else {
                return { rowspan: row.productRowSpan || 1, colspan: 1 }
            }
        }
        return { rowspan: 1, colspan: 1 }
    }
}

// 事件处理
const handleSingleOrderSelection = (orderNo: string, checked: boolean) => {
    emit('single-order-selection', orderNo, checked)
}

const viewOrderDetail = (orderNo: string) => {
    emit('view-order-detail', orderNo)
}

const copyOrderNo = (orderNo: string) => {
    emit('copy-order-no', orderNo)
}

const copySkuId = (skuId: string) => {
    emit('copy-sku-id', skuId)
}

const handleImageError = (event: Event) => {
    emit('image-error', event)
}

const handleImageHover = (event: Event, show: boolean) => {
    emit('image-hover', event, show)
}

const handleOrderAction = (action: string, orderNo: string) => {
    if (action === 'detail') {
        viewOrderDetail(orderNo)
    } else {
        emit('order-action', action, orderNo)
    }
}

const copyProductId = (productId: number) => {
    emit('copy-product-id', productId)
}

const viewProduct = (productId: number) => {
    // 在后台打开新标签页，不立即跳转
    const newTab = window.open(`/products/${productId}`, '_blank', 'noopener,noreferrer')
    if (newTab) {
        newTab.blur() // 让新标签页在后台打开
        window.focus() // 保持当前页面焦点
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/orders.scss';

.order-table-container {
    display: flex;
    flex-direction: column;
    gap: $order-spacing-lg;
}

.order-section {
    @include order-card;
    padding: $order-spacing-lg;

    &:hover {
        box-shadow: $order-shadow-medium;
    }
}

// 订单头部样式
.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $order-spacing-lg;
    padding-bottom: $order-spacing-md;
    border-bottom: 1px solid #f3f4f6;

    .order-header-left {
        display: flex;
        align-items: center;
        gap: $order-spacing-md;
        cursor: pointer;

        .order-info {
            display: flex;
            align-items: center;
            gap: $order-spacing-sm;
        }

        .order-label {
            font-size: $order-small-size;
            color: $order-info-color;
            font-weight: 800;
        }

        .order-number {
            font-size: $order-subtitle-size;
            color: $order-primary-color;
            font-weight: 700;
            text-decoration: underline;
            text-decoration-color: transparent;
            transition: all 0.2s ease;

            &:hover {
                color: $order-primary-hover;
                text-decoration-color: $order-primary-hover;
            }
        }

        .copy-order-btn {
            @include order-icon-btn;
            width: 1.25rem;
            height: 1.25rem;

            .copy-icon {
                width: 0.75rem;
                height: 0.75rem;
            }
        }
    }

    .order-header-center {
        display: flex;
        align-items: center;
        gap: $order-spacing-sm;

        .order-label {
            font-size: $order-small-size;
            color: $order-info-color;
            font-weight: 800;
        }

        .order-time {
            font-size: $order-text-size;
            color: #111827;
            font-weight: 500;
        }
    }

    .order-header-right {
        flex-shrink: 0;
    }
}

// 表格样式
.order-products-table {
    margin-bottom: $order-spacing-lg;
    border-radius: $order-radius-md;
    overflow: hidden;


    :deep(.el-table__header) {
        th {
            text-align: center;
            background-color: #52018d !important;
            color: #ffffff;
            font-weight: 700;
            font-size: $order-small-size;
            padding: $order-spacing-md $order-spacing-sm;
            border-bottom: 2px solid #3d0066;
            border-left: none;
            border-right: none;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

            .cell {
                color: #ffffff !important;
                font-weight: 700;
            }

            &:first-child {
                border-left: none;
            }

            &:last-child {
                border-right: none;
            }
        }
    }

    :deep(.el-table__body-wrapper) {
        .el-table__row {

            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f9fafb;
            }

            .el-table__cell {
                text-align: center;
                vertical-align: middle;
                padding: $order-spacing-md $order-spacing-sm;
                border-bottom: 1px solid #f1f5f9;
            }
        }
    }

    .product-info-cell {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 80px;
        padding: $order-spacing-sm;

        .product-name {
            font-size: $order-text-size;
            font-weight: 600;
            color: #111827;
            margin-bottom: $order-spacing-xs;
            line-height: 1.4;
            text-align: center;
            word-wrap: break-word;
            white-space: normal;

            &.clickable-product-name {
                cursor: pointer;
                color: #6B46C1;
                transition: all 0.2s ease;

                &:hover {
                    color: #8B5CF6;
                    text-decoration: underline;
                }
            }
        }

        .product-id-row {
            display: flex;
            align-items: center;
            gap: $order-spacing-xs;
            justify-content: center;

            .product-id-label {
                font-size: $order-tiny-size;
                color: #000000;
                font-family: monospace;
                font-weight: 700;
            }

            .product-id-value {
                font-size: $order-tiny-size;
                color: #000000;
                font-family: monospace;
                font-weight: 700;
            }

            .copy-product-id-btn {
                @include order-icon-btn;
                width: 1rem;
                height: 1rem;

                .copy-icon {
                    width: 0.625rem;
                    height: 0.625rem;
                }
            }
        }
    }

    .product-image {
        width: 100px;
        height: 100px;
        border-radius: $order-radius-sm;
        overflow: hidden;
        border: 1px solid #e5e7eb;
        margin: 0 auto;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.2s ease;
            cursor: pointer;

            &:hover {
                transform: scale(1.05);
            }
        }
    }

    .sku-id-cell {
        display: flex;
        align-items: center;
        gap: $order-spacing-xs;

        .sku-id {
            font-size: $order-text-size;
            color: #000000;
            font-family: monospace;
            font-weight: 600;
        }

        .copy-sku-btn {
            @include order-icon-btn;
            width: 1rem;
            height: 1rem;

            .copy-icon {
                width: 0.625rem;
                height: 0.625rem;
            }
        }
    }

    .product-specs {
        font-size: $order-small-size;
        color: #000000;
        line-height: 1.4;
    }

    .quantity {
        font-size: $order-text-size;
        color: #000000;
        font-weight: 600;
    }

    .unit-price {
        font-size: $order-small-size;
        color: #000000;
        font-weight: 600;
    }

    .line-total {
        font-size: $order-text-size;
        font-weight: 800;
        color: #ea580c;
    }

    .status-cell {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 32px;

        :deep(.el-tag) {
            border-radius: 6px;
            font-weight: 500;
            padding: 4px 8px;
            font-size: 12px;

            &.el-tag--success {
                background-color: #10B981;
                color: #ffffff;
                border-color: #059669;
            }

            &.el-tag--warning {
                background-color: #F59E0B;
                color: #ffffff;
                border-color: #D97706;
            }

            &.el-tag--primary {
                background-color: #6B46C1;
                color: #ffffff;
                border-color: #5B21B6;
            }

            &.el-tag--info {
                background-color: #6B7280;
                color: #ffffff;
                border-color: #4B5563;
            }

            &.el-tag--danger {
                background-color: #EF4444;
                color: #ffffff;
                border-color: #DC2626;
            }
        }
    }
}

// 订单汇总样式
.order-summary {
    @include order-panel;
    margin-top: $order-spacing-md;

    .summary-row {
        display: flex;
        align-items: center;
        gap: $order-spacing-md;
        flex-wrap: wrap;
        font-size: $order-small-size;

        .info-item,
        .cost-item {
            display: flex;
            align-items: center;
            gap: $order-spacing-xs;
            white-space: nowrap;

            .info-label,
            .cost-label {
                color: $order-info-color;
                font-weight: 600;
            }

            .info-value,
            .cost-value {
                // color: #F59E0B;
                font-weight: 700;
            }

            &.total-item {
                .cost-label {
                    font-weight: 700;
                    color: #374151;
                }

                .total-price {
                    color: #DC2626;
                    font-weight: 800;
                    font-size: $order-text-size;
                }
            }
        }

        .cost-item .cost-value {
            color: #ea580c;
            font-weight: 800;
        }

        .info-separator {
            color: #d1d5db;
            font-weight: 300;
            margin: 0 $order-spacing-xs;
        }
    }
}

// 复选框样式
:deep(.el-checkbox) {
    @include order-checkbox;
}

// 响应式设计
@include order-mobile {
    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: $order-spacing-md;

        .order-header-left,
        .order-header-center,
        .order-header-right {
            width: 100%;
        }

        .order-header-center {
            justify-content: flex-start;
        }

        .order-header-right {
            justify-content: flex-end;
        }
    }

    .order-products-table {
        :deep(.el-table__body-wrapper) {
            overflow-x: auto;
        }
    }

    .order-summary .summary-row {
        flex-direction: column;
        align-items: flex-start;
        gap: $order-spacing-sm;

        .info-item,
        .cost-item {
            justify-content: space-between;
            width: 100%;
        }

        .info-separator {
            display: none;
        }
    }
}
</style>