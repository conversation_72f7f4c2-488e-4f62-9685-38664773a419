<template>
    <div class="order-filters">
        <!-- 批量操作区域 -->
        <div v-if="isAnyOrderSelected" class="batch-operations">
            <div class="batch-info">
                <div class="selected-badge">
                    <svg class="selected-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="selected-count">{{ t('orders.batchOperations.selectedCount', { count: selectedCount }) || `已选择 ${selectedCount} 个订单` }}</span>
                </div>
            </div>
            <div class="batch-actions">
                <el-button 
                    type="danger" 
                    size="small" 
                    @click="handleBatchCancel"
                    :loading="batchLoading"
                    class="batch-btn batch-cancel"
                >
                    <template #icon>
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </template>
                    {{ t('orders.actions.batchCancel', '批量取消') }}
                </el-button>
                <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleBatchPay"
                    :loading="batchLoading"
                    class="batch-btn batch-pay"
                >
                    <template #icon>
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </template>
                    {{ t('orders.batchOperations.batchPay', '批量支付') }}
                </el-button>
                <el-button 
                    size="small" 
                    @click="handleBatchExport"
                    :loading="batchLoading"
                    class="batch-btn batch-export"
                >
                    <template #icon>
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </template>
                    {{ t('orders.batchOperations.batchExport', '批量导出') }}
                </el-button>
            </div>
        </div>

        <!-- 筛选条件摘要 -->
        <div v-if="hasActiveFilters" class="filter-summary">
            <div class="summary-header">
                <svg class="summary-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <span class="summary-title">{{ t('orders.filters.currentConditions', '当前筛选条件') }}</span>
                <el-button 
                    @click="handleClearAllFilters" 
                    size="small" 
                    type="danger" 
                    plain
                    class="clear-all-btn"
                >
                    {{ t('orders.filters.clearAll', '清空全部') }}
                </el-button>
            </div>
            <div class="summary-tags">
                <el-tag 
                    v-if="searchKeyword" 
                    closable 
                    @close="clearSearchKeyword"
                    class="filter-tag search-tag"
                >
                    {{ t('orders.filters.search', '搜索') }}: {{ searchKeyword }}
                </el-tag>
                <el-tag 
                    v-if="currentStatus !== null" 
                    closable 
                    @close="clearStatus"
                    class="filter-tag status-tag"
                >
                    {{ t('orders.filters.status', '状态') }}: {{ getStatusLabel(currentStatus) }}
                </el-tag>
                <el-tag 
                    v-if="currentQuickRange" 
                    closable 
                    @close="clearTimeRange"
                    class="filter-tag time-tag"
                >
                    {{ t('orders.filters.time', '时间') }}: {{ getTimeRangeLabel(currentQuickRange) }}
                </el-tag>
                <el-tag 
                    v-if="startDate && endDate && !currentQuickRange" 
                    closable 
                    @close="clearCustomDate"
                    class="filter-tag date-tag"
                >
                    {{ t('orders.filters.custom', '自定义') }}: {{ startDate }} {{ t('orders.dateRange.separator', '至') }} {{ endDate }}
                </el-tag>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filters-section" :class="{ 'collapsed': isCollapsed }">
            <!-- 折叠/展开按钮 -->
            <div class="collapse-toggle" @click="toggleCollapse">
                <svg class="collapse-icon" :class="{ 'rotated': isCollapsed }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
                <span class="collapse-text">{{ isCollapsed ? t('orders.filters.expand', '展开筛选') : t('orders.filters.collapse', '收起筛选') }}</span>
            </div>

            <!-- 筛选内容 -->
            <div class="filters-content" v-show="!isCollapsed">
                <!-- 第一行：搜索和状态筛选 -->
                <div class="filters-row-primary">
                    <!-- 搜索组 - 使用 demo-filters.html 的样式 -->
                    <div class="search-group">
                        <div class="search-container">
                            <el-select
                                :model-value="searchType"
                                @update:model-value="(value: string) => emit('update:searchType', value)"
                                class="search-type-select"
                                :placeholder="t('orders.search.searchType.default', '搜索类型')"
                            >
                                <el-option :label="t('orders.search.searchType.default', '智能搜索')" value="default" />
                                <el-option :label="t('orders.search.searchType.purchaseOrder', '订单号')" value="orderNo" />
                                <el-option :label="t('orders.search.placeholders.default', '商品名称')" value="productName" />
                                <el-option :label="t('orders.search.searchType.skuId', 'SKU ID')" value="skuId" />
                            </el-select>
                            
                            <el-autocomplete
                                :model-value="searchKeyword"
                                @update:model-value="(value: string) => emit('update:searchKeyword', value)"
                                :fetch-suggestions="fetchSearchSuggestions"
                                :placeholder="t('orders.search.placeholder', '请输入搜索内容，支持订单号、商品名称、SKU ID等')"
                                class="search-input"
                                clearable
                                @keyup.enter="handleSearch"
                                @select="handleSuggestionSelect"
                                :debounce="300"
                            >
                                <template #default="{ item }">
                                    <div class="suggestion-item">
                                        <svg class="suggestion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span class="suggestion-text">{{ item.value }}</span>
                                        <span class="suggestion-type">{{ item.type }}</span>
                                    </div>
                                </template>
                            </el-autocomplete>
                            
                            <button class="search-btn" @click="handleSearch">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </button>
                            
                            <!-- <el-dropdown @command="handleQuickAction" class="quick-actions-dropdown">
                                <button class="quick-actions-btn">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                    </svg>
                                </button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="clear-search">
                                            <svg class="dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                            {{ t('orders.search.clear', '清空搜索') }}
                                        </el-dropdown-item>
                                        <el-dropdown-item command="save-filter">
                                            <svg class="dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                            </svg>
                                            {{ t('orders.filters.saveConditions', '保存筛选条件') }}
                                        </el-dropdown-item>
                                        <el-dropdown-item command="load-filter">
                                            <svg class="dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                            </svg>
                                            {{ t('orders.filters.loadConditions', '加载筛选条件') }}
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown> -->
                        </div>
                    </div>

                    <!-- 状态筛选 - 与搜索框保持同一高度 -->
                    <div class="status-group">
                        <el-select
                            :model-value="currentStatus"
                            @update:model-value="(value: number | null) => { emit('update:currentStatus', value); handleStatusChange(value); }"
                            :placeholder="t('orders.status.label', '订单状态')"
                            clearable
                            class="status-select" 
                            filterable
                        >
                            <el-option :label="t('orders.status.all', '全部状态')" :value="null" />
                            <el-option :label="t('orders.itemStatus.temporarilySaved', '临时保存')" :value="0" />
                            <el-option :label="t('orders.itemStatus.paymentPending', '待支付')" :value="1" />
                            <el-option :label="t('orders.itemStatus.paymentCompleted', '支付完成')" :value="2" />
                            <el-option :label="t('orders.itemStatus.pendingReview', '待审核')" :value="3" />
                            <el-option :label="t('orders.itemStatus.procurementInProgress', '采购中')" :value="4" />
                            <el-option :label="t('orders.itemStatus.partiallyProcurement', '部分履约')" :value="5" />
                            <el-option :label="t('orders.itemStatus.procurementCompleted', '采购完成')" :value="6" />
                            <el-option :label="t('orders.itemStatus.supplierShipped', '供应商已发货')" :value="7" />
                            <el-option :label="t('orders.itemStatus.warehousePendingReceived', '仓库待收货')" :value="8" />
                            <el-option :label="t('orders.itemStatus.warehouseReceived', '仓库已收货')" :value="9" />
                            <el-option :label="t('orders.itemStatus.inStock', '已入库')" :value="10" />
                            <el-option :label="t('orders.itemStatus.cancelled', '已取消')" :value="11" />
                            <el-option :label="t('orders.itemStatus.paymentPendingForPartial', '代付款')" :value="12" />
                            <el-option :label="t('orders.itemStatus.completed', '已完成')" :value="13" />
                            <el-option :label="t('orders.itemStatus.refunding', '退款中')" :value="14" />
                            <el-option :label="t('orders.itemStatus.refunded', '已退款')" :value="15" />
                        </el-select>
                    </div>
                </div>

                <!-- 第二行：时间筛选 - 重新设计排版 -->
                <div class="filters-row-secondary">
                    <div class="time-filters-container">
                        <!-- 快捷时间选择器 -->
                        <div class="quick-time-section">
                            <div class="time-section-header">
                                <svg class="time-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span class="time-section-title">{{ t('orders.timeRange', '时间筛选') }}</span>
                            </div>
                            <div class="quick-time-buttons">
                                <button
                                    v-for="range in enhancedQuickDateRanges"
                                    :key="range.key"
                                    :class="['time-btn', { 'active': currentQuickRange === range.key }]"
                                    @click="handleRangeChange(range.key)"
                                    :title="range.description"
                                >
                                    {{ range.label }}
                                </button>
                            </div>
                        </div>
                        
                        <!-- 自定义日期范围 -->
                        <div class="custom-date-section">
                            <div class="date-range-container">
                                <el-date-picker
                                    :model-value="startDate"
                                    @update:model-value="(value: string | null) => { emit('update:startDate', value); handleDateChange(); }"
                                    type="date"
                                    :placeholder="t('orders.dateRange.start', '开始日期')"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    class="date-input"
                                    size="default"
                                    :shortcuts="dateShortcuts"
                                />
                                <span class="date-separator">{{ t('orders.dateRange.separator', '至') }}</span>
                                <el-date-picker
                                    :model-value="endDate"
                                    @update:model-value="(value: string | null) => { emit('update:endDate', value); handleDateChange(); }"
                                    type="date"
                                    :placeholder="t('orders.dateRange.end', '结束日期')"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    class="date-input"
                                    size="default"
                                    :shortcuts="dateShortcuts"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Search, Refresh } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface Props {
    // 批量操作相关
    isAnyOrderSelected: boolean
    selectedCount: number
    batchLoading?: boolean
    
    // 筛选相关
    searchKeyword: string
    searchType: string
    currentStatus: number | null
    currentQuickRange: string
    quickDateRanges: Array<{ key: string; label: string }>
    startDate: string | null
    endDate: string | null
}

interface Emits {
    // 批量操作事件
    (e: 'batch-cancel'): void
    (e: 'batch-pay'): void
    (e: 'batch-export'): void
    
    // 筛选事件
    (e: 'update:searchKeyword', value: string): void
    (e: 'update:searchType', value: string): void
    (e: 'update:currentStatus', value: number | null): void
    (e: 'update:currentQuickRange', value: string): void
    (e: 'update:startDate', value: string | null): void
    (e: 'update:endDate', value: string | null): void
    (e: 'search'): void
    (e: 'clear-search'): void
    (e: 'status-change', value: number | null): void
    (e: 'range-change', key: string): void
    (e: 'date-change'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const isCollapsed = ref(false)
const searchHistory = ref<string[]>([])

// 计算属性
const hasActiveFilters = computed(() => {
    return !!(props.searchKeyword || props.currentStatus !== null || props.currentQuickRange || (props.startDate && props.endDate))
})

const enhancedQuickDateRanges = computed(() => [
    {
        key: '1day',
        label: t('orders.dateFilter.today', '今天'),
        description: t('orders.dateFilter.todayDesc', '今天的订单'),
        icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
    },
    {
        key: '3days',
        label: t('orders.dateFilter.last3days', '3天'),
        description: t('orders.dateFilter.last3daysDesc', '最近3天的订单'),
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    },
    {
        key: '7days',
        label: t('orders.dateFilter.last7days', '7天'),
        description: t('orders.dateFilter.last7daysDesc', '最近7天的订单'),
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    },
    {
        key: '15days',
        label: t('orders.dateFilter.last15days', '15天'),
        description: t('orders.dateFilter.last15daysDesc', '最近15天的订单'),
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    },
    {
        key: '30days',
        label: t('orders.dateFilter.last30days', '30天'),
        description: t('orders.dateFilter.last30daysDesc', '最近30天的订单'),
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    }
])

const dateShortcuts = computed(() => [
    {
        text: t('orders.dateFilter.today', '今天'),
        value: () => {
            const today = new Date()
            return [today, today]
        }
    },
    {
        text: t('orders.dateFilter.yesterday', '昨天'),
        value: () => {
            const yesterday = new Date()
            yesterday.setDate(yesterday.getDate() - 1)
            return [yesterday, yesterday]
        }
    },
    {
        text: t('orders.dateFilter.lastWeek', '最近一周'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setDate(start.getDate() - 7)
            return [start, end]
        }
    },
    {
        text: t('orders.dateFilter.lastMonth', '最近一个月'),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 1)
            return [start, end]
        }
    }
])

// 状态标签映射
const statusLabels: Record<number, string> = {
    0: '临时保存',
    1: '待支付',
    2: '支付完成',
    3: '待审核',
    4: '采购中',
    5: '部分履约',
    6: '采购完成',
    7: '供应商已发货',
    8: '仓库待收货',
    9: '仓库已收货',
    10: '已入库',
    11: '已取消',
    12: '代付款',
    13: '已完成',
    14: '退款中',
    15: '已退款'
}

// 时间范围标签映射 - 使用国际化
const timeRangeLabels = computed(() => ({
    '1day': t('orders.dateFilter.today', '今天'),
    '3days': t('orders.dateFilter.last3days', '最近3天'),
    '7days': t('orders.dateFilter.last7days', '最近7天'),
    '15days': t('orders.dateFilter.last15days', '最近15天'),
    '30days': t('orders.dateFilter.last30days', '最近30天')
}))

// 方法
const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value
}

const getStatusLabel = (status: number) => {
    return statusLabels[status] || '未知状态'
}

const getTimeRangeLabel = (range: string) => {
    return timeRangeLabels.value[range as keyof typeof timeRangeLabels.value] || range
}

// 清空筛选条件
const clearSearchKeyword = () => {
    emit('update:searchKeyword', '')
    emit('search')
}

const clearStatus = () => {
    emit('update:currentStatus', null)
    emit('status-change', null)
}

const clearTimeRange = () => {
    emit('update:currentQuickRange', '')
    emit('range-change', '')
}

const clearCustomDate = () => {
    emit('update:startDate', null)
    emit('update:endDate', null)
    emit('date-change')
}

const handleClearAllFilters = () => {
    emit('update:searchKeyword', '')
    emit('update:searchType', 'default')
    emit('update:currentStatus', null)
    emit('update:currentQuickRange', '')
    emit('update:startDate', null)
    emit('update:endDate', null)
    emit('search')
}

// 搜索建议功能
const fetchSearchSuggestions = (queryString: string, cb: (suggestions: any[]) => void) => {
    const suggestions: any[] = []
    
    // 基于搜索历史的建议
    if (searchHistory.value.length > 0) {
        const historyMatches = searchHistory.value
            .filter(item => item.toLowerCase().includes(queryString.toLowerCase()))
            .slice(0, 3)
            .map(item => ({
                value: item,
                type: '历史记录'
            }))
        suggestions.push(...historyMatches)
    }
    
    // 根据搜索类型提供不同建议
    if (queryString.length >= 2) {
        switch (props.searchType) {
            case 'orderNo':
                suggestions.push({
                    value: `ORD${queryString.toUpperCase()}`,
                    type: '订单号'
                })
                break
            case 'productName':
                const productSuggestions = [
                    'iPhone 15', 'iPhone 15 Pro', 'MacBook Air', 'iPad Pro'
                ].filter(item => item.toLowerCase().includes(queryString.toLowerCase()))
                    .map(item => ({ value: item, type: '商品名称' }))
                suggestions.push(...productSuggestions)
                break
            case 'skuId':
                suggestions.push({
                    value: `SKU${queryString.toUpperCase()}`,
                    type: 'SKU编号'
                })
                break
            default:
                // 智能搜索建议
                const smartSuggestions = [
                    { value: `订单号: ORD${queryString}`, type: '智能匹配' },
                    { value: `商品: ${queryString}`, type: '智能匹配' }
                ]
                suggestions.push(...smartSuggestions.slice(0, 2))
        }
    }
    
    cb(suggestions.slice(0, 8)) // 最多显示8个建议
}

// 搜索建议选择处理
const handleSuggestionSelect = (item: any) => {
    emit('update:searchKeyword', item.value)
    if (!searchHistory.value.includes(item.value)) {
        searchHistory.value.unshift(item.value)
        if (searchHistory.value.length > 20) {
            searchHistory.value = searchHistory.value.slice(0, 20)
        }
    }
    handleSearch()
}

// 快捷操作处理
const handleQuickAction = (command: string) => {
    switch (command) {
        case 'clear-search':
            clearSearchKeyword()
            break
        case 'save-filter':
            saveCurrentFilter()
            break
        case 'load-filter':
            loadSavedFilter()
            break
        default:
            console.log('未知操作:', command)
    }
}

// 保存筛选条件
const saveCurrentFilter = () => {
    const filterData = {
        searchKeyword: props.searchKeyword,
        searchType: props.searchType,
        currentStatus: props.currentStatus,
        currentQuickRange: props.currentQuickRange,
        startDate: props.startDate,
        endDate: props.endDate,
        timestamp: new Date().toISOString()
    }
    
    // 保存到本地存储
    const savedFilters = JSON.parse(localStorage.getItem('orderFilters') || '[]')
    savedFilters.unshift(filterData)
    if (savedFilters.length > 10) {
        savedFilters.splice(10) // 最多保存10个
    }
    localStorage.setItem('orderFilters', JSON.stringify(savedFilters))
    
    ElMessage.success('筛选条件已保存')
}

// 加载筛选条件
const loadSavedFilter = () => {
    try {
        const saved = localStorage.getItem('orderFilters')
        if (saved) {
            const filterData = JSON.parse(saved)
            emit('update:searchKeyword', filterData.searchKeyword || '')
            emit('update:searchType', filterData.searchType || 'default')
            emit('update:currentStatus', filterData.currentStatus)
            emit('update:currentQuickRange', filterData.currentQuickRange || '')
            emit('update:startDate', filterData.startDate)
            emit('update:endDate', filterData.endDate)
            ElMessage.success('筛选条件已加载')
            emit('search')
        } else {
            ElMessage.info('没有保存的筛选条件')
        }
    } catch (error) {
        ElMessage.error('加载筛选条件失败')
    }
}

// 批量操作
const handleBatchCancel = () => emit('batch-cancel')
const handleBatchPay = () => emit('batch-pay')
const handleBatchExport = () => emit('batch-export')

// 搜索操作
const handleSearch = () => {
    if (props.searchKeyword && !searchHistory.value.includes(props.searchKeyword)) {
        searchHistory.value.unshift(props.searchKeyword)
        if (searchHistory.value.length > 20) {
            searchHistory.value = searchHistory.value.slice(0, 20)
        }
    }
    emit('search')
}

const handleClearSearch = () => emit('clear-search')

// 状态筛选
const handleStatusChange = (status: number | null) => emit('status-change', status)

// 时间范围
const handleRangeChange = (key: string) => {
    emit('update:currentQuickRange', key)
    emit('range-change', key)
}

const handleDateChange = () => emit('date-change')
</script>

<style lang="scss" scoped>
/* 主容器样式 - 参考 demo-filters.html 设计 */
.order-filters {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);
    margin: 1.5rem 0;
    padding: 0;
    overflow: hidden;
    position: relative;

    // 顶部彩色渐变条纹
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #f97316, #ea580c, #dc2626, #7c3aed);
        opacity: 0.8;
    }
}

// 筛选条件摘要 - 增强设计
.filter-summary {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-bottom: 1px solid #bae6fd;
    position: relative;
    overflow: hidden;
    
    // 添加微妙的装饰效果
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #0ea5e9, #0284c7, #0369a1);
        opacity: 0.6;
    }
    
    .summary-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.75rem;
        
        .summary-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: #0284c7;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        .summary-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #0c4a6e;
            flex: 1;
            display: flex;
            align-items: center;
        }
        
        .clear-all-btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            height: auto;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
                background: #dc2626;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
            }
        }
    }
    
    .summary-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        
        .filter-tag {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            border: 1px solid;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            }
            
            &.search-tag {
                background: #fef3c7;
                color: #92400e;
                border-color: #fbbf24;
                
                &:hover {
                    background: #fde68a;
                    border-color: #f59e0b;
                }
            }
            
            &.status-tag {
                background: #dbeafe;
                color: #1e40af;
                border-color: #3b82f6;
                
                &:hover {
                    background: #bfdbfe;
                    border-color: #2563eb;
                }
            }
            
            &.time-tag {
                background: #dcfce7;
                color: #166534;
                border-color: #22c55e;
                
                &:hover {
                    background: #bbf7d0;
                    border-color: #16a34a;
                }
            }
            
            &.date-tag {
                background: #fce7f3;
                color: #be185d;
                border-color: #ec4899;
                
                &:hover {
                    background: #f9a8d4;
                    border-color: #db2777;
                }
            }
        }
    }
}

// 添加脉冲动画
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

// 批量操作区域
.batch-operations {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 50%, #e0f2fe 100%);
    border-bottom: 1px solid #bfdbfe;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(180deg, #3b82f6, #1d4ed8);
    }

    .batch-info {
        .selected-badge {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            backdrop-filter: blur(8px);

            .selected-icon {
                width: 1.25rem;
                height: 1.25rem;
                color: #3b82f6;
                stroke-width: 2.5;
            }

            .selected-count {
                color: #1e40af;
                font-weight: 700;
                font-size: 0.9rem;
                letter-spacing: 0.025em;
            }
        }
    }

    .batch-actions {
        display: flex;
        gap: 0.75rem;

        .batch-btn {
            padding: 0.625rem 1.25rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            svg {
                width: 1rem;
                height: 1rem;
                stroke-width: 2;
            }

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }

            &.batch-cancel {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

                &:hover {
                    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                }
            }

            &.batch-pay {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

                &:hover {
                    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                }
            }

            &.batch-export {
                background: linear-gradient(135deg, #64748b 0%, #475569 100%);
                color: white;

                &:hover {
                    background: linear-gradient(135deg, #475569 0%, #334155 100%);
                }
            }
        }
    }
}

// 筛选区域
.filters-section {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    
    &.collapsed {
        .filters-content {
            display: none;
        }
    }
    
    .collapse-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        
        // 添加悬停时的流光效果
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        &:hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-color: #f97316;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
            
            &::before {
                left: 100%;
            }
        }
        
        .collapse-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: #64748b;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            
            &.rotated {
                transform: rotate(180deg);
            }
        }
        
        .collapse-text {
            font-size: 0.875rem;
            font-weight: 500;
            color: #475569;
            transition: color 0.3s ease;
        }
        
        &:hover .collapse-text {
            color: #f97316;
        }
    }
}

.filters-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.filters-row-primary, .filters-row-secondary {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    flex-wrap: wrap;

    @media (max-width: 1200px) {
        flex-direction: column;
        align-items: stretch;
        gap: 1.25rem;
    }
}

.filters-row-primary {
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    padding-bottom: 1.25rem;
}

// 搜索组 - 使用 demo-filters.html 的样式
.search-group {
    flex: 2;
    min-width: 500px;

    @media (max-width: 1200px) {
        min-width: 100%;
    }

    .search-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid rgba(226, 232, 240, 0.6);
        border-radius: 1rem;
        padding: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), inset 0 1px 3px rgba(255, 255, 255, 0.8);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
            border-color: rgba(249, 115, 22, 0.4);
            box-shadow: 0 6px 16px rgba(249, 115, 22, 0.1), inset 0 1px 3px rgba(255, 255, 255, 0.8);
            transform: translateY(-1px);
        }

        &:focus-within {
            border-color: #f97316;
            box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1), 0 8px 20px rgba(249, 115, 22, 0.15);
            transform: translateY(-2px);
        }

        .search-type-select {
            flex-shrink: 0;
            width: 130px;
            
            :deep(.el-select__wrapper) {
                border: none;
                box-shadow: none;
                background: transparent;
                padding: 0.5rem;
                
                &.is-focus {
                    box-shadow: none;
                    border: none;
                }
                
                .el-input__inner {
                    font-size: 0.875rem;
                    color: #475569;
                    background: transparent;
                }
            }
        }

        .search-input {
            flex: 1;
            
            :deep(.el-autocomplete) {
                width: 100%;
                
                .el-input__wrapper {
                    border: none;
                    box-shadow: none;
                    background: transparent;
                    padding: 0.5rem 0.75rem;

                    .el-input__inner {
                        font-size: 0.9rem;
                        font-weight: 500;
                        color: #1e293b;
                        background: transparent;

                        &::placeholder {
                            color: #94a3b8;
                            font-weight: 400;
                        }
                    }

                    &.is-focus {
                        box-shadow: none;
                        border: none;
                    }
                }
            }
        }

        .search-btn {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            border: none;
            border-radius: 0.5rem;
            width: 4rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(249, 115, 22, 0.3);
            transition: all 0.2s ease;
            cursor: pointer;
            color: white;

            &:hover {
                background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
            }

            svg {
                width: 1rem;
                height: 1rem;
            }
        }

        .quick-actions-dropdown {
            flex-shrink: 0;
            
            .quick-actions-btn {
                padding: 0.5rem;
                border: 1px solid #e2e8f0;
                border-radius: 0.5rem;
                color: #64748b;
                background: transparent;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                    border-color: #f97316;
                    color: #f97316;
                    background: rgba(249, 115, 22, 0.05);
                }
                
                svg {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }
}

// 搜索建议样式
.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    
    .suggestion-icon {
        width: 1rem;
        height: 1rem;
        color: #64748b;
    }
    
    .suggestion-text {
        flex: 1;
        font-size: 0.875rem;
        color: #374151;
    }
    
    .suggestion-type {
        font-size: 0.75rem;
        color: #9ca3af;
        background: #f3f4f6;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
    }
}

// 状态筛选组
.status-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex-shrink: 0;
    min-width: 200px;

    @media (max-width: 1200px) {
        min-width: 100%;
    }

    .filter-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        color: #475569;
        letter-spacing: 0.025em;

        .filter-icon {
            width: 1rem;
            height: 1rem;
            color: #64748b;
        }
    }

    .status-select {
        
        :deep(.el-select__wrapper) {
            vertical-align: middle;
            min-height: 45px;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            // height: 50px;
            width: 100px;;
            font-size: 15px;
            text-align: center;
            &:hover {
                border-color: #f97316;
                box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
            }

            &.is-focus {
                border-color: #f97316;
                box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
            }
        }
    }
}

// 时间筛选组 - 重新设计的布局
.time-filters-container {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    width: 100%;
    
    @media (max-width: 968px) {
        flex-direction: column;
        gap: 1.25rem;
    }
}

.quick-time-section {
    flex: 1;
    min-width: 0;
    
    .time-section-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.875rem;
        
        .time-icon {
            width: 1.125rem;
            height: 1.125rem;
            color: #f97316;
            flex-shrink: 0;
        }
        
        .time-section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #475569;
            letter-spacing: 0.025em;
        }
    }
    
    .quick-time-buttons {
        display: flex;
        gap: 0.625rem;
        flex-wrap: wrap;
        
        .time-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            background: #ffffff;
            color: #475569;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            
            &:hover {
                border-color: #f97316;
                background: rgba(249, 115, 22, 0.05);
                color: #f97316;
                transform: translateY(-1px);
                box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
            }
            
            &.active {
                border-color: #f97316;
                background: #f97316;
                color: white;
                box-shadow: 0 2px 6px rgba(249, 115, 22, 0.3);
                
                &:hover {
                    background: #ea580c;
                    border-color: #ea580c;
                }
            }
        }
    }
}

.custom-date-section {
    flex-shrink: 0;
    
    .date-range-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        
        @media (max-width: 768px) {
            justify-content: center;
        }
        
        .date-input {
            width: 140px;
            
            :deep(.el-date-editor) {
                .el-input__wrapper {
                    border: 1px solid #e2e8f0;
                    border-radius: 0.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                    transition: all 0.3s ease;
                    
                    &:hover {
                        border-color: #f97316;
                        box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
                    }
                    
                    &.is-focus {
                        border-color: #f97316;
                        box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
                    }
                    
                    .el-input__inner {
                        font-size: 0.875rem;
                        color: #374151;
                        
                        &::placeholder {
                            color: #9ca3af;
                        }
                    }
                }
            }
            
            @media (max-width: 768px) {
                width: 120px;
            }
        }
        
        .date-separator {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            margin: 0 0.25rem;
            flex-shrink: 0;
        }
    }
}

// 下拉菜单样式
:deep(.el-dropdown-menu) {
    border-radius: 0.75rem;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(12px);
    padding: 0.5rem 0;
    min-width: 180px;

    .el-dropdown-menu__item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.875rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 0.5rem;
        margin: 0.125rem 0.5rem;

        .dropdown-icon {
            font-size: 1.125rem;
            width: 1.25rem;
            height: 1.25rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        &:hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #1e293b;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
    }
}

// 增强的响应式设计 - 参考 demo-filters.html
@media (max-width: 1200px) {
    .filters-row-primary {
        flex-direction: column;
        align-items: stretch;
        gap: 1.25rem;
    }

    .search-group {
        min-width: 100%;
    }

    .status-group {
        min-width: 100%;
    }
}

// 平板端优化
@media (max-width: 968px) {
    .order-filters {
        margin: 1.25rem 0;
        border-radius: 0.875rem;
    }

    .batch-operations {
        padding: 1.125rem 1.25rem;
        flex-direction: column;
        align-items: center;
        gap: 1rem;

        .batch-info {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        .batch-actions {
            justify-content: center;
            flex-wrap: wrap;
            gap: 0.625rem;
        }
    }

    .filters-section {
        padding: 1.25rem;
        gap: 1.25rem;
    }
}

// 移动端优化 - 增强版
@media (max-width: 768px) {
    .order-filters {
        margin: 1rem 0;
        border-radius: 0.75rem;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    .batch-operations {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;

        .batch-info {
            .selected-badge {
                padding: 0.625rem 0.875rem;
                justify-content: center;
                
                .selected-count {
                    font-size: 0.85rem;
                }
            }
        }

        .batch-actions {
            justify-content: center;
            flex-wrap: wrap;
            gap: 0.5rem;
            
            .batch-btn {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                min-width: 100px;
            }
        }
    }

    .filters-section {
        padding: 1rem;
        gap: 1rem;
        
        .collapse-toggle {
            padding: 0.875rem;
            
            .collapse-text {
                font-size: 0.8rem;
            }
        }
    }

    .search-group {
        .search-container {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: 0.875rem;

            .search-type-wrapper {
                width: 100%;
                
                .search-type-select {
                    :deep(.el-select__wrapper) {
                        padding: 0.625rem 0.75rem;
                    }
                }
            }
            
            .search-input-wrapper {
                .search-input {
                    :deep(.el-input__wrapper) {
                        padding: 0.625rem 0.75rem;
                        
                        .el-input__inner {
                            font-size: 0.875rem;
                        }
                    }
                }
            }
            
            .quick-actions-dropdown {
                align-self: center;
            }
        }
    }

    .status-group {
        .filter-label {
            font-size: 0.8rem;
        }
        
        .status-select {
            :deep(.el-select__wrapper) {
                padding: 0.625rem 0.75rem;
            }
        }
    }

    .time-filters-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .quick-time-section {
        .time-section-header {
            margin-bottom: 0.625rem;
            
            .time-section-title {
                font-size: 0.8rem;
            }
        }
        
        .quick-time-buttons {
            justify-content: center;
            gap: 0.5rem;
            
            .time-btn {
                padding: 0.4rem 0.75rem;
                font-size: 0.8rem;
            }
        }
    }
    
    .custom-date-section {
        .date-range-container {
            flex-direction: column;
            gap: 0.625rem;
            align-items: center;
            
            .date-input {
                width: 100%;
                max-width: 200px;
                
                :deep(.el-date-editor) {
                    .el-input__wrapper {
                        .el-input__inner {
                            font-size: 0.8rem;
                        }
                    }
                }
            }
            
            .date-separator {
                font-size: 0.8rem;
                margin: 0;
            }
        }
    }
    
    .filter-summary {
        padding: 0.875rem 1rem;
        
        .summary-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.625rem;
            
            .summary-title {
                font-size: 0.8rem;
            }
            
            .clear-all-btn {
                font-size: 0.7rem;
                padding: 0.375rem 0.625rem;
                align-self: flex-end;
            }
        }
        
        .summary-tags {
            gap: 0.375rem;
            
            .filter-tag {
                font-size: 0.7rem;
                padding: 0.25rem 0.5rem;
            }
        }
    }
}

// 超小屏幕优化 (< 480px)
@media (max-width: 480px) {
    .order-filters {
        margin: 0.75rem 0;
        border-radius: 0.625rem;
    }
    
    .batch-operations {
        padding: 0.875rem;
        
        .batch-actions {
            .batch-btn {
                width: 100%;
                justify-content: center;
                margin-bottom: 0.375rem;
            }
        }
    }
    
    .search-group {
        .search-container {
            padding: 0.875rem;
        }
    }
    
    .time-group {
        .quick-ranges {
            flex-direction: column;
            align-items: center;
            
            .quick-range-btn {
                width: 100%;
                max-width: 200px;
                justify-content: center;
            }
        }
    }
}
</style>
