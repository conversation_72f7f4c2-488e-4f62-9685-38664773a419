<template>
    <div class="orders-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">{{ t('orders.title', '我的订单') }}</h1>
            <p class="page-subtitle">{{ t('orders.subtitle', '查看和管理您的订单') }}</p>
        </div>

        <!-- 筛选区域（包含批量操作） -->
        <OrderFilters :is-any-order-selected="batchOperations.isAnyOrderSelected.value"
            :selected-count="selectedOrders.length" :batch-loading="false" v-model:search-keyword="searchKeyword"
            v-model:search-type="searchType" v-model:current-status="currentStatus"
            v-model:current-quick-range="currentQuickRange" v-model:start-date="startDate" v-model:end-date="endDate"
            :quick-date-ranges="quickDateRanges"
            @batch-cancel="batchOperations.handleBatchCancel(() => orderManagement.loadOrders())"
            @batch-pay="handleBatchPay" @batch-export="handleBatchExport" @search="orderManagement.searchOrders"
            @clear-search="orderManagement.clearSearchConditions" @status-change="orderManagement.setStatusFilter"
            @range-change="orderManagement.setQuickDateRange" @date-change="orderManagement.handleDateChange" />

        <!-- 主内容区域 - 固定最小高度确保UI稳定性 -->
        <div class="main-content-area">
            <!-- 加载状态 -->
            <div v-if="loading" class="orders-content loading-state">
                <div class="flex justify-center items-center py-12">
                    <div class="loading-spinner"></div>
                    <p class="ml-4">{{ t('orders.loading', '加载订单数据中...') }}</p>
                </div>
            </div>

            <!-- 订单表格 -->
            <div v-else-if="orders.length > 0" class="orders-content">
                <OrderTable ref="ordersTableRef" :orders="orders" :selected-order-nos="selectedOrders"
                    :get-order-products="orderManagement.getGroupedOrderProducts" :get-product-name="getProductName"
                    :get-product-specs="getProductSpecs" :get-product-unit-price="getProductUnitPrice"
                    :get-product-total-price="getProductTotalPrice" :get-order-item-status-text="getOrderItemStatusText"
                    :get-item-status-tag-type="getItemStatusTagType" :format-date="formatDate"
                    :format-price-with-symbol="currencyStore.formatPriceWithSymbol"
                    :get-current-order-amount="getCurrentOrderAmount" :get-product-amount="getProductAmount"
                    :get-shipping-fee="getShippingFee" :get-service-fee="getServiceFee"
                    :get-purchase-order-status-text="getPurchaseOrderStatusText"
                    :get-purchase-order-status-tag-type="getPurchaseOrderStatusTagType"
                    @single-order-selection="batchOperations.handleSingleOrderSelection"
                    @view-order-detail="orderActions.viewOrderDetail" @copy-order-no="copyOrderNo" @copy-sku-id="copySkuId"
                    @copy-product-id="copyProductId" @order-action="handleOrderAction" 
                    @image-hover="handleImageHover" @image-error="handleImageError" />
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
                <div class="empty-icon">
                    <svg class="empty-icon-svg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
                <div class="empty-content">
                    <h3>{{ t('orders.empty.title', '暂无订单') }}</h3>
                    <p>{{ t('orders.empty.description', '您还没有任何订单，去逛逛商城吧！') }}</p>
                    <div class="empty-actions">
                        <router-link to="/products" class="btn btn-primary">
                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            {{ t('orders.empty.browsing', '浏览商品') }}
                        </router-link>
                        <router-link to="/categories" class="btn btn-secondary">
                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            {{ t('orders.empty.categories', '浏览分类') }}
                        </router-link>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页器 - 始终保留位置，避免布局跳动 -->
        <div class="pagination-container">
            <el-pagination 
                v-if="!loading && pagination.totalElements > 0" 
                v-model:current-page="pagination.page" 
                :page-size="pagination.size"
                :total="pagination.totalElements" 
                layout="total, prev, pager, next, jumper" 
                @current-change="changePage"
                background />
            <!-- 分页器占位符，保持布局稳定 -->
            <div v-else class="pagination-placeholder"></div>
        </div>
    </div>

    <!-- 取消订单对话框 -->
    <el-dialog v-model="cancelDialogVisible" :title="t('orders.cancel.title', '确认取消订单')" width="400px"
        :close-on-click-modal="false" :close-on-press-escape="false">
        <div class="cancel-reason-form">
            <div class="form-item">
                <label class="form-label">{{ t('orders.cancel.reason', '取消原因') }}</label>
                <el-select v-model="selectedCancelReason" :placeholder="t('orders.cancel.reason', '请选择取消原因')"
                    style="width: 100%">
                    <el-option v-for="reason in cancelReasons" :key="reason" :label="reason" :value="reason" />
                </el-select>
            </div>
            <div v-if="selectedCancelReason === t('orders.cancel.reasons.other', '其他')" class="form-item">
                <label class="form-label">{{ t('orders.cancel.otherReason', '其他原因') }}</label>
                <el-input v-model="otherCancelReason" type="textarea" :rows="3"
                    :placeholder="t('orders.cancel.placeholder', '请详细说明取消原因')" />
            </div>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancelDialogVisible = false">{{ t('common.cancel', '取消') }}</el-button>
                <el-button type="primary" :loading="actionLoading" @click="handleCancelConfirm">{{
                    t('common.confirm', '确认') }}</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 图片预览模态框 -->
    <div v-if="hoveredImageRef" class="image-preview-modal" @click="handleModalHover(false)"
        @mouseenter="handleModalHover(true)" @mouseleave="handleModalHover(false)">
        <div class="image-preview-container" @click.stop>
            <img :src="hoveredImageRef.src" :alt="hoveredImageRef.alt" class="image-preview-large"
                @error="handleImageError" />
            <div class="image-preview-close" @click="handleModalHover(false)">×</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { orderApi, type AttrJson } from '@/api/modules/order';
import {
    ORDER_STATUS_ZH_MAP,
    ORDER_STATUS_EN_MAP,
    ORDER_STATUS_TAG_TYPE_MAP,
    getOrderStatusEnum
} from '@/types/enums/orderStatus';
// 使用异步组件提升初始加载性能
const OrderFilters = defineAsyncComponent(() => import('@/components/orders/OrderFilters.vue'));
const OrderTable = defineAsyncComponent(() => import('@/components/orders/OrderTable.vue'));
import { useClipboard } from '@/composables/useClipboard';
import { useCurrencyFormat } from '@/composables/useCurrencyFormat';
import { useOrderUtils } from '@/composables/useOrderUtils';
import { useOrderManagement } from '@/composables/useOrderManagement';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, onUnmounted, ref, defineAsyncComponent } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const router = useRouter();


// 使用组合函数
const {
    getProductName,
    getProductSpecs: getProductSpecsFromUtils,
    formatDate,
    getOrderItemStatusText,
    getItemStatusTagType,
} = useOrderUtils();

// Create wrapper function to match OrderTable's expected interface
const getProductSpecs = (specs: string | AttrJson[]): string => {
    if (typeof specs === 'string') {
        try {
            // If it's a string, try to parse it as JSON
            const parsed = JSON.parse(specs);
            if (Array.isArray(parsed)) {
                return getProductSpecsFromUtils(parsed);
            }
            return specs;
        } catch {
            return specs;
        }
    } else if (Array.isArray(specs)) {
        // If it's already an array, use it directly
        return getProductSpecsFromUtils(specs);
    }
    return '';
};

const {
    getProductUnitPrice,
    getProductTotalPrice,
    getCurrentOrderAmount,
    getProductAmount,
    getServiceFee,
    getShippingFee,
    currencyStore,
} = useCurrencyFormat();

const { copyToClipboard } = useClipboard();

// 使用优化后的订单管理 composable
const {
    loading,
    orders,
    searchKeyword,
    searchType,
    currentStatus,
    selectedOrders,
    startDate,
    endDate,
    currentQuickRange,
    pagination,
    quickDateRanges,
    isAnyOrderSelected,
    loadOrders,
    setStatusFilter,
    searchOrders,
    clearSearchConditions,
    handleDateChange,
    setQuickDateRange,
    changePage,
    getGroupedOrderProducts,
    initializeDefaultDateRange
} = useOrderManagement();

const actionLoading = ref(false);

// 表格引用
const ordersTableRef = ref();

// 图片预览相关状态
const hoveredImageRef = ref<{ src: string, alt: string } | null>(null);
const hoverTimer = ref<NodeJS.Timeout | null>(null);


// 方法

const viewOrderDetail = async (orderNo: string) => {
    router.push(`/user/orders/${orderNo}`);
};

// 添加取消订单对话框相关的响应式变量
const cancelDialogVisible = ref(false);
const selectedCancelReason = ref('');
const otherCancelReason = ref('');
const currentCancelOrderNo = ref('');

// 将 cancelReasons 改为计算属性以支持国际化
const cancelReasons = computed(() => [
    t('orders.cancel.reasons.outOfStock', '商品缺货'),
    t('orders.cancel.reasons.tooHighPrice', '价格太高'),
    t('orders.cancel.reasons.informationError', '信息填写错误'),
    t('orders.cancel.reasons.duplicateOrder', '重复下单'),
    t('orders.cancel.reasons.other', '其他'),
]);

const cancelOrder = async (orderNo: string) => {
    currentCancelOrderNo.value = orderNo;
    selectedCancelReason.value = '';
    otherCancelReason.value = '';
    cancelDialogVisible.value = true;
};

const handleCancelConfirm = async () => {
    if (!selectedCancelReason.value) {
        ElMessage.error(t('orders.cancel.selectReason', '请选择取消原因'));
        return;
    }

    if (
        selectedCancelReason.value === t('orders.cancel.reasons.other', '其他') &&
        !otherCancelReason.value.trim()
    ) {
        ElMessage.error(t('orders.cancel.otherReason', '请输入其他原因'));
        return;
    }

    let reason = selectedCancelReason.value;
    if (selectedCancelReason.value === t('orders.cancel.reasons.other', '其他')) {
        reason = otherCancelReason.value.trim();
    }

    try {
        actionLoading.value = true;
        await orderApi.cancelOrder(currentCancelOrderNo.value, reason);
        ElMessage.success(t('orders.success.cancelled', '订单取消成功'));
        cancelDialogVisible.value = false;
        loadOrders(); // 重新加载订单列表
    } catch (error: unknown) {
        const errorMessage =
            (error instanceof Error && error.message) ||
            t('orders.error.cancelFailed', '取消订单失败');
        ElMessage.error(errorMessage);
    } finally {
        actionLoading.value = false;
    }
};

const payOrder = async (orderNo: string) => {
    router.push(`/user/orders/${orderNo}/pay`);
};

// 申请退款
const refundOrder = async (_orderNo: string) => {
    try {
        await ElMessageBox.prompt(
            t('orders.refund.confirmMessage', '请输入退款原因：'),
            t('orders.refund.title', '申请退款'),
            {
                confirmButtonText: t('common.confirm', '确认'),
                cancelButtonText: t('common.cancel', '取消'),
                inputPlaceholder: t(
                    'orders.refund.reasonPlaceholder',
                    '例如：商品质量问题、不满意等'
                ),
                inputPattern: /.+/,
                inputErrorMessage: t(
                    'orders.refund.reasonRequired',
                    '退款原因是必填的'
                ),
                type: 'warning',
            }
        );

        actionLoading.value = true;

        ElMessage.success(t('orders.refund.success', '退款申请已提交，请等待处理'));
        loadOrders();
    } catch (error: unknown) {
        if (error !== 'cancel') {
            const errorMessage =
                (error instanceof Error && error.message) ||
                t('orders.refund.failed', '申请退款失败');
            ElMessage.error(errorMessage);
        }
    } finally {
        actionLoading.value = false;
    }
};


// 生命周期
onMounted(() => {
    // 初始化默认日期范围并加载数据
    initializeDefaultDateRange();
    loadOrders();
});

// 清理定时器
onUnmounted(() => {
});



// 处理订单操作
const handleOrderAction = (action: string, orderNo: string) => {
    switch (action) {
        case 'pay':
            payOrder(orderNo);
            break;
        case 'cancel':
            cancelOrder(orderNo);
            break;
        case 'refund':
            refundOrder(orderNo);
            break;
        case 'detail':
            viewOrderDetail(orderNo);
            break;
        case 'reorder':
            reorderProducts(orderNo);
            break;
        default:
    }
};

// 再次购买订单中的商品
const reorderProducts = async (_orderNo: string) => {
    ElMessage.info(t('orders.reorder.notImplemented', '再次购买功能开发中'));
};

// 复制SKU ID - 修复类型错误
const copySkuId = (skuId: string) => copyToClipboard(skuId, { type: 'sku' } as any);
const copyProductId = (productId: number) => copyToClipboard(String(productId), { type: 'id' } as any);

// 复制订单号 
const copyOrderNo = (orderNo: string) => copyToClipboard(orderNo, { type: 'order' } as any);

// 图片悬停预览功能
const handleImageHover = (event: Event, show: boolean) => {
    const target = event.target as HTMLImageElement;

    // 清除之前的定时器
    if (hoverTimer.value) {
        clearTimeout(hoverTimer.value);
        hoverTimer.value = null;
    }

    if (show) {
        // 300ms延迟显示大图
        hoverTimer.value = setTimeout(() => {
            hoveredImageRef.value = {
                src: target.src,
                alt: target.alt || '产品图片'
            };
        }, 300);
    } else {
        // 立即隐藏大图
        hoveredImageRef.value = null;
    }
};

// 处理模态框鼠标事件，保持图片显示
const handleModalHover = (show: boolean) => {
    if (hoverTimer.value) {
        clearTimeout(hoverTimer.value);
        hoverTimer.value = null;
    }

    if (!show) {
        hoveredImageRef.value = null;
    }
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    // 避免重复设置默认图片导致无限循环
    if (img.src.includes('/default-product.png')) {
        return;
    }
    
    // 设置默认图片
    img.src = '/default-product.png';
    
    // 添加错误样式类
    img.classList.add('image-error');
    
    // 可选：记录错误日志
    console.warn('图片加载失败:', img.dataset.originalSrc || img.src);
};

// 获取采购订单状态文本 - 使用新的枚举系统
const getPurchaseOrderStatusText = (status: string | number): string => {
    const statusValue = typeof status === 'string' ? parseInt(status, 10) : status;
    const statusEnum = getOrderStatusEnum(statusValue);
    
    if (statusEnum !== null) {
        // 根据当前语言返回对应文本
        const currentLocale = 'zh-CN'; // 或从 i18n 获取当前语言
        return currentLocale === 'zh-CN' 
            ? ORDER_STATUS_ZH_MAP[statusEnum] 
            : ORDER_STATUS_EN_MAP[statusEnum];
    }
    
    return t('orders.purchaseStatus.unknown', '未知状态');
};

// 获取采购订单状态标签类型 - 使用新的枚举系统
const getPurchaseOrderStatusTagType = (
    status: string | number
): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
    const statusValue = typeof status === 'string' ? parseInt(status, 10) : status;
    const statusEnum = getOrderStatusEnum(statusValue);
    
    if (statusEnum !== null) {
        return ORDER_STATUS_TAG_TYPE_MAP[statusEnum];
    }
    
    return 'info';
};

// 组织相关功能到对象中
const orderManagement = {
    loadOrders,
    searchOrders,
    clearSearchConditions,
    setStatusFilter,
    handleDateChange,
    setQuickDateRange,
    getGroupedOrderProducts,
};

const batchOperations = {
    isAnyOrderSelected,
    handleSingleOrderSelection: (orderNo: string, checked: boolean) => {
        if (checked) {
            if (!selectedOrders.value.includes(orderNo)) {
                selectedOrders.value.push(orderNo);
            }
        } else {
            const index = selectedOrders.value.indexOf(orderNo);
            if (index > -1) {
                selectedOrders.value.splice(index, 1);
            }
        }
    },
    handleBatchCancel: async (callback?: () => void) => {
        if (!isAnyOrderSelected.value) {
            return;
        }
        try {
            const { value: reason } = await ElMessageBox.prompt(
                t(
                    'orders.confirm.batchCancelMessageWithReason',
                    { count: selectedOrders.value.length },
                    `您确定要取消选中的 ${selectedOrders.value.length} 个订单吗？请输入原因：`
                ),
                t('orders.confirm.title', '确认操作'),
                {
                    confirmButtonText: t('common.confirm', '确认'),
                    cancelButtonText: t('common.cancel', '取消'),
                    inputPlaceholder: t(
                        'orders.confirm.reasonPlaceholder',
                        '例如：商品缺货、信息填写错误等'
                    ),
                    inputPattern: /.+/,
                    inputErrorMessage: t(
                        'orders.error.reasonRequired',
                        '取消原因是必填的'
                    ),
                    type: 'warning',
                }
            );

            if (!reason) {
                return;
            }

            // 由于没有批量取消API，使用循环调用单个取消
            for (const orderNo of selectedOrders.value) {
                await orderApi.cancelOrder(orderNo, reason);
            }
            ElMessage.success(t('orders.success.batchCancelled', '批量取消订单成功'));

            selectedOrders.value = [];
            if (callback) {
                callback();
            }
        } catch (error: unknown) {
            if (error === 'cancel') {
                return;
            }
            ElMessage.error(
                (error instanceof Error && error.message) ||
                t('orders.error.batchCancelFailed', '批量取消订单失败')
            );
        }
    },
};

const orderActions = {
    viewOrderDetail,
};

// 批量操作处理函数
const handleBatchPay = () => {
    ElMessage.info(t('orders.batchPay.notImplemented', '批量支付功能开发中'));
};

const handleBatchExport = () => {
    ElMessage.info(t('orders.batchExport.notImplemented', '批量导出功能开发中'));
};
</script>

<style lang="scss">
.orders-container {
    min-height: 100vh;
    width: 90%;
    margin: 0 auto;
    padding: 2.5rem 32px;
    box-sizing: border-box;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;

    @media (max-width: 768px) {
        padding: 1.5rem 20px;
    }

    @media (max-width: 480px) {
        padding: 1rem 16px;
    }
}

/* 主内容区域 - 确保UI稳定性 */
.main-content-area {
    min-height: 600px; /* 设置最小高度，确保UI稳定 */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 1.5rem;
    box-shadow: 
        0 10px 25px -5px rgba(0, 0, 0, 0.08), 
        0 4px 12px -2px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    margin: 2rem 0;
    padding: 2rem;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 
            0 20px 40px -10px rgba(0, 0, 0, 0.12), 
            0 8px 20px -4px rgba(0, 0, 0, 0.08),
            0 2px 6px rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        min-height: 400px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    @media (max-width: 480px) {
        min-height: 350px;
        padding: 1rem;
        margin: 1rem 0;
    }
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 加载状态布局一致性 */
.loading-state {
    /* 确保加载状态和内容状态具有相同的容器样式 */
    background: white;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 订单内容区域 */
.orders-content {
    min-height: 400px;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 分页器占位符 */
.pagination-placeholder {
    height: 64px; /* 与实际分页器高度一致 */
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.3;
}

.page-header {
    text-align: center;
    margin-bottom: 3.5rem;
    padding: 2.5rem 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 50%, rgba(241,245,249,0.9) 100%);
    border-radius: 1.5rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.8);
    box-shadow: 0 8px 32px rgba(0,0,0,0.08), 0 2px 8px rgba(0,0,0,0.04);
    
    .page-title {
        font-size: 2.75rem;
        font-weight: 800;
        background: linear-gradient(135deg, #1e293b 0%, #3b82f6 30%, #8b5cf6 70%, #f97316 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.875rem;
        line-height: 1.1;
        letter-spacing: -0.02em;
        
        @media (max-width: 768px) {
            font-size: 2.25rem;
        }
        
        @media (max-width: 480px) {
            font-size: 2rem;
        }
    }

    .page-subtitle {
        color: #64748b;
        font-size: 1.125rem;
        font-weight: 500;
        opacity: 0.9;
        
        @media (max-width: 768px) {
            font-size: 1rem;
        }
    }
}

/* 筛选和订单区域样式 - 现代化卡片设计 */
.filters-and-orders-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 1.5rem;
    box-shadow: 
        0 10px 25px -5px rgba(0, 0, 0, 0.08), 
        0 4px 12px -2px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    overflow: visible;
    margin: 2rem 0;
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 
            0 20px 40px -10px rgba(0, 0, 0, 0.12), 
            0 8px 20px -4px rgba(0, 0, 0, 0.08),
            0 2px 6px rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
    }

.batch-operations-row {
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f5f9;
    margin-bottom: 0.5rem;
}

.batch-operations {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    justify-content: flex-start;
    flex-wrap: wrap;

    @media (max-width: 768px) {
        justify-content: center;
        gap: 1rem;
    }

    @media (max-width: 480px) {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }
}

/* 筛选区域整体布局 - 响应式设计 */
.filters-row-one,
.filters-row-two {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
    margin-bottom: 0;

    .filters-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex-wrap: wrap;

        /* 2K分辨率以上 (2560px+) - 合并为一行 */
        @media (min-width: 2560px) {
            flex-wrap: nowrap;
            justify-content: space-between;
        }

        /* 1440p到2K之间 - 保持灵活布局 */
        @media (min-width: 1440px) and (max-width: 2559px) {
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        /* 1080p及以下 - 分列布局 */
        @media (max-width: 1439px) {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            gap: 0.75rem;
        }
    }

    @media (max-width: 768px) {
        gap: 0.75rem;
    }
}

/* 2K分辨率以上 - 合并筛选区域为一行 */
@media (min-width: 2560px) {
    .filters-row-one {
        background: #ffffff;
        border: none;
        border-radius: 0;
        box-shadow: none;
        padding: 1.25rem;
        margin-bottom: 1rem;

        .filters-content {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 1.5rem;
            flex-wrap: nowrap;
            width: 100%;
            overflow: hidden;
        }
    }

    .filters-row-two {
        display: none;
        /* 2K分辨率以上隐藏第二行 */
    }

    /* 在第一行中显示所有筛选内容 */
    .filters-row-one .filters-content {
        .search-group {
            flex: 1.5;
            min-width: 350px;
            max-width: 450px;
        }

        .status-group {
            flex: 0 0 auto;
            min-width: 200px;
        }

        /* 第一行中的时间筛选（2K分辨率以上显示） */
        .time-group-inline {
            flex: 1;
            min-width: 480px;
            max-width: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .filter-label {
                font-size: 0.9rem;
                font-weight: 500;
                color: #475569;
                white-space: nowrap;
            }

            .time-controls {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                flex-wrap: nowrap;
                flex: 1;

                .quick-buttons {
                    display: flex;
                    gap: 0.4rem;
                    flex-wrap: nowrap;
                    flex-shrink: 0;
                }

                .date-range {
                    display: flex;
                    align-items: center;
                    gap: 0.4rem;
                    flex-wrap: nowrap;
                    flex-shrink: 1;

                    .date-picker {
                        width: 187px; // 从140px增加到187px（加宽三分之一）

                        @media (max-width: 768px) {
                            width: 160px; // 移动端也相应增加
                        }
                    }

                    .date-separator {
                        font-size: 0.875rem;
                        color: #64748b;
                        font-weight: 500;
                        margin: 0 0.25rem;
                    }
                }
            }
        }
    }
}

/* 特定优化：2560x1440 (2K) 分辨率 */
@media (min-width: 2560px) and (max-width: 2880px) {
    .filters-row-one .filters-content {
        gap: 1.2rem;

        .search-group {
            flex: 1.3;
            min-width: 320px;
            max-width: 400px;
        }

        .status-group {
            flex: 0 0 auto;
            min-width: 180px;
        }

        .time-group-inline {
            flex: 1.7;
            min-width: 450px;

            .time-controls {
                gap: 0.6rem;

                .quick-buttons {
                    gap: 0.3rem;
                }

                .date-range {
                    gap: 0.3rem;

                    .date-picker {
                        width: 160px;
                    }
                }
            }
        }
    }
}

/* 1080p及以下 - 保持两行布局 */
@media (max-width: 2559px) {
    .filters-row-one {
        background: #ffffff;
        border: none;
        border-radius: 0;
        box-shadow: none;
        padding: 1.25rem;
        margin-bottom: 0;

        @media (max-width: 768px) {
            padding: 1rem;
        }
    }

    .filters-row-two {
        background: #ffffff;
        border: none;
        border-radius: 0;
        box-shadow: none;
        padding: 1.25rem;
        margin-bottom: 1rem;

        @media (max-width: 768px) {
            padding: 1rem;
        }
    }

    /* 第一行：搜索框和状态筛选 */
    .filters-row-one {
        .filters-content {
            @media (min-width: 1200px) {
                justify-content: space-between;
            }
        }
    }

    /* 第二行：时间筛选 */
    .filters-row-two {
        .filters-content {
            @media (min-width: 1200px) {
                justify-content: flex-start;
            }
        }
    }
}

/* 搜索组 */
.search-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 2;
    min-width: 400px;

    @media (max-width: 1200px) {
        min-width: 100%;
        flex: 1;
    }

    .search-input-wrapper {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid transparent;
        border-radius: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), inset 0 1px 3px rgba(0, 0, 0, 0.02);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
            background: #ffffff;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), inset 0 1px 3px rgba(0, 0, 0, 0.02);
            transform: translateY(-0.5px);
        }

        &:focus-within {
            border-color: #f97316;
            background: #ffffff;
            box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1), 0 8px 20px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        /* 搜索类型选择器 - 集成到搜索框内 */
        .search-type-wrapper {
            flex-shrink: 0;
            min-width: 120px;
            margin-left: 0.75rem;

            .search-type-select {
                width: 100%;

                /* Element Plus Select 样式覆盖 - 添加圆角边框 */
                :deep(.el-input) {
                    .el-input__wrapper {
                        border: 1px solid #e2e8f0 !important;
                        border-radius: 0.75rem !important;
                        background: #ffffff !important;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
                        padding: 0.25rem 0.5rem;
                        transition: all 0.3s ease;

                        &:hover {
                            border-color: #f97316 !important;
                            box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15) !important;
                            transform: translateY(-0.5px);
                        }

                        &.is-focus {
                            border-color: #f97316 !important;
                            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
                            transform: translateY(-0.5px);
                        }
                    }

                    .el-input__inner {
                        font-size: 0.875rem;
                        font-weight: 500;
                        color: #475569;
                        height: auto;
                        line-height: 1.5;
                        background: transparent !important;

                        &::placeholder {
                            color: #94a3b8;
                            font-weight: 400;
                        }
                    }

                    .el-input__suffix {
                        .el-input__suffix-inner {
                            .el-select__caret {
                                color: #64748b;
                                font-size: 0.875rem;
                                transition: all 0.3s ease;

                                &.is-reverse {
                                    transform: rotateZ(180deg);
                                }
                            }
                        }
                    }
                }
            }
        }

        .search-input {
            flex: 1;
            border: none !important;
            background: transparent !important;
            box-shadow: none !important;
            padding: 0.875rem 0.75rem;
            padding-right: 3rem;
            font-size: 0.9rem;
            font-weight: 500;
            color: #1e293b;
            outline: none;

            &:focus {
                border: none !important;
                background: transparent !important;
                box-shadow: none !important;
                transform: none;
            }

            &:hover {
                border: none !important;
                background: transparent !important;
                box-shadow: none !important;
                transform: none;
            }

            &::placeholder {
                color: #94a3b8;
                font-size: 0.875rem;
                font-weight: 400;
                transition: color 0.3s ease;
            }

            &:focus::placeholder {
                color: #cbd5e1;
            }
        }

        .clear-search-btn {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            width: 1.5rem;
            height: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            font-weight: 600;
            color: #94a3b8;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            &:hover {
                background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
                color: #dc2626;
                transform: translateY(-50%) scale(1.1);
                box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
            }

            &:active {
                transform: translateY(-50%) scale(0.95);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            }
        }
    }

    .search-btn {
        padding: 0.875rem 1.25rem;
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        color: white;
        border: 2px solid transparent;
        border-radius: 1rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-weight: 600;
        flex-shrink: 0;
        min-width: 52px;
        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25), 0 2px 6px rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;

        /* 按钮光效 */
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        &:hover::before {
            left: 100%;
        }

        &:hover {
            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(249, 115, 22, 0.35), 0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: rgba(255, 255, 255, 0.2);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25), 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        svg {
            width: 1.125rem;
            height: 1.125rem;
            position: relative;
            z-index: 1;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
    }
}

/* 状态筛选组 */
.status-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
    min-width: 200px;

    @media (max-width: 1200px) {
        width: 100%;
        min-width: auto;
    }

    .filter-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #475569;
        white-space: nowrap;
    }

    .status-select {
        flex: 1;
        min-width: 150px;
    }
}

/* 时间筛选组 - 第二行中的时间筛选 */
.time-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;

    @media (max-width: 1200px) {
        width: 100%;
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .filter-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #475569;
        white-space: nowrap;

        @media (max-width: 1200px) {
            align-self: flex-start;
        }
    }

    .time-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;

        @media (max-width: 1200px) {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }

        .quick-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;

            @media (max-width: 1200px) {
                justify-content: center;
            }

            /* 快捷时间按钮 - 现代化美观设计 */
            .quick-btn {
                padding: 0.625rem 1.25rem;
                border: 2px solid rgba(226, 232, 240, 0.6);
                border-radius: 0.75rem;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                color: #475569;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                white-space: nowrap;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
                position: relative;
                overflow: hidden;

                /* 按钮光效 */
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
                    transition: left 0.4s ease;
                }

                &:hover::before {
                    left: 100%;
                }

                &:hover {
                    border-color: rgba(249, 115, 22, 0.4);
                    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                    color: #f97316;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
                }

                &.active {
                    border-color: #ea580c;
                    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
                    color: white;
                    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3), 0 2px 6px rgba(0, 0, 0, 0.05);
                    transform: translateY(-1px);

                    &::before {
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    }

                    &:hover {
                        background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                        box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4), 0 3px 8px rgba(0, 0, 0, 0.08);
                        transform: translateY(-2px);
                    }
                }
            }
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;

            @media (max-width: 1200px) {
                justify-content: center;
            }

            .date-picker {
                width: 187px; // 从140px增加到187px（加宽三分之一）

                @media (max-width: 768px) {
                    width: 160px; // 移动端也相应增加
                }
            }

            .date-separator {
                font-size: 0.875rem;
                color: #64748b;
                font-weight: 500;
                margin: 0 0.25rem;
            }
        }
    }
}

// 空状态样式
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%);
    border-radius: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);

    .empty-icon {
        margin-bottom: 2rem;
        position: relative;
        
        .empty-icon-svg {
            width: 5rem;
            height: 5rem;
            color: #a855f7;
            filter: drop-shadow(0 4px 12px rgba(168, 85, 247, 0.2));
            animation: float 3s ease-in-out infinite;
        }
    }

    .empty-content {
        h3 {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        p {
            font-size: 1.1rem;
            color: #64748b;
            margin-bottom: 2.5rem;
            line-height: 1.6;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
    }

    .empty-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.875rem 1.75rem;
        border-radius: 0.875rem;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        .btn-icon {
            width: 1.25rem;
            height: 1.25rem;
            transition: transform 0.3s ease;
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        &:hover {
            transform: translateY(-2px) scale(1.05);
            
            .btn-icon {
                transform: rotate(10deg) scale(1.1);
            }

            &::before {
                left: 100%;
            }
        }

        &.btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.25);

            &:hover {
                box-shadow: 0 12px 35px rgba(99, 102, 241, 0.35);
            }
        }

        &.btn-secondary {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #475569;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);

            &:hover {
                background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
                border-color: #cbd5e1;
                color: #334155;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            }
        }
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

// 分页样式
.pagination-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 2rem;

    .el-pagination {
        background: white;
        padding: 1rem;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.8);
    }
}

.cancel-reason-form {
    .form-item {
        margin-bottom: 15px;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
}

// 取消订单对话框样式
:deep(.el-dialog) {
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

    .el-dialog__header {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-bottom: 1px solid #e2e8f0;
        padding: 1rem 1.5rem;
        border-radius: 1rem 1rem 0 0;

        .el-dialog__title {
            font-weight: 600;
            color: #1e293b;
        }
    }

    .el-dialog__body {
        padding: 1.5rem;
    }

    .el-dialog__footer {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-top: 1px solid #e2e8f0;
        padding: 1rem 1.5rem;
        border-radius: 0 0 1rem 1rem;

        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }
    }
}

// 复选框样式优化 - 现代化设计
:deep(.el-checkbox) {
    .el-checkbox__input {
        .el-checkbox__inner {
            width: 18px;
            height: 18px;
            border-radius: 0.375rem;
            border: 2px solid #d1d5db;
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0);
                width: 10px;
                height: 10px;
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border-radius: 0.125rem;
                transition: all 0.2s ease;
            }

            &:hover {
                border-color: #3b82f6;
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.15);
                transform: translateY(-0.5px);
            }
        }

        &.is-checked .el-checkbox__inner {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #1d4ed8;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

            &::after {
                transform: translate(-50%, -50%) scale(1);
                background: white;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            &:hover {
                background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                box-shadow: 0 3px 10px rgba(59, 130, 246, 0.4);
            }
        }

        &.is-indeterminate .el-checkbox__inner {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-color: #d97706;

            &::after {
                transform: translate(-50%, -50%) scale(1);
                background: white;
                width: 8px;
                height: 2px;
                border-radius: 1px;
            }
        }
    }

    .el-checkbox__label {
        font-size: 0.875rem;
        color: #374151;
        font-weight: 500;
        margin-left: 0.5rem;
        transition: color 0.2s ease;
    }

    &:hover .el-checkbox__label {
        color: #1f2937;
    }

    &.is-checked .el-checkbox__label {
        color: #1f2937;
        font-weight: 600;
    }
}

// 日期选择器样式优化
:deep(.el-date-editor) {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    .el-input__wrapper {
        border: none !important;
        box-shadow: none !important;
        background: transparent !important;
        padding: 0.25rem 0.5rem;

        .el-input__inner {
            border: none !important;
            background: transparent !important;
            font-size: 0.875rem;
            color: #374151;

            &::placeholder {
                color: #9ca3af;
                font-size: 0.875rem;
            }
        }

        .el-input__prefix {
            color: #6b7280;
        }
    }

    &:hover .el-input__wrapper {
        background-color: #f8fafc !important;
        border-radius: 0.375rem;
    }

    &.is-focus .el-input__wrapper {
        background-color: #f8fafc !important;
        border-radius: 0.375rem;
        box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1) !important;
    }
}

// 下拉选择器样式优化
:deep(.el-select) {
    .el-select__wrapper {
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
            border-color: #f97316;
            box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
        }

        &.is-focus {
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }

        .el-select__selected-item {
            font-size: 0.875rem;
            color: #374151;
        }

        .el-select__placeholder {
            color: #9ca3af;
            font-size: 0.875rem;
        }
    }
}

// 新的订单详情容器样式 - 现代化玻璃拟态设计
.order-detail-container {
    position: relative;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 50%, rgba(241, 245, 249, 0.9) 100%);
    border-radius: 1.25rem;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.08), 
        0 2px 8px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(20px);
    overflow: visible; // 确保图片放大时不被容器裁剪
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 1.5rem;

    &:hover {
        box-shadow: 
            0 16px 48px rgba(0, 0, 0, 0.12), 
            0 4px 16px rgba(0, 0, 0, 0.08),
            0 2px 6px rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
        border-color: rgba(255, 255, 255, 0.9);
    }

    // 订单头部信息
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        transition: all 0.3s ease;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin: -0.75rem -0.75rem 1rem -0.75rem;

        &:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border-bottom-color: #e2e8f0;
        }

        .order-header-left,
        .order-header-center,
        .order-header-right {
            display: flex;
            flex-direction: row; // 改为水平排列
            align-items: center; // 垂直居中对齐
            gap: 0.5rem; // 调整间距
        }

        .order-header-center {
            flex: 1;
            justify-content: center;
        }

        .order-header-right {
            flex: 0 0 auto;
        }

        .order-header-left {
            display: flex;
            flex-direction: row;
            align-items: center; // 改为居中对齐
            gap: 0.75rem;

            .order-checkbox {
                // 移除margin-top，因为现在是居中对齐

                :deep(.el-checkbox__input) {
                    .el-checkbox__inner {
                        width: 18px;
                        height: 18px;
                        border: 2px solid #d1d5db;
                        border-radius: 0.375rem;
                        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
                        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                        &:hover {
                            border-color: #3b82f6;
                            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                            transform: translateY(-1px) scale(1.05);
                            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                        }

                        &::after {
                            width: 4px;
                            height: 8px;
                            border: 2px solid #ffffff;
                            border-left: 0;
                            border-top: 0;
                            transform: rotate(45deg) scaleY(0);
                            transition: transform 0.15s ease-in 0.05s;
                            transform-origin: center;
                        }
                    }

                    &.is-checked .el-checkbox__inner {
                        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                        border-color: #1d4ed8;

                        &::after {
                            transform: rotate(45deg) scaleY(1);
                        }

                        &:hover {
                            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                            border-color: #1e40af;
                        }
                    }

                    &.is-indeterminate .el-checkbox__inner {
                        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                        border-color: #d97706;

                        &::before {
                            content: '';
                            position: absolute;
                            display: block;
                            background-color: #ffffff;
                            height: 2px;
                            transform: scale(0.5);
                            left: 0;
                            right: 0;
                            top: 5px;
                        }
                    }
                }
            }

            .order-info {
                display: flex;
                flex-direction: row; // 改为水平排列
                align-items: center; // 垂直居中对齐
                gap: 0.5rem; // 调整间距
            }

            // 复制订单号按钮样式
            .copy-order-btn {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 1.5rem;
                height: 1.5rem;
                margin-left: 0.5rem;
                padding: 0;
                background: transparent;
                cursor: pointer;
                transition: all 0.2s ease;
                color: #6b7280;

                &:hover {
                    background: #f3f4f6;
                    border-color: #52018d;
                    color: #52018d;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(82, 1, 141, 0.1);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 1px 2px rgba(82, 1, 141, 0.1);
                }

                .copy-icon {
                    width: 0.875rem;
                    height: 0.875rem;
                    stroke-width: 2;
                }
            }
        }

        .order-label {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            letter-spacing: 0.05em;
        }

        .order-number {
            font-size: 1rem;
            color: #52018d;
            font-weight: 800;
            word-break: break-all;
            transition: color 0.2s ease;

            // 可点击订单号的样式
            &.clickable-order-number {
                cursor: pointer;
                text-decoration: underline;
                text-decoration-color: transparent;
                transition: all 0.2s ease;

                &:hover {
                    color: #7c3aed;
                    text-decoration-color: #7c3aed;
                    transform: translateY(-1px);
                }

                &:active {
                    transform: translateY(0);
                    color: #5b21b6;
                }
            }
        }

        .order-time {
            font-size: 0.875rem;
            color: #111827;
            font-weight: 500;
        }
    }

    // 商品信息表格
    .order-products-table {
        margin-bottom: 1rem;

        .table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1.2fr 0.8fr 1fr 1fr 0.8fr 1fr;
            gap: 0.75rem;
            padding: 1.25rem 1rem;
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 30%, #7c3aed 70%, #8b5cf6 100%);
            border-radius: 1rem 1rem 0.5rem 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.875rem;
            font-weight: 700;
            color: #ffffff;
            letter-spacing: 0.025em;
            box-shadow: 
                0 8px 25px rgba(99, 102, 241, 0.35), 
                0 4px 12px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
                animation: shimmer 3s ease-in-out infinite;
            }

            @keyframes shimmer {
                0% {
                    left: -100%;
                }

                50% {
                    left: 100%;
                }

                100% {
                    left: 100%;
                }
            }

            >div {
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 800;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                &.col-product-name {
                    text-align: left;
                    justify-content: flex-start;
                    padding-left: 0.5rem;
                }

                &.col-product-image {
                    text-align: center;
                    justify-content: center;
                }
            }
        }

        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1.2fr 0.8fr 1fr 1fr 0.8fr 1fr;
            gap: 0.75rem;
            padding: 1rem 0.75rem;
            border: 1px solid #e5e7eb;
            border-top: none;
            align-items: center;
            overflow: visible;
            background: #ffffff;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6);
                transform: translateY(-1px);
                border-color: #cbd5e1;
            }

            &:last-child {
                border-radius: 0 0 0.75rem 0.75rem;
                border-bottom: 1px solid #e5e7eb;
            }

            &.group-first {
                border-top: 2px solid #e6f4ff;
                background-color: #fafbfc;

                .col-product-name {
                    border-left: 3px solid #1890ff;
                    padding-left: 12px;
                }
            }

            &.group-continued {
                background-color: #fafbfc;
                border-left: 1px solid #e6f4ff;
                margin-left: 8px;

                .col-product-name {
                    opacity: 0;
                    pointer-events: none;
                }
            }

            &.loading-row {
                grid-template-columns: 1fr;
                text-align: center;
                color: #6b7280;
                font-style: italic;
            }

            .col-product-name {
                text-align: left;

                .product-name {
                    font-size: 0.875rem;
                    color: #111827;
                    font-weight: 800;
                    margin-bottom: 0.25rem;
                    line-height: 1.4;
                }

                .more-products {
                    font-size: 0.75rem;
                    color: #6b7280;
                    font-style: italic;
                }
            }

            .col-product-image {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: visible; // 确保图片放大时不被列容器裁剪
                position: relative; // 为absolute定位的子元素提供定位上下文

                .product-image-container {
                    position: relative;
                    width: 80px;
                    height: 80px;
                    border-radius: 0.375rem;
                    border: 1px solid #e5e7eb;
                    transition: all 0.3s ease;
                    overflow: hidden;
                    cursor: pointer;

                    &:hover {
                        border-color: #3b82f6;
                        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                        transform: translateY(-1px);
                    }
                }

                .product-image-small {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .col-sku-image {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: visible;
                position: relative;

                .sku-images-grid {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0.25rem;
                    justify-content: center;
                    align-items: center;

                    .product-image-container {
                        position: relative;
                        width: 40px;
                        height: 40px;
                        border-radius: 0.25rem;
                        border: 1px solid #e5e7eb;
                        transition: all 0.3s ease;
                        overflow: hidden;
                        cursor: pointer;

                        &.mini {
                            width: 30px;
                            height: 30px;
                        }

                        &:hover {
                            border-color: #3b82f6;
                            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
                            transform: translateY(-1px);
                        }

                        .product-image-small {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            transition: transform 0.3s ease;

                            &:hover {
                                transform: scale(1.05);
                            }
                        }
                    }

                    .more-images {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 30px;
                        height: 30px;
                        background: #f3f4f6;
                        border: 1px solid #e5e7eb;
                        border-radius: 0.25rem;
                        font-size: 0.6rem;
                        color: #6b7280;
                        font-weight: 600;
                    }
                }
            }

            .col-specification {
                text-align: center;

                .specification-text {
                    font-size: 0.75rem;
                    color: #6b7280;
                    line-height: 1.4;
                }
            }

            .col-quantity,
            .col-unit-price,
            .col-sku,
            .col-product-id,
            .col-order-status,
            .col-total {
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;

                .quantity-text,
                .price-text,
                .sku-text,
                .status-text,
                .total-text {
                    font-size: 0.875rem;
                    color: #111827;
                    font-weight: 500;
                }

                .sku-text,
                .product-id-text {
                    color: #3b82f6;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                .total-text {
                    color: #ea580c;
                    font-weight: 600;
                }

                // 分组显示样式
                .sku-list {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                    align-items: center;

                    .sku-text {
                        font-size: 0.75rem;
                        padding: 0.125rem 0.25rem;
                        background: #f3f4f6;
                        border-radius: 0.25rem;
                        display: flex;
                        align-items: center;
                        gap: 0.125rem;
                    }
                }

                .specification-list {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;

                    .specification-item {
                        font-size: 0.75rem;
                        color: #6b7280;
                        line-height: 1.2;
                        padding: 0.125rem;
                    }
                }

                .status-list {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                    align-items: center;
                }
            }
        }
    }

    // 采购订单信息面板
    .order-summary-panel {
        margin-top: 0.5rem;
        margin-bottom: 0.75rem;
        padding: 0.75rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        .summary-row {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            font-size: 0.75rem;

            .info-item {
                display: flex;
                align-items: center;
                gap: 0.375rem;
                white-space: nowrap;

                .info-label {
                    color: #6b7280;
                    font-weight: 600;
                    font-size: 0.75rem;
                }

                .info-value {
                    color: #374151;
                    font-weight: 500;
                    font-size: 0.75rem;
                }

                .el-tag {
                    font-size: 0.6875rem;
                    height: auto;
                    padding: 0.1875rem 0.375rem;
                    border-radius: 0.25rem;
                }
            }

            .info-separator {
                color: #d1d5db;
                font-weight: 300;
                font-size: 0.875rem;
                margin: 0 0.25rem;
            }

            .cost-item {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                white-space: nowrap;

                &.total-item {
                    .cost-label {
                        font-weight: 700;
                        color: #374151;
                        font-size: 12px;
                    }

                    .total-price {
                        color: #dc2626;
                        font-weight: 800;
                        font-size: 12px;
                    }
                }

                .cost-label {
                    color: #6b7280;
                    font-size: 12px;
                    font-weight: 700;
                }

                .cost-value {
                    color: #16a34a;
                    font-weight: 700;
                    font-size: 12px;
                }
            }
        }

        // 响应式设计：小屏幕时换行显示
        @media (max-width: 768px) {
            .summary-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;

                .info-item,
                .cost-item {
                    justify-content: space-between;
                    width: 100%;
                }

                .info-separator {
                    display: none;
                }
            }
        }
    }

    // 订单底部区域
    .order-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-top: 1px solid #e2e8f0;
        border-radius: 0 0 0.75rem 0.75rem;
        gap: 1rem;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6);
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
        }
    }

    // 订单汇总信息
    .order-summary {
        flex: 1;
    }

    // 保留原有 cost-row 样式以维持向后兼容性
    .cost-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.25rem 0;

        &.total-row {
            margin-top: 0.375rem;
            padding-top: 0.375rem;

            .cost-label {
                font-weight: 700;
                color: #374151;
                font-size: 0.8125rem;
            }

            .total-price {
                color: #dc2626;
                font-weight: 800;
                font-size: 0.875rem;
            }
        }

        .cost-label {
            color: #6b7280;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
        }

        .cost-value {
            color: #16a34a;
            font-weight: 700;
            text-align: right;
            min-width: 50px;
            font-size: 0.75rem;
        }
    }

    .cost-divider {
        height: 1px;
        background: #d1d5db;
        margin: 0.375rem 0;
    }
}

// 操作按钮组样式
.order-actions-header {
    .order-actions-dropdown {
        .order-actions-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.625rem 1.25rem;
            font-size: 0.875rem;
            font-weight: 600;
            border-radius: 0.5rem;
            border: 1px solid transparent;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25), 0 1px 3px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;

            /* 按钮光效 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s ease;
            }

            &:hover::before {
                left: 100%;
            }

            &:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35), 0 2px 6px rgba(0, 0, 0, 0.08);
                border-color: rgba(255, 255, 255, 0.1);
            }

            &:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25), 0 1px 3px rgba(0, 0, 0, 0.05);
            }

            .el-icon {
                font-size: 1rem;
                transition: transform 0.3s ease;
            }

            &:hover .el-icon {
                transform: rotate(180deg);
            }
        }
    }

    .action-buttons-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.5rem 0.875rem;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 0.375rem;
        border: 1px solid;
        transition: all 0.15s ease-in-out;
        cursor: pointer;
        white-space: nowrap;

        .action-icon {
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
        }

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        &:active {
            transform: translateY(0);
        }

        &.action-btn-pay {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #059669;
            color: white;

            &:hover {
                background: linear-gradient(135deg, #059669, #047857);
                border-color: #047857;
            }
        }

        &.action-btn-detail {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-color: #2563eb;
            color: white;

            &:hover {
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
                border-color: #1d4ed8;
            }
        }

        &.action-btn-more {
            background: white;
            border-color: #d1d5db;
            color: #6b7280;
            padding: 0.5rem;
            border-radius: 50%;

            &:hover {
                background: #f9fafb;
                border-color: #9ca3af;
                color: #374151;
            }
        }
    }
}

// 下拉菜单图标样式
.dropdown-icon {
    width: 1.125rem;
    height: 1.125rem;
    margin-right: 0.5rem;
    color: currentColor;
    flex-shrink: 0;
}

// 保持向后兼容
.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.375rem 0;

    &:not(:last-child) {
        border-bottom: 1px solid #e5e7eb;
    }

    .summary-label {
        font-size: 1rem;
        font-weight: 500;
    }

    .summary-value {
        font-size: 1rem;
        color: #111827;
        font-weight: 800;
    }
}


// 操作按钮
.order-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    flex-wrap: wrap;
    flex: 0 0 auto;

    .el-button {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        height: 28px;
        min-width: 80px;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: nowrap;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
    }
}


// 响应式设计
@media (max-width: 1024px) {
    .order-detail-container {
        .order-products-table {

            .table-header,
            .table-row {
                grid-template-columns: 1.5fr 0.8fr 1fr 0.6fr 0.8fr 0.8fr 0.6fr 0.8fr;
                gap: 0.5rem;
                padding: 0.5rem;
                font-size: 0.75rem;
            }
        }
    }
}

@media (max-width: 768px) {
    .order-detail-container {
        padding: 0.75rem;

        .order-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;

            .order-header-left,
            .order-header-center,
            .order-header-right {
                width: 100%;
            }

            .order-header-center {
                justify-content: flex-start;
            }

            .order-header-right {
                justify-content: flex-end;
            }
        }

        .order-footer {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;

            .order-summary {
                .summary-row {
                    justify-content: center;
                    text-align: center;
                }
            }

            .order-actions {
                justify-content: center;
            }
        }

        .order-products-table {
            .table-header {
                display: none; // 隐藏表头，使用卡片式布局
            }

            .table-row {
                display: block;
                padding: 1rem;
                border-radius: 0.5rem;
                margin-bottom: 0.75rem;
                background: #ffffff;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

                >div {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0.25rem 0;

                    &:before {
                        content: attr(data-label);
                        font-weight: 600;
                        color: #6b7280;
                        font-size: 0.75rem;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    }
                }

                .col-product-image {
                    flex-direction: column;
                    align-items: center;

                    &:before {
                        content: 'Image';
                        margin-bottom: 0.25rem;
                    }
                }

                .col-product-name {
                    flex-direction: column;
                    align-items: flex-start;

                    &:before {
                        content: 'Product Name';
                        margin-bottom: 0.25rem;
                    }
                }
            }
        }

        .order-actions {
            justify-content: center;

            .el-button {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
                height: 28px;
                min-width: 80px;
                border-radius: 0.375rem;
                font-weight: 500;
                white-space: nowrap;
                flex: 0 0 auto;
            }
        }
    }
}

.toggle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #52018d;
    font-size: 1.25rem;
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
        background: rgba(82, 1, 141, 0.1);
        border-color: rgba(82, 1, 141, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(82, 1, 141, 0.15);
    }

    &:active {
        transform: translateY(0);
    }

    &.expanded {
        transform: rotate(180deg);

        &:hover {
            transform: rotate(180deg) translateY(-1px);
        }
    }
}

/* 商品详细信息表格展开动画 */
.order-products-table {
    animation: slideDown 0.3s ease-out;
    overflow: visible; // 改为visible，允许图片放大时突破表格边界
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        max-height: 1000px;
        transform: translateY(0);
    }
}

/* 可点击的SKU和商品ID样式 */
.clickable-sku,
.clickable-product-id {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    justify-content: center;

    &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .copy-icon {
        opacity: 0.7;
        transition: all 0.2s ease;
        color: #6b7280;
        flex-shrink: 0;
    }

    &:hover .copy-icon {
        opacity: 1;
        color: #3b82f6;
        transform: scale(1.1);
    }
}

.product-price-detail {
    font-size: 0.875rem;
    font-weight: 500;
}

.product-quantity-detail {
    font-size: 0.875rem;
}

/* 商品操作按钮区域 - 现代化美观设计 */
.product-actions-detail {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 140px;
    margin: 0 auto !important;
    padding: 0.75rem 0.5rem;

    &:hover {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        transform: translateY(-1px);
    }

    .el-button {
        width: 100% !important;
        height: 36px !important;
        font-size: 13px !important;
        font-weight: 500 !important;
        padding: 8px 12px !important;
        border-radius: 0.5rem !important;
        white-space: nowrap;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 100px !important;
        max-width: 130px !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        border: 1px solid transparent !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
        overflow: hidden !important;

        /* 按钮内部光效 */
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        &:hover::before {
            left: 100%;
        }

        /* 主要按钮样式 */
        &.el-button--primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
            border-color: #2563eb !important;
            color: white !important;
            box-shadow: 0 2px 6px rgba(59, 130, 246, 0.25) !important;

            &:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
                border-color: #1d4ed8 !important;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35) !important;
                transform: translateY(-2px) !important;
            }

            &:active {
                transform: translateY(0) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.25) !important;
            }
        }

        /* 成功按钮样式 */
        &.el-button--success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
            border-color: #059669 !important;
            color: white !important;
            box-shadow: 0 2px 6px rgba(16, 185, 129, 0.25) !important;

            &:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
                border-color: #047857 !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35) !important;
                transform: translateY(-2px) !important;
            }

            &:active {
                transform: translateY(0) !important;
                box-shadow: 0 2px 6px rgba(16, 185, 129, 0.25) !important;
            }
        }

        /* Plain 按钮样式 */
        &.is-plain {
            background: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px) !important;

            &.el-button--primary {
                color: #2563eb !important;
                border-color: rgba(37, 99, 235, 0.3) !important;
                background: rgba(59, 130, 246, 0.05) !important;

                &:hover {
                    background: rgba(59, 130, 246, 0.1) !important;
                    border-color: rgba(37, 99, 235, 0.5) !important;
                    color: #1d4ed8 !important;
                }
            }

            &.el-button--success {
                color: #059669 !important;
                border-color: rgba(5, 150, 105, 0.3) !important;
                background: rgba(16, 185, 129, 0.05) !important;

                &:hover {
                    background: rgba(16, 185, 129, 0.1) !important;
                    border-color: rgba(5, 150, 105, 0.5) !important;
                    color: #047857 !important;
                }
            }
        }

        /* 按钮文字样式 */
        span {
            position: relative;
            z-index: 1;
            font-weight: 500;
            letter-spacing: 0.025em;
        }
    }
}

.loading-products {
    text-align: center;
    padding: 1rem 0;
}

.loading-text {
    font-size: 0.875rem;
    color: #6b7280;
}

/* 订单号列样式 - 基础布局 */
.order-number-cell {
    display: flex;
    flex-direction: column;
    /* clickable样式已在order-basic-info中定义 */
}

/* 新的订单信息容器样式 */
.order-info-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

/* 基本订单信息样式 */
.order-basic-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    &.clickable {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;

        &:hover {
            background-color: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            .order-number {
                color: #31018d;
            }
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
    }

    .order-number {
        font-weight: 700;
        color: #52018d;
        word-break: break-all;
        transition: color 0.2s ease;
    }

    .order-quantity {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .order-date {
        font-size: 0.75rem;
        color: #6b7280;
    }
}

/* 订单金额行样式 */
.order-amount-row {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.5rem 0;
    border-top: 1px solid #e5e7eb;

    .order-amount {
        font-size: 1.125rem;
        font-weight: 700;
        color: #059669;
    }

    .order-update-time {
        font-size: 0.75rem;
        color: #6b7280;
    }
}

/* 操作按钮行样式 */
.order-actions-row {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding: 0.5rem 0;
    border-top: 1px solid #e5e7eb;

    .el-button {
        font-size: 0.75rem !important;
        padding: 0.375rem 0.75rem !important;
        height: auto !important;
        min-height: 28px !important;
        border-radius: 0.375rem !important;
        margin: 0 !important;
    }
}

/* 订单金额列样式 */
.order-amount-cell {
    text-align: right;
}

/* 操作列样式 */
.order-actions-cell {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem;
    align-items: center !important;
    justify-content: center !important;
    min-height: 80px;
    padding: 0.5rem !important;
    text-align: center !important;
}

.action-buttons-row {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 110px;
    margin: 0 auto !important;

    .el-button {
        width: 100% !important;
        height: 32px !important;
        font-size: 12px !important;
        padding: 6px 8px !important;
        border-radius: 4px;
        white-space: nowrap;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 90px !important;
        max-width: 110px !important;
        box-sizing: border-box !important;
        margin: 0 !important;
    }
}

/* 简化的表格样式覆盖 */
.el-table .el-table__cell {
    &.has-order-actions-cell {
        padding: 0 !important;
        text-align: center !important;
        vertical-align: middle !important;
    }

    &.has-product-actions-detail {
        text-align: center !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
    }
}

/* 按钮间距重置 */
.action-buttons-row .el-button,
.product-actions-detail .el-button {
    margin: 0 !important;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {

    /* 整体容器适配 */
    .orders-container {
        padding: 0.75rem;
        background: #f8fafc;
    }

    /* 页面标题适配 */
    .page-header {
        text-align: center;
        margin-bottom: 1.5rem;

        .page-title {
            font-size: 1.75rem;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
    }

    /* 筛选区域移动端适配 */
    .filters-section {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    /* 批量操作区域适配 */
    .batch-operations {
        justify-content: center;
        gap: 0.5rem;

        .el-checkbox {
            font-size: 0.875rem;
        }

        .el-button {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }
    }

    /* 搜索组移动端优化 */
    .search-group {
        min-width: 100%;

        .search-input {
            font-size: 0.875rem;
            padding: 0.625rem 0.875rem;
            padding-right: 2.25rem;
        }

        .search-btn {
            padding: 0.625rem 0.875rem;
            min-width: 44px;
        }
    }

    /* 状态筛选移动端优化 */
    .status-group {
        .filter-label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
    }

    /* 时间筛选移动端优化 */
    .time-group {
        .filter-label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .quick-buttons {
            justify-content: flex-start;

            /* 移动端快捷时间按钮 - 现代化美观设计 */
            .quick-btn {
                font-size: 0.75rem;
                padding: 0.5rem 0.875rem;
                border: 1.5px solid rgba(226, 232, 240, 0.6);
                border-radius: 0.625rem;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                color: #475569;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                white-space: nowrap;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
                position: relative;
                overflow: hidden;
                min-height: 36px;

                /* 移动端按钮光效 */
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
                    transition: left 0.4s ease;
                }

                &:hover::before {
                    left: 100%;
                }

                &:hover {
                    border-color: rgba(249, 115, 22, 0.4);
                    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                    color: #f97316;
                    transform: translateY(-0.5px);
                    box-shadow: 0 3px 8px rgba(249, 115, 22, 0.15);
                }

                &.active {
                    border-color: #ea580c;
                    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
                    color: white;
                    box-shadow: 0 3px 8px rgba(249, 115, 22, 0.3), 0 1px 4px rgba(0, 0, 0, 0.05);
                    transform: translateY(-0.5px);

                    &::before {
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    }

                    &:hover {
                        background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                        box-shadow: 0 4px 10px rgba(249, 115, 22, 0.4), 0 2px 6px rgba(0, 0, 0, 0.08);
                        transform: translateY(-1px);
                    }
                }
            }
        }

        .date-range {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: nowrap;

            .date-picker {
                width: 187px; // 从140px增加到187px（加宽三分之一）

                @media (max-width: 768px) {
                    width: 160px; // 移动端也相应增加
                }
            }

            .date-separator {
                font-size: 0.75rem;
                color: #64748b;
                font-weight: 500;
                margin: 0 0.25rem;
                flex-shrink: 0;
            }
        }
    }

    /* 订单金额列移动端适配 */
    .order-amount-cell {
        .order-amount {
            font-size: 1rem;
            font-weight: 600;
        }

        .order-update-time {
            font-size: 0.6875rem;
            margin-top: 0.125rem;
        }
    }

    /* 状态标签移动端适配 */
    .el-tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    /* 移动端商品图片适配 */
    .product-image-container {
        width: 2rem !important;
        height: 2rem !important;

        /* 移动端禁用悬停放大效果 */
        &:hover {
            transform: none !important;
            box-shadow: none !important;
            border-color: #e5e7eb !important;
            z-index: auto !important;

            .product-image-small {
                box-shadow: none !important;
            }
        }
    }

    .product-image-small {
        border-radius: 0.25rem;
        transition: transform 0.2s ease;
    }

    .product-image-large {
        width: 3rem;
        height: 3rem;
        display: block;
        margin: 0 auto;
    }

    /* 商品详情移动端适配 */
    .product-name-detail {
        font-size: 0.875rem;
        line-height: 1.3;
    }

    .product-specs-detail {
        font-size: 0.6875rem;
    }

    .product-sku-detail {
        font-size: 0.6875rem;
    }

    .product-price-detail {
        font-size: 0.8125rem;
    }

    .product-quantity-detail {
        font-size: 0.8125rem;
    }

    /* 商品操作按钮移动端适配 - 现代化设计 */
    .product-actions-detail {
        flex-direction: column !important;
        gap: 0.5rem;
        max-width: 120px;
        padding: 0.5rem 0.375rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 0.5rem;
        border: 1px solid rgba(226, 232, 240, 0.5);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

        &:hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            transform: translateY(-0.5px);
        }

        .el-button {
            width: 100% !important;
            height: 30px !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            padding: 4px 6px !important;
            border-radius: 0.375rem !important;
            min-width: 85px !important;
            max-width: 110px !important;
            line-height: 1.2 !important;
            border: 1px solid transparent !important;
            transition: all 0.25s ease !important;
            position: relative !important;
            overflow: hidden !important;

            /* 移动端按钮光效 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.4s ease;
            }

            &:hover::before {
                left: 100%;
            }

            /* 移动端按钮样式继承桌面端样式，只调整阴影强度 */
            &.el-button--primary,
            &.el-button--success {
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

                &:hover {
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
                    transform: translateY(-1px) !important;
                }
            }
        }
    }

    /* 订单操作按钮移动端适配 */
    .order-actions-cell {
        gap: 0.375rem;
        min-height: 65px;
        padding: 0.375rem 0.25rem;
    }

    .action-buttons-row {
        flex-direction: column !important;
        gap: 0.375rem;
        align-items: center !important;
        max-width: 90px;
        margin: 0 auto !important;

        .el-button {
            width: 100% !important;
            height: 26px !important;
            font-size: 0.6875rem !important;
            padding: 3px 5px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 75px !important;
            max-width: 90px !important;
            box-sizing: border-box !important;
            margin: 0 !important;
            line-height: 1.2 !important;
        }
    }

    /* 展开内容移动端适配 */
    .expanded-content {
        padding: 0.5rem;
    }

    .product-details-table {
        font-size: 0.8125rem;

        .el-table__cell {
            padding: 0.375rem 0.25rem;
        }
    }

    /* 空状态移动端适配 */
    .empty-state {
        padding: 2.5rem 1rem;
        margin-bottom: 1rem;

        .empty-icon .empty-icon-svg {
            width: 4rem;
            height: 4rem;
        }

        .empty-content {
            h3 {
                font-size: 1.25rem;
                margin-bottom: 0.75rem;
            }

            p {
                font-size: 0.95rem;
                margin-bottom: 2rem;
                padding: 0 0.5rem;
            }
        }

        .empty-actions {
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;

            .btn {
                width: 100%;
                max-width: 200px;
                justify-content: center;
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;

                &:hover {
                    transform: translateY(-1px) scale(1.02);
                }
            }
        }
    }

    /* 分页器移动端适配 */
    .pagination-container {
        margin-top: 1rem;

        .el-pagination {
            justify-content: center;

            .el-pagination__total,
            .el-pagination__jump {
                display: none;
                /* 移动端隐藏总数和跳转 */
            }
        }
    }

    /* 对话框移动端适配 */
    .el-dialog {
        width: 95% !important;
        margin: 5vh auto !important;
    }

    .cancel-reason-form {
        .form-item {
            margin-bottom: 1rem;

            .form-label {
                font-size: 0.875rem;
                margin-bottom: 0.5rem;
            }
        }
    }
}

/* 移动端toggle-icon适配 */
@media (max-width: 768px) {
    .toggle-icon {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 1.125rem;
    }

    /* 移动端复制按钮适配 */
    .copy-order-btn {
        width: 1.25rem;
        height: 1.25rem;
        margin-left: 0.375rem;

        .copy-icon {
            width: 0.75rem;
            height: 0.75rem;
        }
    }
}

/* 超小屏幕适配 (480px以下) */
@media (max-width: 480px) {
    .orders-container {
        padding: 0.5rem;
    }

    .page-header {
        .page-title {
            font-size: 1.5rem;
        }
    }

    .toggle-icon {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 1rem;
    }

    /* 超小屏幕复制按钮适配 */
    .copy-order-btn {
        width: 1rem;
        height: 1rem;
        margin-left: 0.25rem;
        border-radius: 0.25rem;

        .copy-icon {
            width: 0.625rem;
            height: 0.625rem;
        }
    }

    .filters-section {
        padding: 0.75rem;
    }

    .orders-table {
        min-width: 700px;
        /* 超小屏幕减少最小宽度 */
    }

    .batch-operations {
        flex-direction: column;
        align-items: stretch;

        .el-button {
            width: 100%;
            justify-content: center;
        }
    }

    .quick-buttons {

        /* 超小屏幕快捷时间按钮 - 现代化美观设计 */
        .quick-btn {
            flex: 1;
            min-width: 0;
            font-size: 0.6875rem;
            padding: 0.375rem 0.625rem;
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 0.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            color: #475569;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            position: relative;
            overflow: hidden;
            min-height: 32px;

            /* 超小屏幕按钮光效 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
                transition: left 0.4s ease;
            }

            &:hover::before {
                left: 100%;
            }

            &:hover {
                border-color: rgba(249, 115, 22, 0.4);
                background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                color: #f97316;
                transform: translateY(-0.5px);
                box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
            }

            &.active {
                border-color: #ea580c;
                background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
                color: white;
                box-shadow: 0 2px 6px rgba(249, 115, 22, 0.3), 0 1px 3px rgba(0, 0, 0, 0.05);
                transform: translateY(-0.5px);

                &::before {
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                }

                &:hover {
                    background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                    box-shadow: 0 3px 8px rgba(249, 115, 22, 0.4), 0 2px 4px rgba(0, 0, 0, 0.08);
                    transform: translateY(-1px);
                }
            }
        }
    }

    .date-range {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.375rem;
        flex-wrap: nowrap;

        .date-picker {
            width: 45%;
            min-width: 120px;
            flex-shrink: 0;
        }

        .date-separator {
            display: block;
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
            margin: 0 0.25rem;
            flex-shrink: 0;
        }
    }
}

/* 订单操作下拉菜单样式 */
:deep(.el-dropdown-menu) {
    border-radius: 0.75rem;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(12px);
    padding: 0.5rem 0;
    min-width: 180px;

    .el-dropdown-menu__item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.875rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 0.5rem;
        margin: 0.125rem 0.5rem;
        position: relative;
        overflow: hidden;

        /* 菜单项光效 */
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.4s ease;
        }

        &:hover::before {
            left: 100%;
        }

        .dropdown-icon {
            font-size: 1.125rem;
            width: 1.25rem;
            height: 1.25rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        &:hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: #1e293b;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

            .dropdown-icon {
                transform: scale(1.1);
            }
        }

        &.action-pay {
            &:hover {
                background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                color: #16a34a;
                border-left: 3px solid #16a34a;

                .dropdown-icon {
                    color: #16a34a;
                    transform: scale(1.1) rotate(5deg);
                }
            }
        }

        &.action-cancel {
            &:hover {
                background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
                color: #dc2626;
                border-left: 3px solid #dc2626;

                .dropdown-icon {
                    color: #dc2626;
                    transform: scale(1.1) rotate(-5deg);
                }
            }
        }

        &.action-refund {
            &:hover {
                background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
                color: #d97706;
                border-left: 3px solid #d97706;

                .dropdown-icon {
                    color: #d97706;
                    transform: scale(1.1) rotate(5deg);
                }
            }
        }

        &.action-detail {
            &:hover {
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                color: #2563eb;
                border-left: 3px solid #2563eb;

                .dropdown-icon {
                    color: #2563eb;
                    transform: scale(1.1);
                }
            }
        }

        &.action-reorder {
            &:hover {
                background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
                color: #7c3aed;
                border-left: 3px solid #7c3aed;

                .dropdown-icon {
                    color: #7c3aed;
                    transform: scale(1.1) rotate(360deg);
                }
            }
        }

        &:active {
            transform: translateX(2px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }
    }
}


/* 时间筛选组显示控制 - 响应式布局 */
/* 2K分辨率以上：显示第一行中的时间筛选，隐藏第二行 */
@media (min-width: 2560px) {
    .time-group-inline {
        display: flex;
    }

    .filters-row-two .time-group {
        display: none;
    }
}

/* 2K分辨率以下：隐藏第一行中的时间筛选，显示第二行 */
@media (max-width: 2559px) {
    .time-group-inline {
        display: none;
    }

    .filters-row-two .time-group {
        display: flex;
    }
}

/* 图片预览模态框样式 */
.image-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    animation: fadeIn 0.3s ease-out;
    transition: all 0.3s ease-out;

    .image-preview-container {
        position: relative;
        max-width: 90vw;
        max-height: 90vh;
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        cursor: default;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        transition: all 0.3s ease-out;
        transform: scale(0.95);
        animation: scaleIn 0.3s ease-out forwards;

        &:hover {
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5);
            transform: translateY(-5px);
        }

        .image-preview-large {
            display: block;
            max-width: 80vw;
            max-height: 80vh;
            width: auto;
            height: auto;
            object-fit: contain;
            transition: all 0.3s ease-out;
        }

        .image-preview-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 2.5rem;
            height: 2.5rem;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.25rem;
            font-weight: bold;
            transition: all 0.3s ease-out;
            backdrop-filter: blur(10px);

            &:hover {
                background: rgba(220, 38, 38, 0.8);
                transform: scale(1.1);
            }
        }
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* 移动端图片预览优化 */
@media (max-width: 768px) {
    .image-preview-modal {
        .image-preview-container {
            max-width: 95vw;
            max-height: 95vh;
            border-radius: 0.75rem;

            .image-preview-large {
                max-width: 90vw;
                max-height: 90vh;
            }

            .image-preview-close {
                width: 2rem;
                height: 2rem;
                top: 0.5rem;
                right: 0.5rem;
                font-size: 1rem;
            }
        }
    }
}
}
</style>
