/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.GoodsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.MessageTypeFactory;
import com.fulfillmen.support.alibaba.webhook.data.GoodsMessage;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 阿里巴巴回调处理公共服务
 *
 * <pre>
 * 提供公共的webhook日志处理逻辑，避免在定时任务类中重复代码：
 * 1. 消息事件重构
 * 2. 消息类型检查
 * 3. 业务处理调用
 * 4. 状态更新逻辑
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/1/3
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlibabaCallbackProcessingService {

    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final OrderWebhookService orderWebhookService;

    /**
     * 处理单个webhook回调记录
     *
     * @param callbackLog    webhook回调日志记录
     * @param processingType 处理类型（用于日志区分）
     * @return 处理结果
     */
    public CallbackProcessingResult processCallback(SysAlibabaCallbackLogs callbackLog, String processingType) {
        String bizId = callbackLog.getOrderId();
        Long logId = callbackLog.getId();
        String eventType = callbackLog.getEventType();

        try {
            log.debug("开始处理{}回调: logId={}, bizId={}, eventType={}", processingType, logId, bizId, eventType);
            CallbackMessageType messageType = MessageTypeFactory.fromValue(eventType);
            if (Objects.isNull(messageType)) {
                return CallbackProcessingResult.skipped(bizId, "不支持的消息类型: " + eventType);
            }
            // 根据消息类型分发处理
            switch (messageType) {
                case GoodsMessageTypeEnums goodsMessageTypeEnums:
                    log.warn("商品消息类型暂不支持 : [{}] ", goodsMessageTypeEnums);
                    break;
                case OrderMessageTypeEnums orderMessageTypeEnums:
                    MessageEvent<OrderMessage> messageEvent = reconstructMessageEvent(callbackLog);
                    OrderMessage orderMessage = messageEvent.getData();
                    orderWebhookService.processOrderWebhook(orderMessage, messageEvent, orderMessageTypeEnums);
                    break;
                case LogisticsMessageTypeEnums logisticsMessageTypeEnums:
                    log.warn("物流消息类型暂不支持 : [{}] ", logisticsMessageTypeEnums);
                    break;
                default:
                    return CallbackProcessingResult.skipped(bizId, "不支持的消息类型: " + eventType);
            }

            log.debug("{} 回调处理成功: logId={}, bizId={}, eventType={}", processingType, logId, bizId, eventType);
            return CallbackProcessingResult.success(bizId, "处理成功");
        } catch (Exception e) {
            String reason = processingType + "处理失败: " + e.getMessage();
            log.error("{} 回调处理异常: logId={}, bizId={}, eventType={}", processingType, logId, bizId, eventType, e);
            return CallbackProcessingResult.failed(bizId, reason);
        }
    }

    /**
     * 批量标记订单的所有历史未处理记录为已处理
     *
     * @param orderId              订单ID
     * @param latestGmtBornVirtual 最新的业务时间戳
     * @return 更新的记录数量
     */
    public int batchMarkOrderProcessed(Long orderId, Long latestGmtBornVirtual) {
        return callbackLogsRepository.markOrderProcessedByLatestTimestamp(orderId, latestGmtBornVirtual);
    }

    /**
     * 标记单个记录为成功
     *
     * @param logId 日志ID
     */
    public void markSuccess(Long logId) {
        callbackLogsRepository.markSuccess(logId);
    }

    /**
     * 标记单个记录为失败
     *
     * @param logId  日志ID
     * @param reason 失败原因
     */
    public void markFailed(Long logId, String reason) {
        callbackLogsRepository.markFailed(logId, reason);
    }

    /**
     * 标记单个记录为已处理
     *
     * @param logId 日志ID
     */
    public void markProcessed(Long logId) {
        callbackLogsRepository.markProcessed(logId);
    }

    /**
     * 更新重试次数
     *
     * @param logId      日志ID
     * @param retryCount 重试次数
     */
    public void updateRetryCount(Long logId, int retryCount) {
        callbackLogsRepository.updateRetryCount(logId, retryCount);
    }

    /**
     * 从回调日志重构消息事件
     *
     * @param callbackLog 回调日志记录
     * @return 重构的消息事件
     */
    private MessageEvent<OrderMessage> reconstructMessageEvent(SysAlibabaCallbackLogs callbackLog) {
        try {
            // 尝试从元数据中解析原始事件
            String metadata = callbackLog.getMetadata();
            if (metadata != null) {
                // 手动解析 JSON 并重构 MessageEvent
                JsonNode jsonNode = JacksonUtil.convertToBean(metadata, JsonNode.class);
                if (jsonNode != null) {
                    MessageEvent<OrderMessage> event = new MessageEvent<>();

                    // 设置基本字段
                    event.setMsgId("process-" + callbackLog.getId());
                    event.setReceivedAt(callbackLog.getReceivedTimestamp());

                    if (jsonNode.has("msgId")) {
                        event.setMsgId(jsonNode.get("msgId").asText());
                    }
                    if (jsonNode.has("gmtBorn")) {
                        event.setGmtBorn(jsonNode.get("gmtBorn").asLong());
                    } else if (callbackLog.getGmtBornVirtual() != null) {
                        event.setGmtBorn(callbackLog.getGmtBornVirtual());
                    }
                    if (jsonNode.has("userInfo")) {
                        event.setUserInfo(jsonNode.get("userInfo").asText());
                    }
                    if (jsonNode.has("rawData")) {
                        event.setRawData(jsonNode.get("rawData").asText());
                    }

                    // 处理消息类型 - 优先使用日志中保存的事件类型
                    String eventType = callbackLog.getEventType();
                    if (eventType != null) {
                        CallbackMessageType messageType = MessageTypeFactory.fromValue(eventType);
                        event.setType(messageType);
                    } else if (jsonNode.has("type")) {
                        String typeString = jsonNode.get("type").asText();
                        CallbackMessageType messageType = MessageTypeFactory.fromValue(typeString);
                        event.setType(messageType);
                    }

                    // 处理订单数据
                    OrderMessage orderMessage = null;
                    if (event.getType() instanceof OrderMessageTypeEnums) {
                        JsonNode dataNode = jsonNode.get("data");
                        if (dataNode != null) {
                            orderMessage = JacksonUtil.convertToBean(dataNode.toString(), OrderMessage.class);
                        }
                    } else if (event.getType() instanceof LogisticsMessageTypeEnums) {
                        // 物流消息暂不处理，直接忽略
                        log.debug("跳过物流消息处理: logId={}", callbackLog.getId());
                    } else if (event.getType() instanceof GoodsMessage) {
                        // 商品消息暂不处理，直接忽略
                        log.debug("跳过商品消息处理: logId={}", callbackLog.getId());
                    }

                    if (orderMessage == null) {
                        if (jsonNode.has("data")) {
                            JsonNode dataNode = jsonNode.get("data");
                            orderMessage = JacksonUtil.convertToBean(dataNode.toString(), OrderMessage.class);
                        } else {
                            // 如果没有 data 节点，尝试直接解析整个 metadata 为 OrderMessage
                            orderMessage = JacksonUtil.convertToBean(metadata, OrderMessage.class);
                        }
                    }

                    // 确保订单ID正确设置
                    if (orderMessage != null && orderMessage.getOrderId() == null && callbackLog.getOrderId() != null) {
                        orderMessage.setOrderId(Long.valueOf(callbackLog.getOrderId()));
                    }

                    event.setData(orderMessage);
                    return event;
                }
            }
        } catch (Exception e) {
            log.warn("重构消息事件失败: logId={}, metadata={}", callbackLog.getId(),
                callbackLog.getMetadata() != null
                    ? callbackLog.getMetadata().substring(0, Math.min(100, callbackLog.getMetadata().length()))
                    : "null",
                e);
        }

        // 回退方案：返回基本的事件对象
        log.debug("使用回退方案重构消息事件: logId={}, orderId={}, eventType={}",
            callbackLog.getId(), callbackLog.getOrderId(), callbackLog.getEventType());

        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("fallback-" + callbackLog.getId());
        event.setReceivedAt(
            callbackLog.getReceivedTimestamp() != null ? callbackLog.getReceivedTimestamp() : LocalDateTime.now());

        if (callbackLog.getGmtBornVirtual() != null) {
            event.setGmtBorn(callbackLog.getGmtBornVirtual());
        }

        // 使用 MessageTypeFactory 设置消息类型
        if (callbackLog.getEventType() != null) {
            CallbackMessageType messageType = MessageTypeFactory.fromValue(callbackLog.getEventType());
            event.setType(messageType);
        }

        // 构建基本的订单消息
        OrderMessage orderMessage = new OrderMessage();
        if (callbackLog.getOrderId() != null) {
            orderMessage.setOrderId(Long.valueOf(callbackLog.getOrderId()));
        }
        event.setData(orderMessage);

        return event;
    }

    /**
     * 回调处理状态枚举
     */
    public enum CallbackProcessingStatus {
        // 成功处理
        SUCCESS,
        // 跳过（状态一致或不支持的消息类型）
        SKIPPED,
        // 处理失败
        FAILED
    }

    /**
     * 回调处理结果
     */
    @Getter
    public static class CallbackProcessingResult {

        private final CallbackProcessingStatus status;
        private final String message;
        private final String bizId;

        private CallbackProcessingResult(CallbackProcessingStatus status, String message, String bizId) {
            this.status = status;
            this.message = message;
            this.bizId = bizId;
        }

        public static CallbackProcessingResult success(String bizId, String message) {
            return new CallbackProcessingResult(CallbackProcessingStatus.SUCCESS, message, bizId);
        }

        public static CallbackProcessingResult failed(String bizId, String message) {
            return new CallbackProcessingResult(CallbackProcessingStatus.FAILED, message, bizId);
        }

        public static CallbackProcessingResult skipped(String bizId, String message) {
            return new CallbackProcessingResult(CallbackProcessingStatus.SKIPPED, message, bizId);
        }

    }
}
