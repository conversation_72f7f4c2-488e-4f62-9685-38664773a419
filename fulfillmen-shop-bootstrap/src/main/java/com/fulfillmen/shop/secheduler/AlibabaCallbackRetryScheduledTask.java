/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.notification.service.WeChatNotificationService;
import com.fulfillmen.shop.secheduler.properties.AlibabaCallbackProperties;
import com.fulfillmen.shop.secheduler.service.AlibabaCallbackProcessingService;
import com.fulfillmen.shop.secheduler.service.AlibabaCallbackProcessingService.CallbackProcessingResult;
import com.fulfillmen.shop.secheduler.service.AlibabaCallbackProcessingService.CallbackProcessingStatus;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 阿里巴巴回调重试定时任务
 *
 * <pre>
 * TODO: 2025 年 08 月 13 日 10:28:06 需要等完善部分功能模块后，才能开启处理。 当前的功能尚未完善，还存在一些业务代码逻辑上的缺陷
 * FIXBUG: 2025/8/18 : 1. 状态上来说，存在一些问题，如果最新的状态是成功的。则失败的逻辑可以忽略。
 * 1. 业务代码里，订单ID 存在多个事件乱序。需要合并后在处理。
 * 功能说明：
 * 1. 定期扫描失败的回调记录（最多重试3次）
 * 2. 重新执行失败的回调处理逻辑
 * 3. 达到最大重试次数后发送企业微信通知
 * 4. 使用分布式锁防止多实例并发执行
 *
 * 配置项：
 * - shop.alibaba.callback-retry.max-retry-count: 最大重试次数（默认3次）
 * - shop.alibaba.callback-retry.batch-size: 每批处理数量（默认20条）
 * - shop.alibaba.callback-retry.time-window-hours: 时间窗口小时数（默认24小时）
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/09 12:10
 * @since 1.0.0
 */
@Slf4j
@Profile({"!dev", "!local"})
//@Component
@RequiredArgsConstructor
public class AlibabaCallbackRetryScheduledTask {

    private static final String RETRY_LOCK_KEY = "scheduled:alibaba:callback:retry";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final AlibabaCallbackProcessingService callbackProcessingService;
    private final WeChatNotificationService weChatNotificationService;
    //    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final Executor virtualThreadExecutor;
    private final AlibabaCallbackProperties alibabaCallbackProperties;
    @Value("${shop.alibaba.callback-retry.max-retry-count:3}")
    private int maxRetryCount;
    @Value("${shop.alibaba.callback-retry.batch-size:20}")
    private int batchSize;

    // 移除不需要的依赖，状态一致性检查将在OrderWebhookService中处理
    @Value("${shop.alibaba.callback-retry.time-window-hours:24}")
    private int timeWindowHours;
    @Value("${shop.alibaba.callback-retry.enabled:true}")
    private boolean retryEnabled;

    /**
     * 每5分钟执行一次失败记录重试处理 - 专门处理失败的记录
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void scheduledProcessFailedCallbacks() {
        if (!retryEnabled) {
            log.debug("阿里巴巴回调重试功能已禁用");
            return;
        }

        // 分布式锁，避免多实例并发执行
        boolean locked = RedisUtils.tryLock(RETRY_LOCK_KEY, 600_000L, 0L);
        if (!locked) {
            log.info("阿里巴巴重试任务跳过：未获取到分布式锁");
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalProcessed = 0;
        int totalSkipped = 0;
        int totalFailed = 0;

        try {
            log.info("开始执行阿里巴巴失败记录重试任务，时间窗口：{}小时，批次大小：{}", timeWindowHours, batchSize);

            // 查询需要重试的失败记录（重试次数小于最大重试次数）
            List<SysAlibabaCallbackLogs> failedLogs = callbackLogsRepository.findFailedLogsForRetry(maxRetryCount, timeWindowHours, batchSize);

            if (failedLogs.isEmpty()) {
                log.debug("没有需要重试的失败记录");
                return;
            }

            log.info("找到 {} 条失败记录需要重试", failedLogs.size());

            // 并行处理失败记录重试
            @SuppressWarnings("unchecked") CompletableFuture<RetryProcessingResult>[] processingTasks = failedLogs.stream()
                .map(this::processFailedLogRetryAsync)
                .toArray(CompletableFuture[]::new);

            // 等待所有处理任务完成
            CompletableFuture.allOf(processingTasks).join();

            // 统计结果
            for (CompletableFuture<RetryProcessingResult> task : processingTasks) {
                try {
                    RetryProcessingResult result = task.get();
                    switch (result.status()) {
                        case SUCCESS -> totalProcessed++;
                        case SKIPPED -> totalSkipped++;
                        case FAILED -> totalFailed++;
                    }
                } catch (Exception e) {
                    totalFailed++;
                    log.error("获取处理结果异常", e);
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("阿里巴巴重试任务完成：成功处理={}, 跳过={}, 失败={}, 耗时={}ms", totalProcessed, totalSkipped, totalFailed, duration);

        } catch (Exception e) {
            log.error("阿里巴巴重试任务执行异常", e);
            // 发送系统异常通知
            weChatNotificationService.sendSystemErrorNotification("重试任务异常", "阿里巴巴重试定时任务执行异常：" + e.getMessage());
        } finally {
            RedisUtils.unlock(RETRY_LOCK_KEY);
        }
    }

    /**
     * 异步处理失败记录重试
     */
    private CompletableFuture<RetryProcessingResult> processFailedLogRetryAsync(SysAlibabaCallbackLogs failedLog) {
        return CompletableFuture.supplyAsync(() -> {
            String bizId = failedLog.getOrderId();
            Long logId = failedLog.getId();
            int currentRetryCount = failedLog.getRetryCount() != null ? failedLog.getRetryCount() : 0;

            try {
                log.info("开始重试失败记录: bizId={}, logId={}, currentRetryCount={}", bizId, logId, currentRetryCount);

                // 检查是否超过最大重试次数
                if (currentRetryCount >= maxRetryCount) {
                    log.warn("记录已达到最大重试次数，跳过: logId={}, currentRetryCount={}, maxRetryCount={}", logId, currentRetryCount, maxRetryCount);
                    return new RetryProcessingResult(CallbackProcessingStatus.SKIPPED, "达到最大重试次数", bizId);
                }

                // 更新重试次数
                callbackProcessingService.updateRetryCount(logId, currentRetryCount + 1);

                // 使用公共服务处理回调
                CallbackProcessingResult result = callbackProcessingService.processCallback(failedLog, "重试");

                switch (result.getStatus()) {
                    case SUCCESS -> {
                        // 标记为成功
                        callbackProcessingService.markSuccess(logId);
                        log.info("重试成功: bizId={}, logId={}, retryCount={}", bizId, logId, currentRetryCount + 1);
                        return new RetryProcessingResult(CallbackProcessingStatus.SUCCESS, "重试成功", bizId);
                    }
                    case FAILED -> {
                        // 标记失败
                        String failureReason = "重试失败: " + result.getMessage();
                        callbackProcessingService.markFailed(logId, failureReason);

                        // 检查是否需要发送通知
                        int newRetryCount = currentRetryCount + 1;
                        if (newRetryCount >= maxRetryCount) {
                            log.warn("记录达到最大重试次数，发送通知: logId={}, bizId={}, maxRetryCount={}", logId, bizId, maxRetryCount);
                            sendRetryFailedNotification(failedLog, newRetryCount, failureReason);
                        }

                        return new RetryProcessingResult(CallbackProcessingStatus.FAILED, failureReason, bizId);
                    }
                    case SKIPPED -> {
                        // 跳过的记录也标记为成功（通常是不支持的消息类型）
                        callbackProcessingService.markSuccess(logId);
                        log.info("重试跳过: bizId={}, logId={}, 原因={}", bizId, logId, result.getMessage());
                        return new RetryProcessingResult(CallbackProcessingStatus.SKIPPED, result.getMessage(), bizId);
                    }
                    default -> {
                        return new RetryProcessingResult(CallbackProcessingStatus.FAILED, "未知的处理状态", bizId);
                    }
                }

            } catch (Exception e) {
                log.error("重试异常: bizId={}, logId={}, retryCount={}", bizId, logId, currentRetryCount + 1, e);

                // 标记失败
                String failureReason = "重试异常: " + e.getMessage();
                callbackProcessingService.markFailed(logId, failureReason);

                // 检查是否需要发送通知
                int newRetryCount = currentRetryCount + 1;
                if (newRetryCount >= maxRetryCount) {
                    log.warn("记录达到最大重试次数，发送通知: logId={}, bizId={}, maxRetryCount={}", logId, bizId, maxRetryCount);
                    sendRetryFailedNotification(failedLog, newRetryCount, failureReason);
                }

                return new RetryProcessingResult(CallbackProcessingStatus.FAILED, failureReason, bizId);
            }
        }, virtualThreadExecutor);
    }

    /**
     * 发送重试失败通知
     */
    private void sendRetryFailedNotification(SysAlibabaCallbackLogs callbackLog, int retryCount, String failureReason) {
        try {
            String lastFailedTime = callbackLog.getGmtModified() != null
                ? callbackLog.getGmtModified().format(FORMATTER)
                : "未知";

            weChatNotificationService.sendCallbackRetryFailedNotification(
                callbackLog.getOrderId(),
                callbackLog.getEventType(),
                retryCount,
                failureReason,
                lastFailedTime);
        } catch (Exception e) {
            log.error("发送重试失败通知异常: logId={}", callbackLog.getId(), e);
        }
    }

    /**
     * 重试处理结果
     */
    @Builder
    public record RetryProcessingResult(
        CallbackProcessingStatus status,
        String message,
        String bizId
    ) {

    }

    // 移除重复的状态一致性检查逻辑，已移至OrderWebhookServiceImpl
}
