/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.order.consistency.OrderDataConsistencyManager;
import com.fulfillmen.shop.manager.core.order.status.OrderSyncStatusManager;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 订单状态监控器
 *
 * <p>定时监控订单状态异常、同步失败、长时间未处理等问题，并触发告警</p>
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 订单状态监控和告警系统
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatusMonitor {

    private final TzOrderSupplierMapper supplierOrderMapper;
    private final TzOrderPurchaseMapper purchaseOrderMapper;
    private final OrderSyncStatusManager syncStatusManager;
    private final OrderDataConsistencyManager consistencyManager;

    /**
     * 定时检查同步失败的订单（每5分钟执行一次）
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void monitorSyncFailures() {
        try {
            log.debug("开始监控同步失败订单");

            // 查询同步失败的供应商订单
            LambdaQueryWrapper<TzOrderSupplier> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzOrderSupplier::getExternalSyncStatus, OrderSupplierSyncStatusEnums.SYNC_FAILED)
                .orderByDesc(TzOrderSupplier::getGmtModified);

            List<TzOrderSupplier> failedOrders = supplierOrderMapper.selectList(queryWrapper);

            if (!failedOrders.isEmpty()) {
                log.warn("发现 {} 个同步失败的供应商订单", failedOrders.size());

                // 分析失败原因并生成监控报告
                SyncFailureReport report = analyzeSyncFailures(failedOrders);

                // 触发告警
                triggerSyncFailureAlert(report);

                log.info("同步失败监控完成，生成报告：{}", report.getSummary());
            } else {
                log.debug("未发现同步失败订单");
            }

        } catch (Exception e) {
            log.error("监控同步失败订单时发生异常", e);
        }
    }

    /**
     * 定时检查长时间未同步的订单（每10分钟执行一次）
     */
    @Scheduled(fixedRate = 600000) // 10分钟
    public void monitorPendingSyncOrders() {
        try {
            log.debug("开始监控长时间未同步订单");

            // 查询超过30分钟未同步的订单
            LocalDateTime threshold = LocalDateTime.now().minus(30, ChronoUnit.MINUTES);

            LambdaQueryWrapper<TzOrderSupplier> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzOrderSupplier::getExternalSyncStatus, OrderSupplierSyncStatusEnums.NOT_SYNCED)
                .lt(TzOrderSupplier::getGmtCreated, threshold)
                .in(TzOrderSupplier::getStatus,
                    TzOrderSupplierStatusEnum.PENDING_PAYMENT,
                    TzOrderSupplierStatusEnum.PENDING_SHIPMENT);

            List<TzOrderSupplier> pendingOrders = supplierOrderMapper.selectList(queryWrapper);

            if (!pendingOrders.isEmpty()) {
                log.warn("发现 {} 个长时间未同步的供应商订单", pendingOrders.size());

                // 生成监控报告
                PendingSyncReport report = analyzePendingSyncOrders(pendingOrders);

                // 触发告警
                triggerPendingSyncAlert(report);

                log.info("未同步订单监控完成，生成报告：{}", report.getSummary());
            } else {
                log.debug("未发现长时间未同步订单");
            }

        } catch (Exception e) {
            log.error("监控长时间未同步订单时发生异常", e);
        }
    }

    /**
     * 定时检查数据一致性问题（每小时执行一次）
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void monitorDataConsistency() {
        try {
            log.debug("开始监控数据一致性问题");

            // 查询近24小时内修改过的采购订单
            LocalDateTime threshold = LocalDateTime.now().minusHours(24);

            LambdaQueryWrapper<TzOrderPurchase> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                .gt(TzOrderPurchase::getGmtModified, threshold)
                .in(TzOrderPurchase::getOrderStatus,
                    TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED,
                    TzOrderPurchaseStatusEnum.PENDING_REVIEW,
                    TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED);

            List<TzOrderPurchase> recentOrders = purchaseOrderMapper.selectList(queryWrapper);

            log.info("检查 {} 个采购订单的数据一致性", recentOrders.size());

            List<ConsistencyIssueReport> issues = new ArrayList<>();
            int checkedCount = 0;
            int issueCount = 0;

            for (TzOrderPurchase order : recentOrders) {
                try {
                    var report = consistencyManager.checkAndRepairConsistency(order.getId());
                    checkedCount++;

                    if (report.isRepairNeeded()) {
                        issueCount++;
                        issues.add(ConsistencyIssueReport.builder()
                            .purchaseOrderNo(report.getPurchaseOrderNo())
                            .issueType(report.getIssueType().getDescription())
                            .repairSuccess(report.isRepairSuccess())
                            .checkTime(report.getCheckTime())
                            .build());
                    }
                } catch (Exception e) {
                    log.warn("检查采购订单 {} 一致性时发生异常", order.getPurchaseOrderNo(), e);
                }
            }

            if (!issues.isEmpty()) {
                log.warn("发现 {} 个数据一致性问题", issueCount);
                triggerConsistencyAlert(issues, checkedCount, issueCount);
            }

            log.info("数据一致性监控完成：检查 {} 个订单，发现 {} 个问题", checkedCount, issueCount);

        } catch (Exception e) {
            log.error("监控数据一致性时发生异常", e);
        }
    }

    /**
     * 定时生成系统健康报告（每天执行一次）
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void generateHealthReport() {
        try {
            log.info("开始生成系统健康报告");

            SystemHealthReport report = buildSystemHealthReport();

            log.info("系统健康报告生成完成：{}", report.getSummary());

            // 如果发现严重问题，触发告警
            if (report.hasCriticalIssues()) {
                triggerCriticalHealthAlert(report);
            }

        } catch (Exception e) {
            log.error("生成系统健康报告时发生异常", e);
        }
    }

    /**
     * 分析同步失败原因
     */
    private SyncFailureReport analyzeSyncFailures(List<TzOrderSupplier> failedOrders) {
        Map<String, Integer> failureReasons = new HashMap<>();
        List<String> criticalOrders = new ArrayList<>();

        for (TzOrderSupplier order : failedOrders) {
            // 分析失败原因（从metadataJson中提取）
            String metadata = order.getMetadataJson();
            String reason = extractFailureReason(metadata);
            failureReasons.merge(reason, 1, Integer::sum);

            // 判断是否为关键订单（创建时间超过1小时）
            if (order.getGmtCreated().isBefore(LocalDateTime.now().minus(1, ChronoUnit.HOURS))) {
                criticalOrders.add(order.getSupplierOrderNo());
            }
        }

        return SyncFailureReport.builder()
            .totalFailedCount(failedOrders.size())
            .failureReasons(failureReasons)
            .criticalOrderCount(criticalOrders.size())
            .criticalOrders(criticalOrders)
            .reportTime(LocalDateTime.now())
            .build();
    }

    /**
     * 分析长时间未同步订单
     */
    private PendingSyncReport analyzePendingSyncOrders(List<TzOrderSupplier> pendingOrders) {
        Map<TzOrderSupplierStatusEnum, Integer> statusDistribution = pendingOrders.stream()
            .collect(Collectors.groupingBy(
                TzOrderSupplier::getStatus,
                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)));

        List<String> urgentOrders = pendingOrders.stream()
            .filter(order -> order.getGmtCreated().isBefore(
                LocalDateTime.now().minusHours(2)))
            .map(TzOrderSupplier::getSupplierOrderNo)
            .collect(Collectors.toList());

        return PendingSyncReport.builder()
            .totalPendingCount(pendingOrders.size())
            .statusDistribution(statusDistribution)
            .urgentOrderCount(urgentOrders.size())
            .urgentOrders(urgentOrders)
            .reportTime(LocalDateTime.now())
            .build();
    }

    /**
     * 构建系统健康报告
     */
    private SystemHealthReport buildSystemHealthReport() {
        // 统计各类订单数量
        Map<String, Object> statistics = new HashMap<>();

        // 统计同步状态分布
        LambdaQueryWrapper<TzOrderSupplier> syncQuery = new LambdaQueryWrapper<>();
        // 不需要显式声明 isDeleted，MP逻辑删除插件自动处理
        List<TzOrderSupplier> allSupplierOrders = supplierOrderMapper.selectList(syncQuery);

        Map<OrderSupplierSyncStatusEnums, Long> syncStatusCount = allSupplierOrders.stream()
            .collect(Collectors.groupingBy(
                TzOrderSupplier::getExternalSyncStatus,
                Collectors.counting()));

        statistics.put("syncStatusDistribution", syncStatusCount);
        statistics.put("totalSupplierOrders", allSupplierOrders.size());

        // 计算同步成功率
        long syncedCount = syncStatusCount.getOrDefault(OrderSupplierSyncStatusEnums.SYNCED, 0L);
        double syncSuccessRate = allSupplierOrders.isEmpty() ? 0.0 : (double) syncedCount / allSupplierOrders.size() * 100;

        statistics.put("syncSuccessRate", syncSuccessRate);

        // 判断是否有严重问题
        boolean hasCriticalIssues = syncSuccessRate < 95.0 ||
            syncStatusCount.getOrDefault(OrderSupplierSyncStatusEnums.SYNC_FAILED, 0L) > 10;

        return SystemHealthReport.builder()
            .reportTime(LocalDateTime.now())
            .statistics(statistics)
            .syncSuccessRate(syncSuccessRate)
            .hasCriticalIssues(hasCriticalIssues)
            .build();
    }

    /**
     * 从元数据中提取失败原因
     */
    private String extractFailureReason(String metadataJson) {
        if (metadataJson == null || metadataJson.trim().isEmpty()) {
            return "未知原因";
        }

        // 简单的字符串匹配提取失败原因
        if (metadataJson.contains("网络")) {
            return "网络异常";
        } else if (metadataJson.contains("超时")) {
            return "请求超时";
        } else if (metadataJson.contains("权限")) {
            return "权限不足";
        } else if (metadataJson.contains("参数")) {
            return "参数错误";
        } else if (metadataJson.contains("阿里巴巴")) {
            return "阿里巴巴接口异常";
        } else {
            return "系统异常";
        }
    }

    /**
     * 触发同步失败告警
     */
    private void triggerSyncFailureAlert(SyncFailureReport report) {
        String alertLevel = report.getCriticalOrderCount() > 5 ? "CRITICAL" : "WARNING";

        log.warn("[{}] 同步失败告警 - 总计：{} 个，关键：{} 个，主要原因：{}",
            alertLevel, report.getTotalFailedCount(), report.getCriticalOrderCount(),
            report.getFailureReasons().entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("未知"));

        // 这里可以集成实际的告警系统，如发送邮件、钉钉、短信等
        // alertService.sendAlert(AlertType.SYNC_FAILURE, report);
    }

    /**
     * 触发未同步订单告警
     */
    private void triggerPendingSyncAlert(PendingSyncReport report) {
        String alertLevel = report.getUrgentOrderCount() > 3 ? "CRITICAL" : "WARNING";

        log.warn("[{}] 未同步订单告警 - 总计：{} 个，紧急：{} 个",
            alertLevel, report.getTotalPendingCount(), report.getUrgentOrderCount());

        // alertService.sendAlert(AlertType.PENDING_SYNC, report);
    }

    /**
     * 触发数据一致性告警
     */
    private void triggerConsistencyAlert(List<ConsistencyIssueReport> issues, int checkedCount, int issueCount) {
        double issueRate = (double) issueCount / checkedCount * 100;
        String alertLevel = issueRate > 5.0 ? "CRITICAL" : "WARNING";

        log.warn("[{}] 数据一致性告警 - 检查：{} 个，问题：{} 个，问题率：{:.2f}%",
            alertLevel, checkedCount, issueCount, issueRate);

        // alertService.sendAlert(AlertType.DATA_CONSISTENCY, issues);
    }

    /**
     * 触发系统健康关键告警
     */
    private void triggerCriticalHealthAlert(SystemHealthReport report) {
        log.error("[CRITICAL] 系统健康告警 - 同步成功率：{:.2f}%，存在严重问题",
            report.getSyncSuccessRate());

        // alertService.sendAlert(AlertType.SYSTEM_HEALTH, report);
    }

    // ================== 报告数据结构 ==================

    /**
     * 同步失败报告
     */
    @Getter
    public static class SyncFailureReport {

        // Getters
        private int totalFailedCount;
        private Map<String, Integer> failureReasons;
        private int criticalOrderCount;
        private List<String> criticalOrders;
        private LocalDateTime reportTime;

        public static SyncFailureReportBuilder builder() {
            return new SyncFailureReportBuilder();
        }

        public String getSummary() {
            return String.format("同步失败报告：总计%d个，关键%d个", totalFailedCount, criticalOrderCount);
        }

        public static class SyncFailureReportBuilder {

            private int totalFailedCount;
            private Map<String, Integer> failureReasons;
            private int criticalOrderCount;
            private List<String> criticalOrders;
            private LocalDateTime reportTime;

            public SyncFailureReportBuilder totalFailedCount(int totalFailedCount) {
                this.totalFailedCount = totalFailedCount;
                return this;
            }

            public SyncFailureReportBuilder failureReasons(Map<String, Integer> failureReasons) {
                this.failureReasons = failureReasons;
                return this;
            }

            public SyncFailureReportBuilder criticalOrderCount(int criticalOrderCount) {
                this.criticalOrderCount = criticalOrderCount;
                return this;
            }

            public SyncFailureReportBuilder criticalOrders(List<String> criticalOrders) {
                this.criticalOrders = criticalOrders;
                return this;
            }

            public SyncFailureReportBuilder reportTime(LocalDateTime reportTime) {
                this.reportTime = reportTime;
                return this;
            }

            public SyncFailureReport build() {
                SyncFailureReport report = new SyncFailureReport();
                report.totalFailedCount = this.totalFailedCount;
                report.failureReasons = this.failureReasons;
                report.criticalOrderCount = this.criticalOrderCount;
                report.criticalOrders = this.criticalOrders;
                report.reportTime = this.reportTime;
                return report;
            }
        }

    }

    /**
     * 未同步订单报告
     */
    @Getter
    public static class PendingSyncReport {

        // Getters
        private int totalPendingCount;
        private Map<TzOrderSupplierStatusEnum, Integer> statusDistribution;
        private int urgentOrderCount;
        private List<String> urgentOrders;
        private LocalDateTime reportTime;

        public static PendingSyncReportBuilder builder() {
            return new PendingSyncReportBuilder();
        }

        public String getSummary() {
            return String.format("未同步报告：总计%d个，紧急%d个", totalPendingCount, urgentOrderCount);
        }

        public static class PendingSyncReportBuilder {

            private int totalPendingCount;
            private Map<TzOrderSupplierStatusEnum, Integer> statusDistribution;
            private int urgentOrderCount;
            private List<String> urgentOrders;
            private LocalDateTime reportTime;

            public PendingSyncReportBuilder totalPendingCount(int totalPendingCount) {
                this.totalPendingCount = totalPendingCount;
                return this;
            }

            public PendingSyncReportBuilder statusDistribution(Map<TzOrderSupplierStatusEnum, Integer> statusDistribution) {
                this.statusDistribution = statusDistribution;
                return this;
            }

            public PendingSyncReportBuilder urgentOrderCount(int urgentOrderCount) {
                this.urgentOrderCount = urgentOrderCount;
                return this;
            }

            public PendingSyncReportBuilder urgentOrders(List<String> urgentOrders) {
                this.urgentOrders = urgentOrders;
                return this;
            }

            public PendingSyncReportBuilder reportTime(LocalDateTime reportTime) {
                this.reportTime = reportTime;
                return this;
            }

            public PendingSyncReport build() {
                PendingSyncReport report = new PendingSyncReport();
                report.totalPendingCount = this.totalPendingCount;
                report.statusDistribution = this.statusDistribution;
                report.urgentOrderCount = this.urgentOrderCount;
                report.urgentOrders = this.urgentOrders;
                report.reportTime = this.reportTime;
                return report;
            }
        }

    }

    /**
     * 一致性问题报告
     */
    @Getter
    public static class ConsistencyIssueReport {

        // Getters
        private String purchaseOrderNo;
        private String issueType;
        private boolean repairSuccess;
        private LocalDateTime checkTime;

        public static ConsistencyIssueReportBuilder builder() {
            return new ConsistencyIssueReportBuilder();
        }

        public static class ConsistencyIssueReportBuilder {

            private String purchaseOrderNo;
            private String issueType;
            private boolean repairSuccess;
            private LocalDateTime checkTime;

            public ConsistencyIssueReportBuilder purchaseOrderNo(String purchaseOrderNo) {
                this.purchaseOrderNo = purchaseOrderNo;
                return this;
            }

            public ConsistencyIssueReportBuilder issueType(String issueType) {
                this.issueType = issueType;
                return this;
            }

            public ConsistencyIssueReportBuilder repairSuccess(boolean repairSuccess) {
                this.repairSuccess = repairSuccess;
                return this;
            }

            public ConsistencyIssueReportBuilder checkTime(LocalDateTime checkTime) {
                this.checkTime = checkTime;
                return this;
            }

            public ConsistencyIssueReport build() {
                ConsistencyIssueReport report = new ConsistencyIssueReport();
                report.purchaseOrderNo = this.purchaseOrderNo;
                report.issueType = this.issueType;
                report.repairSuccess = this.repairSuccess;
                report.checkTime = this.checkTime;
                return report;
            }
        }

    }

    /**
     * 系统健康报告
     */
    public static class SystemHealthReport {

        @Getter
        private LocalDateTime reportTime;
        @Getter
        private Map<String, Object> statistics;
        @Getter
        private double syncSuccessRate;
        private boolean hasCriticalIssues;

        public static SystemHealthReportBuilder builder() {
            return new SystemHealthReportBuilder();
        }

        public String getSummary() {
            return String.format("系统健康报告：同步成功率%.2f%%，%s",
                syncSuccessRate, hasCriticalIssues ? "存在严重问题" : "运行正常");
        }

        public static class SystemHealthReportBuilder {

            private LocalDateTime reportTime;
            private Map<String, Object> statistics;
            private double syncSuccessRate;
            private boolean hasCriticalIssues;

            public SystemHealthReportBuilder reportTime(LocalDateTime reportTime) {
                this.reportTime = reportTime;
                return this;
            }

            public SystemHealthReportBuilder statistics(Map<String, Object> statistics) {
                this.statistics = statistics;
                return this;
            }

            public SystemHealthReportBuilder syncSuccessRate(double syncSuccessRate) {
                this.syncSuccessRate = syncSuccessRate;
                return this;
            }

            public SystemHealthReportBuilder hasCriticalIssues(boolean hasCriticalIssues) {
                this.hasCriticalIssues = hasCriticalIssues;
                return this;
            }

            public SystemHealthReport build() {
                SystemHealthReport report = new SystemHealthReport();
                report.reportTime = this.reportTime;
                report.statistics = this.statistics;
                report.syncSuccessRate = this.syncSuccessRate;
                report.hasCriticalIssues = this.hasCriticalIssues;
                return report;
            }
        }

        public boolean hasCriticalIssues() {
            return hasCriticalIssues;
        }
    }
}