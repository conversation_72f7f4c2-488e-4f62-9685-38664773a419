# 订单状态流程设计文档

## 📋 文档概述

本文档详细描述了Fulfillmen Shop系统中订单状态流转的完整设计，包括采购订单、订单项和物流状态的协调机制。

## 🏗️ 三层状态体系架构

### 核心实体层次结构

```
TzOrderPurchase (采购订单) - 聚合根，客户视角
    ├── TzOrderSupplier (供应商订单) - 按供应商维度拆分
    │   ├── 履约状态: TzOrderSupplierStatusEnum (8个状态)
    │   ├── 外部同步: externalSyncStatus (对1688等平台)
    │   └── WMS同步: wmsSyncStatus (对WMS系统)
    │
    └── TzOrderItem (订单项) - 具体商品采购明细
        ├── 商品状态: TzOrderItemStatusEnum (7个状态)
        └── 物流状态: TzOrderItemLogisticsStatusEnum (6个状态)
```

### 状态枚举完整清单

- **TzOrderPurchaseStatusEnum**: 采购订单状态 (15个状态) - 客户可见状态
- **TzOrderSupplierStatusEnum**: 供应商订单状态 (8个状态) - 供应商履约维度
- **TzOrderItemStatusEnum**: 订单项状态 (7个状态) - 商品级别明细
- **TzOrderItemLogisticsStatusEnum**: 物流状态 (6个状态) - 物流跟踪
- **OrderSupplierSyncStatusEnums**: 同步状态 (3个状态) - 多系统同步

### 状态驱动机制

> 📌 **核心原理**: 采购订单状态由供应商订单状态聚合而来，供应商订单状态由订单项状态驱动更新

## 🔄 状态流转图

### 📊 采购订单状态生命周期

```mermaid
graph TD
    A[临时保存 0] --> B[待支付 1]
    B --> C[支付完成 2]
    C --> D[待审核 3]
    D --> E[采购中 4]
    E --> F[部分履约 5]
    F --> G[采购完成 6]
    G --> H[供应商已发货 7]
    H --> I[仓库待收货 8]
    I --> J[仓库已收货 9]
    J --> K[已入库 10]
    K --> L[已完成 13]

    M[代付款 12]
    N[退款中 14]
    O[已退款 15]

    B -.-> Z[已取消 11]
    C -.-> Z
    D -.-> Z
    E -.-> Z
    F -.-> Z
    G -.-> Z
    H -.-> Z
    I -.-> Z
    J -.-> Z
    K -.-> Z
```

### 📦 订单项状态生命周期

```mermaid
graph TD
    A[待处理 0] --> B[采购中 1]
    B --> C[已发货 2]
    C --> D[已送达 3]
    D --> E[已完成 4]

    F[失败 5]
    G[已取消 6]

    A -.-> G
    B -.-> F
    C -.-> F
    D -.-> F
    E -.-> F
```

### 🏪 供应商订单状态生命周期（关键驱动层）

```mermaid
graph TD
    A[待支付 0] --> B[待发货 1]
    B --> C[部分发货 2]
    B --> D[待收货 3]
    C --> D
    D --> E[等待仓库签收 4]
    E --> F[仓库签收 5]
    F --> G[已完成 6]

    H[已取消 7]

    A -.-> H
    B -.-> H
    C -.-> H
    D -.-> H
    E -.-> H
    F -.-> H

    style A fill:#fff2cc
    style B fill:#d5e8d4
    style G fill:#c8e6c9
    style H fill:#ffcdd2
```

**供应商订单状态说明**:

- **待支付(0)**: 财务确认向供应商支付前 → 对应1688 `waitbuyerpay`
- **待发货(1)**: 供应商确认订单，准备发货 → 对应1688 `waitsellersend`
- **部分发货(2)**: 供应商部分商品已发货
- **待收货(3)**: 供应商所有商品都已发货 → 对应1688 `waitbuyerreceive`
- **等待仓库签收(4)**: 货物已送达WMS仓库 → 对应1688 `confirm_goods`
- **仓库签收(5)**: WMS确认收货，等待质检入库
- **已完成(6)**: 货物质检完成并入库，供应商订单履约完成
- **已取消(7)**: 订单被取消，需要处理退款

### 🚚 物流状态流转

```mermaid
graph TD
    A[未创建 8] --> B[未发货 1]
    B --> C[已发货 2]
    C --> D[已收货 3]

    E[部分发货 5]
    F[已经退货 4]

    C -.-> F
    B --> E
```

## 🎯 三层状态映射矩阵

### 📊 完整状态关联表

| 采购订单状态    | 供应商订单状态                             | 订单项状态    | 物流状态   | 触发条件         | 客户可见含义  |
|-----------|-------------------------------------|----------|--------|--------------|---------|
| 待支付(1)    | 待支付(0)                              | 待处理(0)   | 未创建(8) | 客户下单未付款      | 请尽快完成支付 |
| 支付完成(2)   | 待支付(0)                              | 待处理(0)   | 未创建(8) | 客户完成付款       | 等待采购处理  |
| 采购中(4)    | 全部待支付(0)                            | 采购中(1)   | 未发货(1) | 采购员开始采购      | 正在采购商品  |
| 部分履约(5)   | **混合状态**:<br/>部分待发货(1)<br/>部分待支付(0) | 采购中(1)   | 未发货(1) | **部分供应商已付款** | 部分商品已采购 |
| 采购完成(6)   | 全部待发货(1)                            | 采购中(1)   | 未发货(1) | **所有供应商已付款** | 等待供应商发货 |
| 供应商已发货(7) | 全部待收货(3)+                           | 已发货(2)   | 已发货(2) | 供应商开始发货      | 商品运输中   |
| 仓库待收货(8)  | 等待仓库签收(4)                           | 已送达(3)   | 已收货(3) | 商品到达仓库       | 等待入库质检  |
| 仓库已收货(9)  | 仓库签收(5)                             | 已送达(3)   | 已收货(3) | 仓库已签收        | 商品质检中   |
| 已入库(10)   | 已完成(6)                              | 已完成(4)   | 已收货(3) | 质检完成上架       | 商品已入库   |
| 已完成(13)   | 全部已完成(6)                            | 全部已完成(4) | 已收货(3) | 全部完成         | 订单完成    |

### 🔄 状态聚合规则

#### 采购订单状态聚合算法

```
if (所有供应商订单状态 == 待支付(0)) → 采购中(4)
if (部分供应商订单状态 >= 待发货(1) && 部分 == 待支付(0)) → 部分履约(5)
if (所有供应商订单状态 >= 待发货(1)) → 采购完成(6)
if (所有供应商订单状态 >= 待收货(3)) → 供应商已发货(7)
...以此类推
```

> ⚠️ **重要规则**:
> - 支付完成后(状态≥2)，客户**不能取消订单**
> - **部分履约(5)** = 部分供应商订单已付款，是关键中间状态
> - 供应商订单状态变化驱动采购订单状态自动更新

## 🔄 同步状态管理机制

### 多系统同步架构

```
TzOrderSupplier 同步状态:
├── externalSyncStatus (外部平台同步)
│   ├── 0: 未同步 - 订单尚未推送到1688等平台
│   ├── 1: 已同步 - 成功创建外部平台订单
│   └── 2: 同步失败 - 创建外部平台订单失败
│
└── wmsSyncStatus (WMS系统同步)
    ├── 0: 未创建 - 尚未在WMS创建采购单
    ├── 1: 已创建 - 成功在WMS创建采购单
    └── 2: 创建失败 - WMS采购单创建失败
```

### 同步状态流转规则

```mermaid
graph TD
    A[供应商订单创建] --> B{外部平台同步}
    B -->|成功| C[externalSyncStatus=1]
    B -->|失败| D[externalSyncStatus=2]
    C --> E{WMS同步}
    E -->|成功| F[wmsSyncStatus=1]
    E -->|失败| G[wmsSyncStatus=2]

    D --> H[记录失败原因]
    G --> I[记录失败原因]
    H --> J[人工重试机制]
    I --> J

    style C fill:#c8e6c9
    style F fill:#c8e6c9
    style D fill:#ffcdd2
    style G fill:#ffcdd2
```

### 关键字段说明

- **platformOrderId**: 外部平台订单ID (如1688订单ID)
- **platformOrderNo**: 外部平台订单号
- **wmsPurchaseOrderNo**: WMS系统采购单号
- **externalSyncFailedMessage**: 外部平台同步失败原因
- **wmsFailedMessage**: WMS同步失败原因

### 同步异常处理机制

1. **失败重试**: 指数退避算法自动重试
2. **人工干预**: 同步失败超过阈值需要人工处理
3. **状态补偿**: 提供状态修复和数据同步补偿机制
4. **监控告警**: 同步失败率超标自动告警

## ⚙️ 状态转换规则

### 正向流转规则

1. **顺序性**: 必须按既定顺序流转，不允许跳跃
2. **幂等性**: 重复的状态更新不会产生副作用
3. **事务性**: 状态转换必须在事务中完成
4. **审计性**: 每次状态变更必须记录变更日志
5. **同步性**: 状态转换需要考虑多系统同步状态

### 逆向流转规则

1. **取消规则**:
    - 仅"待支付(1)"状态可由客户取消
    - 支付完成后(状态≥2)客户**不能取消订单**
    - 仅能通过管理后台操作退款流程
2. **退款规则**: 仅支付完成后的订单可由后台申请退款
3. **异常处理**: 失败状态不可自动恢复，需人工干预

## 🔍 关键业务校验点

### 三层状态聚合算法实现

#### 🏪 供应商订单状态聚合到采购订单状态

```java
@Component
public class OrderStatusAggregator {

    /**
     * 核心聚合算法：供应商订单状态 → 采购订单状态
     */
    public TzOrderPurchaseStatusEnum calculatePurchaseStatus(List<TzOrderSupplier> suppliers) {
        if (suppliers.isEmpty()) {
            throw new BusinessException("采购订单必须包含至少一个供应商订单");
        }

        // 统计各状态的供应商数量
        long totalSuppliers = suppliers.size();
        long paidSuppliers = countSuppliersByMinStatus(suppliers, PENDING_SHIPMENT); // 待发货及以上
        long shippedSuppliers = countSuppliersByMinStatus(suppliers, SHIPPED); // 待收货及以上
        long completedSuppliers = countSuppliersByStatus(suppliers, COMPLETED); // 已完成
        long cancelledSuppliers = countSuppliersByStatus(suppliers, CANCELLED); // 已取消

        // 状态聚合逻辑
        if (cancelledSuppliers > 0 && cancelledSuppliers == totalSuppliers) {
            return ORDER_CANCELLED; // 全部取消
        }

        if (completedSuppliers == totalSuppliers) {
            return ORDER_COMPLETED; // 全部完成
        }

        if (shippedSuppliers == totalSuppliers) {
            return SUPPLIER_SHIPPED; // 全部已发货
        }

        if (paidSuppliers == totalSuppliers) {
            return PROCUREMENT_COMPLETED; // 采购完成，等待发货
        }

        if (paidSuppliers > 0) {
            return PARTIALLY_PROCUREMENT; // 部分履约（关键状态）
        }

        return PROCUREMENT_IN_PROGRESS; // 采购中
    }

    private long countSuppliersByMinStatus(List<TzOrderSupplier> suppliers,
                                          TzOrderSupplierStatusEnum minStatus) {
        return suppliers.stream()
            .filter(s -> s.getStatus().getValue() >= minStatus.getValue())
            .count();
    }
}
```

#### 📦 订单项状态驱动供应商订单状态

```java
@Component
public class SupplierOrderStatusHandler {

    /**
     * 订单项状态变更驱动供应商订单状态更新
     */
    @EventListener
    @Transactional
    public void handleItemStatusChanged(OrderItemStatusChangedEvent event) {
        // 查找受影响的供应商订单
        TzOrderSupplier supplierOrder = findSupplierOrderByItem(event.getItemId());
        List<TzOrderItem> relatedItems = findItemsBySupplierOrder(supplierOrder.getId());

        // 计算新的供应商订单状态
        TzOrderSupplierStatusEnum newStatus = calculateSupplierStatus(relatedItems);

        if (newStatus != supplierOrder.getStatus()) {
            // 更新供应商订单状态
            updateSupplierOrderStatus(supplierOrder.getId(), newStatus);

            // 触发采购订单状态更新
            publishPurchaseOrderUpdateEvent(supplierOrder.getPurchaseOrderId());
        }
    }

    private TzOrderSupplierStatusEnum calculateSupplierStatus(List<TzOrderItem> items) {
        boolean allCompleted = items.stream()
            .allMatch(item -> item.getStatus() == TzOrderItemStatusEnum.COMPLETED);

        boolean anyShipped = items.stream()
            .anyMatch(item -> item.getStatus() == TzOrderItemStatusEnum.SHIPPED);

        boolean anyDelivered = items.stream()
            .anyMatch(item -> item.getStatus() == TzOrderItemStatusEnum.DELIVERED);

        if (allCompleted) return COMPLETED;
        if (anyDelivered) return WAREHOUSE_RECEIVED;
        if (anyShipped) return SHIPPED;

        return PENDING_SHIPMENT;
    }
}
```

#### 🔄 完整事件驱动链

```java
@Component
public class OrderStatusEventChain {

    /**
     * 采购订单状态更新事件处理
     */
    @EventListener
    public void handlePurchaseOrderUpdate(PurchaseOrderUpdateEvent event) {
        List<TzOrderSupplier> suppliers = findSuppliersByPurchaseOrder(event.getPurchaseOrderId());

        TzOrderPurchaseStatusEnum newStatus = orderStatusAggregator
            .calculatePurchaseStatus(suppliers);

        updatePurchaseOrderStatus(event.getPurchaseOrderId(), newStatus);

        // 通知客户状态变更
        publishCustomerNotification(event.getPurchaseOrderId(), newStatus);
    }

    /**
     * 同步状态检查和修复
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void syncStatusCheck() {
        List<TzOrderPurchase> inconsistentOrders = findInconsistentOrders();

        for (TzOrderPurchase order : inconsistentOrders) {
            repairOrderStatus(order.getId());
        }
    }
}
```

### 时间轴验证

- 状态转换时间必须递增
- 关键状态必须有对应时间戳
- 超时状态需要预警机制

## 🚨 异常场景处理

### 状态冲突解决

1. **并发更新**: 使用乐观锁机制防止状态冲突
2. **网络异常**: 实现状态同步补偿机制
3. **系统故障**: 提供状态回滚和恢复能力

### 边界条件

1. **空订单**: 无订单项的订单不允许流转
2. **部分失败**: 单个订单项失败的处理策略
3. **重复通知**: 幂等处理重复的状态更新请求
4. **同步不一致**: 处理外部平台和WMS系统状态不同步问题

## 🔄 多系统协调机制

### 系统间状态同步策略

#### 🔗 三系统协调架构

```
Fulfillmen Shop (主系统)
    ↓ 同步订单
1688/电商平台 (externalSyncStatus)
    ↓ 物流状态回调
WMS系统 (wmsSyncStatus)
    ↓ 入库状态通知
Fulfillmen Shop (状态聚合更新)
```

#### 📊 系统同步状态矩阵

| 主系统状态     | 外部平台状态 | WMS状态  | 同步策略       | 异常处理   |
|-----------|--------|--------|------------|--------|
| 采购中(4)    | 未同步(0) | 未创建(0) | 等待外部平台创建订单 | 超时重试   |
| 采购中(4)    | 已同步(1) | 未创建(0) | 创建WMS采购单   | 创建失败告警 |
| 采购完成(6)   | 已同步(1) | 已创建(1) | 正常状态，监控进展  | 定期健康检查 |
| 供应商已发货(7) | 已同步(1) | 已创建(1) | 等待WMS入库通知  | 物流超时预警 |

### 异常处理和恢复机制

#### 🚨 同步失败处理流程

```java
@Component
public class SyncFailureHandler {

    /**
     * 外部平台同步失败处理
     */
    @EventListener
    @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 2000, multiplier = 2))
    public void handleExternalSyncFailure(ExternalSyncFailureEvent event) {
        TzOrderSupplier supplier = findSupplierOrder(event.getSupplierId());

        // 更新失败状态和原因
        supplier.setExternalSyncStatus(OrderSupplierSyncStatusEnums.SYNC_FAILED);
        supplier.setExternalSyncFailedMessage(event.getFailureReason());
        updateSupplierOrder(supplier);

        // 记录失败日志用于分析
        logSyncFailure("EXTERNAL", event);

        // 超过重试次数后进入人工处理队列
        if (event.getRetryCount() >= 3) {
            addToManualProcessingQueue(supplier.getId(), "外部平台同步失败");
        }
    }

    /**
     * WMS同步失败处理
     */
    @EventListener
    public void handleWmsSyncFailure(WmsSyncFailureEvent event) {
        // 类似的处理逻辑...
    }

    /**
     * 状态不一致修复
     */
    @Scheduled(cron = "0 */10 * * * *") // 每10分钟执行
    public void repairInconsistentStates() {
        List<TzOrderSupplier> inconsistentSuppliers =
            findSuppliersWithInconsistentSyncStatus();

        for (TzOrderSupplier supplier : inconsistentSuppliers) {
            try {
                // 尝试修复状态
                repairSupplierSyncStatus(supplier);
            } catch (Exception e) {
                log.error("修复供应商订单同步状态失败: {}", supplier.getId(), e);
            }
        }
    }
}
```

#### 🔧 数据一致性保证机制

```java
@Component
public class DataConsistencyGuard {

    /**
     * 分布式事务处理 - 状态更新
     */
    @Transactional
    @GlobalTransactional // 使用Seata分布式事务
    public void updateOrderStatusWithConsistency(Long orderId,
                                                TzOrderPurchaseStatusEnum newStatus) {
        try {
            // 1. 更新主订单状态
            updatePurchaseOrderStatus(orderId, newStatus);

            // 2. 同步外部平台状态
            syncExternalPlatformStatus(orderId, newStatus);

            // 3. 同步WMS系统状态
            syncWmsStatus(orderId, newStatus);

            // 4. 发送状态变更通知
            publishStatusChangeNotification(orderId, newStatus);

        } catch (Exception e) {
            // 回滚所有更改
            log.error("订单状态更新失败，执行回滚: {}", orderId, e);
            throw new BusinessException("订单状态更新失败");
        }
    }

    /**
     * 最终一致性补偿机制
     */
    @Async
    public void executeCompensationTask(CompensationTask task) {
        int maxRetries = 5;
        int currentRetry = 0;

        while (currentRetry < maxRetries) {
            try {
                // 执行补偿逻辑
                task.compensate();
                break;
            } catch (Exception e) {
                currentRetry++;
                if (currentRetry >= maxRetries) {
                    // 补偿失败，记录人工处理
                    addToManualProcessingQueue(task.getOrderId(),
                        "补偿任务失败: " + e.getMessage());
                } else {
                    // 指数退避重试
                    try {
                        Thread.sleep(1000 * (1L << currentRetry));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }
}
```

### 监控和告警机制

#### 📈 关键监控指标

- **同步成功率**: externalSyncStatus=1 的比例
- **同步延迟**: 从状态变更到同步完成的时间
- **异常恢复时间**: 从失败到成功恢复的平均时间
- **人工介入比例**: 需要人工处理的订单百分比

#### 🚨 告警规则配置

```yaml
monitoring:
  alerts:
    - name: "外部平台同步失败率过高"
      condition: "external_sync_failure_rate > 5%"
      severity: "HIGH"
      action: "immediate_notification"

    - name: "WMS同步延迟过长"
      condition: "wms_sync_delay > 30min"
      severity: "MEDIUM"
      action: "scheduled_notification"

    - name: "状态不一致订单过多"
      condition: "inconsistent_orders_count > 100"
      severity: "CRITICAL"
      action: "escalation"
```

## 📊 监控指标

### 状态分布统计

- 各状态订单数量统计
- 状态流转耗时分析
- 异常状态比例监控

### 业务健康度

- 订单完成率 = 已完成订单 / 总订单数
- 平均处理时间 = 从创建到完成的平均时间
- 异常率 = (取消 + 失败) / 总订单数
- 客户取消率 = 客户主动取消 / 总取消订单

## 🛠️ 代码实现要点

### 状态机模式

```java
@Component
public class OrderStateMachine {

    public void transition(TzOrderPurchase order, TzOrderPurchaseStatusEnum targetStatus) {
        validateTransition(order.getOrderStatus(), targetStatus);
        order.setOrderStatus(targetStatus);
        recordStateChange(order, targetStatus);
    }

    private void validateTransition(TzOrderPurchaseStatusEnum current, TzOrderPurchaseStatusEnum target) {
        // 状态转换规则验证
    }
}
```

### 事件驱动架构

```java
@EventListener
public void handleOrderPaid(OrderPaidEvent event) {
    stateMachine.transition(event.getOrder(), PAYMENT_COMPLETED);
    publishNextStepEvent(event);
}
```

## 🔐 安全与合规

### 权限控制

- 状态变更需要相应权限
- 关键状态变更需要审批
- 审计日志不可篡改

### 数据一致性

- 分布式事务保证
- 最终一致性模型
- 补偿事务机制

## 📈 扩展性设计

### 新状态支持

1. 新增状态枚举值
2. 更新状态流转图
3. 补充业务规则
4. 回归测试验证

### 多渠道适配

- 支持不同销售渠道的状态差异
- 提供状态映射配置
- 实现渠道特定的状态逻辑

---

## 📝 版本记录

| 版本  | 日期         | 变更内容                                                               | 作者     |
|-----|------------|--------------------------------------------------------------------|--------|
| 1.0 | 2025-08-29 | 初始版本创建，基础三层实体状态流程                                                  | Claude |
| 2.0 | 2025-08-30 | **重大更新**: 新增TzOrderSupplier供应商订单层，完善三层状态体系架构、多系统同步机制、状态聚合算法和异常处理策略 | Claude |

## 🔗 相关文档

- [MDC过滤器使用说明](./MDC过滤器使用说明.md)
- [统一过滤器配置说明](./统一过滤器配置说明.md)
- [SaToken上下文问题解决方案](./SaToken上下文问题解决方案.md)

---
*本文档最后更新: 2025-08-30*

## 🎯 重要提醒

**代码审计要点**:

1. ✅ 确认供应商订单状态聚合算法的正确实现
2. ✅ 验证三层状态变更的事务一致性
3. ✅ 检查多系统同步状态的异常处理
4. ✅ 审查客户取消订单权限控制逻辑
5. ✅ 确认"部分履约"状态的业务规则实现
