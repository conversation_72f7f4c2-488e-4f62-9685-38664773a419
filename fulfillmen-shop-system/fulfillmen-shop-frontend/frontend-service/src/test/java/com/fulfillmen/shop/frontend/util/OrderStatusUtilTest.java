/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 订单状态工具类测试 重点测试客户操作权限控制
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 验证订单状态权限判断逻辑
 * @since 1.0.0
 */
@DisplayName("订单状态工具类测试")
class OrderStatusUtilTest {

    @Test
    @DisplayName("测试客户取消订单权限")
    void testCanCancel() {
        // null 状态不能取消
        assertFalse(OrderStatusUtil.canCancel(null),
            "null状态不应该允许取消");

        // 只有待支付状态可以取消
        assertTrue(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.PAYMENT_PENDING),
            "待支付状态应该允许取消");

        // 支付完成后的所有状态都不能取消
        assertFalse(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED),
            "支付完成状态不应该允许取消");
        assertFalse(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS),
            "采购中状态不应该允许取消");
        assertFalse(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT),
            "部分履约状态不应该允许取消");
        assertFalse(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED),
            "供应商已发货状态不应该允许取消");
        assertFalse(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.IN_STOCK),
            "已入库状态不应该允许取消");

        // 已取消状态不能重复取消
        assertFalse(OrderStatusUtil.canCancel(TzOrderPurchaseStatusEnum.ORDER_CANCELLED),
            "已取消状态不应该允许重复取消");
    }

    @Test
    @DisplayName("测试支付权限")
    void testCanPay() {
        // null 状态不能支付
        assertFalse(OrderStatusUtil.canPay(null),
            "null状态不应该允许支付");

        // 待支付状态可以支付
        assertTrue(OrderStatusUtil.canPay(TzOrderPurchaseStatusEnum.PAYMENT_PENDING),
            "待支付状态应该允许支付");
        assertTrue(OrderStatusUtil.canPay(TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED),
            "临时保存状态应该允许支付");

        // 已支付状态不能重复支付
        assertFalse(OrderStatusUtil.canPay(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED),
            "支付完成状态不应该允许重复支付");
        assertFalse(OrderStatusUtil.canPay(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS),
            "采购中状态不应该允许支付");
    }

    @Test
    @DisplayName("测试退款申请权限")
    void testCanApplyRefund() {
        // null 状态不能申请退款
        assertFalse(OrderStatusUtil.canApplyRefund(null),
            "null状态不应该允许申请退款");

        // 支付前状态不能申请退款
        assertFalse(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.PAYMENT_PENDING),
            "待支付状态不应该允许申请退款");
        assertFalse(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED),
            "临时保存状态不应该允许申请退款");

        // 支付后到完成前的状态可以申请退款
        assertTrue(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED),
            "支付完成状态应该允许申请退款");
        assertTrue(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS),
            "采购中状态应该允许申请退款");
        assertTrue(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED),
            "供应商已发货状态应该允许申请退款");
        assertTrue(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.IN_STOCK),
            "已入库状态应该允许申请退款");

        // 已取消状态不能申请退款
        assertFalse(OrderStatusUtil.canApplyRefund(TzOrderPurchaseStatusEnum.ORDER_CANCELLED),
            "已取消状态不应该允许申请退款");
    }

    @Test
    @DisplayName("测试物流查看权限")
    void testCanViewTracking() {
        // null 状态不能查看物流
        assertFalse(OrderStatusUtil.canViewTracking(null),
            "null状态不应该允许查看物流");

        // 支付前状态不能查看物流
        assertFalse(OrderStatusUtil.canViewTracking(TzOrderPurchaseStatusEnum.PAYMENT_PENDING),
            "待支付状态不应该允许查看物流");
        assertFalse(OrderStatusUtil.canViewTracking(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED),
            "支付完成状态不应该允许查看物流");

        // 采购中及以后状态可以查看物流
        assertTrue(OrderStatusUtil.canViewTracking(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS),
            "采购中状态应该允许查看物流");
        assertTrue(OrderStatusUtil.canViewTracking(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED),
            "供应商已发货状态应该允许查看物流");
        assertTrue(OrderStatusUtil.canViewTracking(TzOrderPurchaseStatusEnum.IN_STOCK),
            "已入库状态应该允许查看物流");
    }

    @Test
    @DisplayName("测试最终状态判断")
    void testIsFinalStatus() {
        // 最终状态
        assertTrue(OrderStatusUtil.isFinalStatus(TzOrderPurchaseStatusEnum.IN_STOCK),
            "已入库应该是最终状态");
        assertTrue(OrderStatusUtil.isFinalStatus(TzOrderPurchaseStatusEnum.ORDER_CANCELLED),
            "已取消应该是最终状态");

        // 非最终状态
        assertFalse(OrderStatusUtil.isFinalStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING),
            "待支付不应该是最终状态");
        assertFalse(OrderStatusUtil.isFinalStatus(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS),
            "采购中不应该是最终状态");
        assertFalse(OrderStatusUtil.isFinalStatus(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED),
            "仓库已收货不应该是最终状态");
    }

    @Test
    @DisplayName("测试状态信息获取")
    void testGetOrderStatusInfo() {
        // null 状态处理
        OrderStatusUtil.OrderStatusInfo nullInfo = OrderStatusUtil.getOrderStatusInfo(null);
        assertNotNull(nullInfo, "null状态应该返回默认状态信息");
        assertEquals("未知状态", nullInfo.getDisplayName());

        // 待支付状态信息
        OrderStatusUtil.OrderStatusInfo pendingInfo = OrderStatusUtil.getOrderStatusInfo(TzOrderPurchaseStatusEnum.PAYMENT_PENDING);
        assertNotNull(pendingInfo, "待支付状态应该有状态信息");
        assertEquals("待支付", pendingInfo.getDisplayName());
        assertEquals("warning", pendingInfo.getTagType());
        assertEquals(10, pendingInfo.getProgressPercentage());

        // 已完成状态信息
        OrderStatusUtil.OrderStatusInfo completedInfo = OrderStatusUtil.getOrderStatusInfo(TzOrderPurchaseStatusEnum.IN_STOCK);
        assertEquals("已完成", completedInfo.getDisplayName());
        assertEquals("success", completedInfo.getTagType());
        assertEquals(100, completedInfo.getProgressPercentage());

        // 已取消状态信息
        OrderStatusUtil.OrderStatusInfo cancelledInfo = OrderStatusUtil.getOrderStatusInfo(TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
        assertEquals("已取消", cancelledInfo.getDisplayName());
        assertEquals("danger", cancelledInfo.getTagType());
        assertEquals(0, cancelledInfo.getProgressPercentage());
    }
}
