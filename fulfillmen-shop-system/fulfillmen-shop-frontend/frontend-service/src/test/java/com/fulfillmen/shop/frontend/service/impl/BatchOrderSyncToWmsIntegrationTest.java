/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.common.context.UserContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.tenant.ShopTenantContext;
import com.fulfillmen.shop.frontend.service.IFrontendOrderService;
import com.google.common.collect.Sets;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 批量订单同步到WMS集成测试
 *
 * <pre>
 * 功能目标：
 * 1. 实际批量同步指定的25个订单号到WMS系统
 * 2. 连接真实数据库和WMS接口进行同步
 * 3. 处理并发同步和错误重试
 * 4. 生成详细的同步执行报告
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/25
 */
@Slf4j
@SpringBootTest(classes = SimpleBatchSyncTestConfig.class)
@ActiveProfiles("sealosDev")
@Transactional
class BatchOrderSyncToWmsIntegrationTest {

    /**
     * 需要批量同步的订单号列表（从图片中提取的25个订单号）
     */
    private static final Set<String> TARGET_ORDER_NUMBERS = Sets.newHashSet(
        "N2508241728409995687",
        "N2508241727260055679",
        "N2508241726083675671",
        "N2508241723258575647",
        "N2508241722084025635",
        "N2508241712359275567",
        "N2508241707359555515",
        "N2508241701294295463",
        "N2508241656590475425",
        "N2508241655521895415",
        "N2508241643403545301",
        "N2508241642362185293",
        "N2508241637094775247",
        "N2508241623036105111",
        "N2508241619058145079",
        "N2508241609352225027",
        "N2508241555355504967",
        "N2508241554336054957",
        "N2508241554302544955",
        "N2508241533418424847",
        "N2508241530448124829",
        "N2508241529440844823",
        "N2508241528198634813",
        "N2508241523545174783"
    );
    private static final Long TARGET_USER_ID = 732080147390572L;
    private static final Long TARGET_TENANT_ID = 10000L;
    @Autowired
    private IFrontendOrderService frontendOrderService;

    @BeforeEach
    void setUp() {
        // 设置测试环境的用户和租户上下文
        ShopTenantContext.setTenantId(TARGET_TENANT_ID.toString());
        UserContext userContext = UserContext.builder().id(TARGET_USER_ID).wmsCusCode("13100").build();
        UserContextHolder.setContext(userContext, true);
        log.info("🔧 批量订单同步测试环境初始化完成");
        log.info("目标用户ID: {}", TARGET_USER_ID);
        log.info("目标租户ID: {}", TARGET_TENANT_ID);
        log.info("待同步订单数量: {}", TARGET_ORDER_NUMBERS.size());
        log.info("订单号范围: {} -> {}",
            TARGET_ORDER_NUMBERS.stream().toList().getFirst(),
            TARGET_ORDER_NUMBERS.stream().toList().getLast());
    }

    @Test
    @DisplayName("批量订单同步到WMS - 实际执行")
    void testBatchSyncOrdersToWmsActual() {
        log.info("🚀 开始执行批量订单同步到WMS - 实际同步");

        LocalDateTime startTime = LocalDateTime.now();
        Duration startDuration = Duration.ofMillis(System.currentTimeMillis());

        try {
            // 1. 执行批量同步
            BatchSyncResult result = executeBatchSyncToWms(TARGET_ORDER_NUMBERS.stream().toList());

            // 2. 验证结果
            assertNotNull(result, "同步结果不应为空");
            assertTrue(result.getTotalOrders() > 0, "应该有订单被处理");

            // 3. 输出详细报告
            printBatchSyncReport(result, "实际同步");

            // 4. 导出结果到文件
            exportSyncResultToCsv(result);

            log.info("✅ 批量订单同步到WMS完成 - 实际执行成功");

        } catch (Exception e) {
            log.error("❌ 批量订单同步执行失败", e);
            throw new RuntimeException("批量同步执行失败: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("单个订单同步到WMS验证")
    void testSingleOrderSyncVerification() {
        log.info("🧪 开始单个订单同步验证测试");

        // 选择第一个订单进行详细验证
        String testOrderNo = "N2508241609352225027";
        log.info("测试订单号: {}", testOrderNo);

        try {
            long startTime = System.currentTimeMillis();

            // 执行单个订单同步
            frontendOrderService.syncOrderToWms(testOrderNo);

            long executionTime = System.currentTimeMillis() - startTime;

            log.info("✅ 单个订单同步成功: {}, 耗时: {}ms", testOrderNo, executionTime);

        } catch (BusinessExceptionI18n e) {
            log.warn("⚠️ 单个订单同步业务异常: {}, 原因: {}", testOrderNo, e.getMessage());
            // 这种情况可能是订单不存在或状态不对，属于预期异常
        } catch (Exception e) {
            log.error("❌ 单个订单同步系统异常: {}, 原因: {}", testOrderNo, e.getMessage(), e);
            throw new RuntimeException("单个订单同步失败: " + e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("分批次同步订单 - 提升成功率")
    void testBatchSyncInChunks() {
        log.info("📦 开始分批次订单同步测试");

        // 将25个订单分为5批，每批5个
        int chunkSize = 5;
        List<List<String>> orderChunks = partitionList(TARGET_ORDER_NUMBERS.stream().toList(), chunkSize);

        log.info("订单分批策略: {} 批次，每批 {} 个订单", orderChunks.size(), chunkSize);

        List<BatchSyncResult> chunkResults = new ArrayList<>();

        for (int i = 0; i < orderChunks.size(); i++) {
            List<String> chunk = orderChunks.get(i);
            log.info("执行第 {} 批次，订单数量: {}", i + 1, chunk.size());

            try {
                BatchSyncResult chunkResult = executeBatchSyncToWms(chunk);
                chunkResults.add(chunkResult);

                log.info("第 {} 批次完成: 成功 {} 个，失败 {} 个",
                    i + 1, chunkResult.getSuccessCount(), chunkResult.getFailureCount());

                // 批次间增加短暂延迟，避免对WMS造成压力
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("第 {} 批次执行失败: {}", i + 1, e.getMessage(), e);
            }
        }

        // 汇总所有批次结果
        BatchSyncResult overallResult = mergeBatchResults(chunkResults);
        printBatchSyncReport(overallResult, "分批次同步");

        log.info("📦 分批次订单同步完成");
    }

    /**
     * 执行批量订单同步到WMS
     */
    private BatchSyncResult executeBatchSyncToWms(List<String> orderNumbers) {
        LocalDateTime startTime = LocalDateTime.now();
        long startMillis = System.currentTimeMillis();

        AtomicInteger successCount = new AtomicInteger(0);
        List<String> successOrders = Collections.synchronizedList(new ArrayList<>());
        List<SyncFailure> failures = Collections.synchronizedList(new ArrayList<>());

        // 使用虚拟线程池进行并发同步
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

        try {
            List<CompletableFuture<Void>> futures = orderNumbers.stream()
                .map(orderNo -> CompletableFuture.runAsync(() -> {
                    try {
                        log.debug("开始同步订单: {}", orderNo);

                        // 实际调用同步接口
                        frontendOrderService.syncOrderToWms(orderNo);

                        successCount.incrementAndGet();
                        successOrders.add(orderNo);
                        log.info("✅ 订单同步成功: {}", orderNo);

                    } catch (BusinessExceptionI18n e) {
                        failures.add(SyncFailure.builder()
                            .orderNo(orderNo)
                            .errorMessage(e.getMessage())
                            .errorType("BUSINESS_EXCEPTION")
                            .failureTime(LocalDateTime.now())
                            .stackTrace(getStackTrace(e))
                            .build());
                        log.warn("⚠️ 订单同步业务异常: {}, 原因: {}", orderNo, e.getMessage());

                    } catch (Exception e) {
                        failures.add(SyncFailure.builder()
                            .orderNo(orderNo)
                            .errorMessage(e.getMessage())
                            .errorType("SYSTEM_EXCEPTION")
                            .failureTime(LocalDateTime.now())
                            .stackTrace(getStackTrace(e))
                            .build());
                        log.error("❌ 订单同步系统异常: {}, 原因: {}", orderNo, e.getMessage());
                    }
                }, executor))
                .toList();

            // 等待所有同步任务完成，最多等待5分钟
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .orTimeout(5, TimeUnit.MINUTES)
                .join();

        } catch (Exception e) {
            log.error("批量同步执行异常: {}", e.getMessage(), e);
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        LocalDateTime endTime = LocalDateTime.now();
        long executionTime = System.currentTimeMillis() - startMillis;

        return BatchSyncResult.builder()
            .totalOrders(orderNumbers.size())
            .successCount(successCount.get())
            .failureCount(failures.size())
            .executionTimeMs(executionTime)
            .successOrders(new ArrayList<>(successOrders))
            .failures(new ArrayList<>(failures))
            .successRate(!orderNumbers.isEmpty() ? (double) successCount.get() / orderNumbers.size() * 100 : 0.0)
            .averageTimePerOrder(!orderNumbers.isEmpty() ? (double) executionTime / orderNumbers.size() : 0.0)
            .startTime(startTime)
            .endTime(endTime)
            .build();
    }

    /**
     * 将列表分割为指定大小的子列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int chunkSize) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(list.subList(i, Math.min(list.size(), i + chunkSize)));
        }
        return chunks;
    }

    /**
     * 合并多个批次的结果
     */
    private BatchSyncResult mergeBatchResults(List<BatchSyncResult> chunkResults) {
        if (chunkResults.isEmpty()) {
            return BatchSyncResult.builder().build();
        }

        int totalOrders = chunkResults.stream().mapToInt(BatchSyncResult::getTotalOrders).sum();
        int totalSuccess = chunkResults.stream().mapToInt(BatchSyncResult::getSuccessCount).sum();
        int totalFailures = chunkResults.stream().mapToInt(BatchSyncResult::getFailureCount).sum();
        long totalTime = chunkResults.stream().mapToLong(BatchSyncResult::getExecutionTimeMs).sum();

        List<String> allSuccessOrders = chunkResults.stream()
            .flatMap(r -> r.getSuccessOrders().stream())
            .collect(Collectors.toList());

        List<SyncFailure> allFailures = chunkResults.stream()
            .flatMap(r -> r.getFailures().stream())
            .collect(Collectors.toList());

        LocalDateTime earliestStart = chunkResults.stream()
            .map(BatchSyncResult::getStartTime)
            .min(LocalDateTime::compareTo)
            .orElse(LocalDateTime.now());

        LocalDateTime latestEnd = chunkResults.stream()
            .map(BatchSyncResult::getEndTime)
            .max(LocalDateTime::compareTo)
            .orElse(LocalDateTime.now());

        return BatchSyncResult.builder()
            .totalOrders(totalOrders)
            .successCount(totalSuccess)
            .failureCount(totalFailures)
            .executionTimeMs(totalTime)
            .successOrders(allSuccessOrders)
            .failures(allFailures)
            .successRate(totalOrders > 0 ? (double) totalSuccess / totalOrders * 100 : 0.0)
            .averageTimePerOrder(totalOrders > 0 ? (double) totalTime / totalOrders : 0.0)
            .startTime(earliestStart)
            .endTime(latestEnd)
            .build();
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * 打印批量同步报告
     */
    private void printBatchSyncReport(BatchSyncResult result, String testScenario) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        log.info("📋 ===== 批量订单同步WMS报告 ({}) =====", testScenario);
        log.info("测试时间: {}", timestamp);
        log.info("订单总数: {} 个", result.getTotalOrders());
        log.info("同步成功: {} 个", result.getSuccessCount());
        log.info("同步失败: {} 个", result.getFailureCount());
        log.info("成功率: {}%", String.format("%.2f", result.getSuccessRate()));
        log.info("总执行时间: {} ms ({} 秒)", result.getExecutionTimeMs(),
            String.format("%.2f", result.getExecutionTimeMs() / 1000.0));
        log.info("平均每订单处理时间: {} ms", String.format("%.2f", result.getAverageTimePerOrder()));

        if (result.getStartTime() != null && result.getEndTime() != null) {
            log.info("开始时间: {}", result.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            log.info("结束时间: {}", result.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        if (!result.getFailures().isEmpty()) {
            log.info("📝 失败订单详情:");
            result.getFailures().forEach(failure -> {
                log.info("  ❌ {}: {} ({})", failure.getOrderNo(), failure.getErrorMessage(), failure.getErrorType());
                if (failure.getFailureTime() != null) {
                    log.info("     失败时间: {}", failure.getFailureTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
            });
        }

        if (!result.getSuccessOrders().isEmpty()) {
            log.info("✅ 成功订单数量: {}", result.getSuccessOrders().size());
            log.info("成功订单列表:");
            result.getSuccessOrders().forEach(orderNo -> log.info("  ✓ {}", orderNo));
        }

        // 统计不同类型的错误
        if (!result.getFailures().isEmpty()) {
            log.info("📊 错误类型统计:");
            result.getFailures().stream()
                .collect(Collectors.groupingBy(SyncFailure::getErrorType, Collectors.counting()))
                .forEach((errorType, count) -> log.info("  - {}: {} 个", errorType, count));
        }

        log.info("📊 ===== 报告结束 =====");
    }

    /**
     * 导出同步结果到CSV文件
     */
    private void exportSyncResultToCsv(BatchSyncResult result) throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "batch_order_sync_result_" + timestamp + ".csv";
        Path outputPath = Paths.get("src/test/resources", fileName);

        log.info("开始导出同步结果到文件: {}", outputPath.toAbsolutePath());

        List<String> lines = new ArrayList<>();

        // CSV头部
        lines.add("序号,订单号,同步状态,错误类型,错误信息,处理时间");

        // 成功的订单
        for (int i = 0; i < result.getSuccessOrders().size(); i++) {
            String orderNo = result.getSuccessOrders().get(i);
            lines.add(String.format("%d,%s,成功,,,%s",
                i + 1, orderNo, result.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        // 失败的订单
        int successCount = result.getSuccessOrders().size();
        for (int i = 0; i < result.getFailures().size(); i++) {
            SyncFailure failure = result.getFailures().get(i);
            String escapedMessage = failure.getErrorMessage().replace("\"", "\"\"").replace(",", "，");
            lines.add(String.format("%d,%s,失败,%s,\"%s\",%s",
                successCount + i + 1,
                failure.getOrderNo(),
                failure.getErrorType(),
                escapedMessage,
                failure.getFailureTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        // 添加汇总信息
        lines.add("");
        lines.add("汇总统计");
        lines.add(String.format("订单总数,%d", result.getTotalOrders()));
        lines.add(String.format("成功数量,%d", result.getSuccessCount()));
        lines.add(String.format("失败数量,%d", result.getFailureCount()));
        lines.add(String.format("成功率,%.2f%%", result.getSuccessRate()));
        lines.add(String.format("总执行时间,%d ms", result.getExecutionTimeMs()));
        lines.add(String.format("平均处理时间,%.2f ms", result.getAverageTimePerOrder()));

        Files.write(outputPath, lines, StandardCharsets.UTF_8);
        log.info("同步结果导出完成，文件: {}, 记录数: {}", outputPath.toAbsolutePath(), lines.size());
    }

    /**
     * 批量同步执行结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchSyncResult {

        private int totalOrders;
        private int successCount;
        private int failureCount;
        private long executionTimeMs;
        private List<String> successOrders;
        private List<SyncFailure> failures;
        private double successRate;
        private double averageTimePerOrder;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
    }

    /**
     * 单个同步失败记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncFailure {

        private String orderNo;
        private String errorMessage;
        private String errorType;
        private LocalDateTime failureTime;
        private String stackTrace;
    }
}
