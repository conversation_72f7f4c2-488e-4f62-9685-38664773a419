/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.req.OrderReq.CancelOrderRequest;
import com.fulfillmen.shop.frontend.service.IFrontendOrderService;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 前端订单服务取消权限测试 专门测试客户取消订单的权限控制
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 验证订单取消权限控制逻辑
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("前端订单服务取消权限测试")
class FrontendOrderServiceCancelPermissionTest {

    @Mock
    private TzOrderPurchaseRepository orderPurchaseRepository;

    @Mock
    private IFrontendOrderService frontendOrderService;

    private CancelOrderRequest cancelRequest;

    @BeforeEach
    void setUp() {
        cancelRequest = CancelOrderRequest.builder()
            .reason("客户取消")
            .description("测试取消")
            .build();
    }

    @Test
    @DisplayName("待支付状态订单应该允许取消")
    void shouldAllowCancelForPaymentPendingOrder() {
        // 准备测试数据
        TzOrderPurchase order = createMockOrder("TEST001", TzOrderPurchaseStatusEnum.PAYMENT_PENDING);
        OrderContextDTO orderContext = createMockOrderContext(order);

        when(orderPurchaseRepository.getOrderContextByPurchaseOrderNo("TEST001"))
            .thenReturn(orderContext);

        // 验证待支付状态可以取消
        assertTrue(order.getOrderStatus().isCancellable(),
            "待支付状态应该允许取消");
    }

    @Test
    @DisplayName("支付完成后的订单不应该允许取消")
    void shouldNotAllowCancelForPaidOrder() {
        // 支付完成状态
        TzOrderPurchase paidOrder = createMockOrder("TEST002", TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED);
        assertFalse(paidOrder.getOrderStatus().isCancellable(),
            "支付完成状态不应该允许取消");

        // 采购中状态
        TzOrderPurchase procurementOrder = createMockOrder("TEST003", TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS);
        assertFalse(procurementOrder.getOrderStatus().isCancellable(),
            "采购中状态不应该允许取消");

        // 部分履约状态
        TzOrderPurchase partialOrder = createMockOrder("TEST004", TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT);
        assertFalse(partialOrder.getOrderStatus().isCancellable(),
            "部分履约状态不应该允许取消");

        // 供应商已发货状态
        TzOrderPurchase shippedOrder = createMockOrder("TEST005", TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED);
        assertFalse(shippedOrder.getOrderStatus().isCancellable(),
            "供应商已发货状态不应该允许取消");

        // 已入库状态
        TzOrderPurchase stockedOrder = createMockOrder("TEST006", TzOrderPurchaseStatusEnum.IN_STOCK);
        assertFalse(stockedOrder.getOrderStatus().isCancellable(),
            "已入库状态不应该允许取消");
    }

    @Test
    @DisplayName("临时保存状态订单不应该允许取消")
    void shouldNotAllowCancelForTemporarySavedOrder() {
        TzOrderPurchase order = createMockOrder("TEST007", TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED);
        assertFalse(order.getOrderStatus().isCancellable(),
            "临时保存状态不应该允许取消");
    }

    @Test
    @DisplayName("已取消状态订单不应该允许重复取消")
    void shouldNotAllowCancelForAlreadyCancelledOrder() {
        TzOrderPurchase order = createMockOrder("TEST008", TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
        assertFalse(order.getOrderStatus().isCancellable(),
            "已取消状态不应该允许重复取消");
    }

    @Test
    @DisplayName("验证业务规则：支付完成后客户无法取消订单")
    void shouldEnforceBusinessRuleAfterPayment() {
        // 遍历所有支付完成后的状态
        TzOrderPurchaseStatusEnum[] statusesAfterPayment = {
            TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED,
            TzOrderPurchaseStatusEnum.PENDING_REVIEW,
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT,
            TzOrderPurchaseStatusEnum.PROCUREMENT_COMPLETED,
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED,
            TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED,
            TzOrderPurchaseStatusEnum.IN_STOCK
        };

        for (TzOrderPurchaseStatusEnum status : statusesAfterPayment) {
            assertFalse(status.isCancellable(),
                String.format("状态 %s 支付完成后不应该允许客户取消", status.getDescription()));
        }
    }

    @Test
    @DisplayName("验证取消权限与状态值的关系")
    void shouldValidateCancelPermissionBasedOnStatusValue() {
        // 只有值为1的PAYMENT_PENDING状态可以取消
        assertEquals(1, TzOrderPurchaseStatusEnum.PAYMENT_PENDING.getValue());
        assertTrue(TzOrderPurchaseStatusEnum.PAYMENT_PENDING.isCancellable());

        // 值≥2的状态（支付完成后）都不能取消
        for (TzOrderPurchaseStatusEnum status : TzOrderPurchaseStatusEnum.values()) {
            if (status.getValue() >= 2 && status != TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
                assertFalse(status.isCancellable(),
                    String.format("状态 %s (值=%d) 应该禁止客户取消",
                        status.getDescription(), status.getValue()));
            }
        }
    }

    /**
     * 创建模拟订单对象
     */
    private TzOrderPurchase createMockOrder(String orderNo, TzOrderPurchaseStatusEnum status) {
        TzOrderPurchase order = new TzOrderPurchase();
        order.setPurchaseOrderNo(orderNo);
        order.setOrderStatus(status);
        order.setId(1L);
        return order;
    }

    /**
     * 创建模拟订单上下文
     */
    private OrderContextDTO createMockOrderContext(TzOrderPurchase order) {
        return OrderContextDTO.builder()
            .purchaseOrder(order)
            .supplierOrders(java.util.Collections.emptyList())
            .orderItems(java.util.Collections.emptyList())
            .shoppingCartIds(java.util.Collections.emptyList())
            .build();
    }
}
