2025-09-02 11:55:51 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 11:55:53 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 11:55:53 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 11:55:53 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 0 Redis repository interfaces.
2025-09-02 11:55:54 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 11:55:54 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 11:55:54 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 11:55:54 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 11:55:54 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2778 ms
2025-09-02 11:55:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 11:55:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 11:55:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 11:55:55 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 11:55:57 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 11:55:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 11:55:57 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'alibabaCallbackLogsScheduledTask' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/AlibabaCallbackLogsScheduledTask.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'alibabaCallbackProcessingService' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/service/AlibabaCallbackProcessingService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'orderWebhookServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderWebhookServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'orderDataSyncServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderDataSyncServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'productSyncServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/service/impl/ProductSyncServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'pdcProductMappingRepositoryImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/core/repository/impl/PdcProductMappingRepositoryImpl.class]: Unsatisfied dependency expressed through constructor parameter 5: No qualifying bean of type 'com.fulfillmen.shop.domain.convert.product.PdcProductDetailConvertMapping' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-09-02 11:55:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 11:55:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-02 11:55:57 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 5 of constructor in com.fulfillmen.shop.manager.core.repository.impl.PdcProductMappingRepositoryImpl required a bean of type 'com.fulfillmen.shop.domain.convert.product.PdcProductDetailConvertMapping' that could not be found.


Action:

Consider defining a bean of type 'com.fulfillmen.shop.domain.convert.product.PdcProductDetailConvertMapping' in your configuration.

2025-09-02 12:01:24 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 12:01:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 12:01:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 12:01:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 77 ms. Found 0 Redis repository interfaces.
2025-09-02 12:01:27 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 12:01:27 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 12:01:27 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 12:01:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 12:01:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2935 ms
2025-09-02 12:01:28 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 12:01:28 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 12:01:28 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 12:01:29 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 12:01:30 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 12:01:30 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 12:01:30 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'alibabaCallbackLogsScheduledTask' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/AlibabaCallbackLogsScheduledTask.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'alibabaCallbackProcessingService' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/service/AlibabaCallbackProcessingService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'orderWebhookServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderWebhookServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'wmsManagerImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/wms/impl/WmsManagerImpl.class]: Unsatisfied dependency expressed through constructor parameter 3: No qualifying bean of type 'com.fulfillmen.shop.manager.support.wms.convert.WmsPurchaseOrderConvert' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-09-02 12:01:31 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 12:01:31 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-02 12:01:31 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 3 of constructor in com.fulfillmen.shop.manager.support.wms.impl.WmsManagerImpl required a bean of type 'com.fulfillmen.shop.manager.support.wms.convert.WmsPurchaseOrderConvert' that could not be found.


Action:

Consider defining a bean of type 'com.fulfillmen.shop.manager.support.wms.convert.WmsPurchaseOrderConvert' in your configuration.

2025-09-02 13:37:10 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 13:37:12 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 13:37:12 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 13:37:12 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 0 Redis repository interfaces.
2025-09-02 13:37:13 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:37:13 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:37:13 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 13:37:13 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 13:37:13 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2730 ms
2025-09-02 13:37:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 13:37:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 13:37:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 13:37:14 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:37:16 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:37:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 13:37:16 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'alibabaCallbackLogsScheduledTask' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/AlibabaCallbackLogsScheduledTask.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'alibabaCallbackProcessingService' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/service/AlibabaCallbackProcessingService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'orderWebhookServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderWebhookServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'orderDataSyncServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderDataSyncServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 5: No qualifying bean of type 'com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-09-02 13:37:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 13:37:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-02 13:37:16 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 5 of constructor in com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl required a bean of type 'com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert' that could not be found.


Action:

Consider defining a bean of type 'com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert' in your configuration.

2025-09-02 13:38:12 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 13:38:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 13:38:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 13:38:14 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 154 ms. Found 0 Redis repository interfaces.
2025-09-02 13:38:15 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:38:15 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:38:16 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 13:38:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 13:38:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3143 ms
2025-09-02 13:38:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 13:38:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 13:38:16 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 13:38:17 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:38:18 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:38:18 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 13:38:19 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@69e49a81
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:20382, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=14, lastTimeStamp=1756791500195}] - instanceId:[InstanceId{instanceId=**************:20382, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-02 13:38:20 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 62 ms
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-02 13:38:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-09-02 13:38:23 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114911029879357440,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-02 13:38:24 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114911029879357440,SpanId:,BizId:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@49d07fcf
2025-09-02 13:38:24 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114911029879357440,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-02 13:38:24 INFO  [RMI TCP Connection(9)-127.0.0.1] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 13:39:26 WARN  [virtual-226] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-09-02 13:45:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 13:38:18,581 to 2025-09-02 13:45:00,003
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.02|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.02|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.02|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:_local         |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 13:55:48 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-02 13:55:48 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=14, lastTimeStamp=1756792548258}] instanceId:[InstanceId{instanceId=**************:20382, stable=false}] @ namespace:[fulfillmen-shop].
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-02 13:55:48 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-02 13:55:50 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 13:55:50 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 13:55:50 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-02 13:55:50 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-09-02 13:55:53 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 13:55:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 13:55:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 13:55:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 0 Redis repository interfaces.
2025-09-02 13:55:55 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:55:55 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:55:56 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 13:55:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 13:55:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2644 ms
2025-09-02 13:55:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 13:55:56 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 13:55:57 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 13:55:57 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:55:59 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:55:59 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 13:55:59 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'alibabaCallbackLogsScheduledTask' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/AlibabaCallbackLogsScheduledTask.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'alibabaCallbackProcessingService' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/service/AlibabaCallbackProcessingService.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'orderWebhookServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderWebhookServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'orderDataSyncServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-workspace/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/alibaba/webhook/service/impl/OrderDataSyncServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 5: No qualifying bean of type 'com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-09-02 13:55:59 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 13:55:59 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-09-02 13:55:59 ERROR [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 5 of constructor in com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl required a bean of type 'com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert' that could not be found.


Action:

Consider defining a bean of type 'com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert' in your configuration.

2025-09-02 13:56:19 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 13:56:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 13:56:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 13:56:21 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 72 ms. Found 0 Redis repository interfaces.
2025-09-02 13:56:21 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:56:21 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 13:56:22 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 13:56:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 13:56:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2566 ms
2025-09-02 13:56:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 13:56:22 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 13:56:23 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 13:56:23 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:56:24 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 13:56:25 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 13:56:25 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@56ac5c80
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:30237, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=14, lastTimeStamp=1756792586283}] - instanceId:[InstanceId{instanceId=**************:30237, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-02 13:56:26 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 57 ms
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-02 13:56:27 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-09-02 13:56:29 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114915584948027392,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-02 13:56:30 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114915584948027392,SpanId:,BizId:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@4a8797a2
2025-09-02 13:56:30 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114915584948027392,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-02 13:56:30 INFO  [RMI TCP Connection(6)-127.0.0.1] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 14:00:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 13:56:25,044 to 2025-09-02 14:00:00,009
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.05|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.05|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.05|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 14:15:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 14:00:00,009 to 2025-09-02 14:15:00,017
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 14:28:06 WARN  [HikariPool-1 housekeeper] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m6s134ms).
2025-09-02 14:28:36 WARN  [virtual-284] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-09-02 14:30:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 14:15:00,017 to 2025-09-02 14:30:00,027
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-02 14:30:03 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=14, lastTimeStamp=1756794603562}] instanceId:[InstanceId{instanceId=**************:30237, stable=false}] @ namespace:[fulfillmen-shop].
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-02 14:30:03 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-02 14:30:05 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 14:30:05 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 14:30:05 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-02 14:30:05 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-09-02 15:39:52 INFO  [background-preinit] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 15:39:53 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 15:39:53 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 15:39:53 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 80 ms. Found 0 Redis repository interfaces.
2025-09-02 15:39:54 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 15:39:54 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-09-02 15:39:55 WARN  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-09-02 15:39:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 15:39:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3155 ms
2025-09-02 15:39:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-09-02 15:39:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-09-02 15:39:55 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.Version - Redisson 3.45.1
2025-09-02 15:39:56 INFO  [redisson-netty-1-7] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 15:39:58 INFO  [redisson-netty-1-20] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-09-02 15:39:58 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-09-02 15:39:59 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-02 15:39:59 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@37191ef0
2025-09-02 15:39:59 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:88312, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=14, lastTimeStamp=1756798799934}] - instanceId:[InstanceId{instanceId=**************:88312, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-09-02 15:40:00 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio - XNIO version 3.8.16.Final
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 122 ms
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-02 15:40:01 INFO  [main] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-09-02 15:40:03 INFO  [vt#1] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 15:40:04 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114941650408292352,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-02 15:40:04 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114941650408292352,SpanId:,BizId:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@405d12dc
2025-09-02 15:40:04 INFO  [scheduling-1] [TenantId:10000,LoginId:,IP:,OS:,Browser:] [TraceId:1114941650408292352,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-02 15:45:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 15:39:58,454 to 2025-09-02 15:45:00,009
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.01|100.00%|             1|             1|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.01|  0.00%|             1|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.01|100.00%|             1|             1|             0|             0|        0.0|          0
cart:                        |      0.03| 50.00%|             2|             1|             0|             0|       71.0|         71
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.03|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local         |      0.03|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.03|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 16:00:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 15:45:00,009 to 2025-09-02 16:00:00,024
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|100.00%|             3|             3|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|100.00%|             3|             3|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|100.00%|             4|             4|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             2|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 16:04:44 WARN  [virtual-327] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-09-02 16:15:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 16:00:00,024 to 2025-09-02 16:15:00,018
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.01|100.00%|             6|             6|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 16:30:00 INFO  [JetCacheDefaultExecutor] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-09-02 16:15:00,018 to 2025-09-02 16:30:00,013
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|100.00%|             3|             3|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-09-02 16:31:55 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-3] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [Thread-4] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=14, lastTimeStamp=1756801915713}] instanceId:[InstanceId{instanceId=**************:88312, stable=false}] @ namespace:[fulfillmen-shop].
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-02 16:31:55 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-02 16:31:57 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-09-02 16:31:57 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-09-02 16:31:57 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-02 16:31:57 INFO  [SpringApplicationShutdownHook] [TenantId:,LoginId:,IP:,OS:,Browser:] [TraceId:,SpanId:,BizId:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
