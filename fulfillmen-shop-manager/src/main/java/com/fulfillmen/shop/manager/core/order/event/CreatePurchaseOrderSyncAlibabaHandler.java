/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TenantWarehouseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO;
import com.fulfillmen.shop.domain.entity.TenantWarehouse;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierMultipleOrdersEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.manager.core.order.status.OrderSyncStatusManager;
import com.fulfillmen.shop.manager.support.alibaba.impl.OrderManager;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * 创建 1688 订单事件
 *
 * <AUTHOR>
 * @date 2025/7/18 17:54
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePurchaseOrderSyncAlibabaHandler {

    private final OrderManager orderManager;
    private final IWmsManager wmsManager;
    private final TzOrderItemMapper orderItemMapper;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TenantWarehouseMapper tenantWarehouseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final OrderSyncStatusManager syncStatusManager;
    private final TransactionTemplate transactionTemplate;
    //    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final Executor virtualThreadExecutor;

    /**
     * 处理订单创建事件
     *
     * @param orderContextDTO 订单上下文
     */
    public void handle(OrderContextDTO orderContextDTO) {
        log.info("开始创建 1688 订单，采购订单号: {}", orderContextDTO.getPurchaseOrderNo());
        // 如果订单不是待支付状态，则不需要创建 1688 订单
        try {
            if (!orderContextDTO.isPurchaseOrderPayCompleted()) {
                log.info("采购订单 {} 未支付，不需要创建 1688 订单", orderContextDTO.getPurchaseOrderNo());
                return;
            }
            createAlibabaOrder(orderContextDTO);
            updatePurchaseOrderStatus(orderContextDTO, orderContextDTO.getSupplierOrders());
            log.info("创建 1688 订单完成 : [{}] ", orderContextDTO.getPurchaseOrderNo());
        } catch (Exception e) {
            log.error("创建 1688 订单失败 : [{}] ", orderContextDTO.getPurchaseOrderNo(), e);
            // FIXME: 2025/7/18 后期开发一个事件通知机制处理
        }
    }

    /**
     * 创建 1688订单
     *
     * @param orderContextDTO 订单上下文
     */
    private void createAlibabaOrder(OrderContextDTO orderContextDTO) {
        List<CompletableFuture<Void>> futures = Lists.newArrayList();
        // 并行创建 1688 订单
        orderContextDTO.getSupplierOrders().forEach(supplierOrder -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 使用事务化的供应商订单处理逻辑
                    submitSupplierOrderToAlibaba(supplierOrder, orderContextDTO);
                } catch (Exception e) {
                    // 处理失败：记录失败状态（在单独事务中）
                    handleSupplierOrderSyncFailure(supplierOrder, e);
                }
            }, virtualThreadExecutor);
            futures.add(future);
        });
        // 等待完成
        futures.forEach(CompletableFuture::join);
    }

    /**
     * 处理供应商订单同步失败
     * 
     * <pre>
     * 在独立事务中处理失败状态更新，避免影响主同步事务的回滚
     * </pre>
     * 
     * @param supplierOrder 失败的供应商订单
     * @param exception     异常信息
     */
    private void handleSupplierOrderSyncFailure(TzOrderSupplier supplierOrder, Exception exception) {
        try {
            log.error("供应商订单 {} 同步失败，使用统一状态管理器记录失败状态",
                supplierOrder.getSupplierOrderNo(), exception);

            // 使用统一状态管理器更新失败状态
            String failureReason = String.format("阿里巴巴订单创建失败: %s",
                exception.getMessage() != null ? exception.getMessage() : exception.getClass().getSimpleName());

            syncStatusManager.updateSupplierSyncStatus(
                supplierOrder,
                OrderSupplierSyncStatusEnums.SYNC_FAILED,
                failureReason,
                null
            );

            // 更新数据库
            orderSupplierMapper.updateById(supplierOrder);

            log.info("供应商订单 {} 失败状态记录完成", supplierOrder.getSupplierOrderNo());

        } catch (Exception e) {
            log.error("记录供应商订单 {} 失败状态时发生异常", supplierOrder.getSupplierOrderNo(), e);
            // 这里不再抛出异常，避免影响其他订单的处理
        }
    }

    /**
     * 提交供应商订单到外部平台
     * 
     * <pre>
     * 修复：添加事务管理，确保数据一致性
     * - 成功时：更新供应商订单和订单项状态
     * - 失败时：正确记录失败状态，不影响已完成操作
     * </pre>
     */
    public void submitSupplierOrderToAlibaba(TzOrderSupplier supplierOrder, OrderContextDTO orderContextDTO) {
        log.info("开始提交供应商订单到外部平台，订单号: {}", supplierOrder.getSupplierOrderNo());

        // 使用编程式事务
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                try {
                    // 构建1688订单创建请求
                    OrderCreateRequestRecord request = buildOrderCreateRequest(supplierOrder, orderContextDTO);
                    // 调用1688订单创建API
                    CreateOrderRespDTO createOrderRespDTO = orderManager.createCrossOrder(request);
                    // FIXME: 2025/7/28 请注意，存在部分商品下单失败。需要额外的处理对应的 商品项信息。
                    if (!CollectionUtils.isEmpty(createOrderRespDTO.getFailedOfferList())) {
                        // TODO：2025/7/18 1. 采购失败的商品，需要处理相关商品逻辑 2. 采购失败的商品，需要通知用户
                        log.warn("部分商品采购失败，请查看并处理 : [{}] ", JacksonUtil.toJsonString(createOrderRespDTO.getFailedOfferList()));
                    }
                    // // 更新外部订单信息
                    String metadataJson = JacksonUtil.toJsonString(createOrderRespDTO);
                    if (createOrderRespDTO.getIsMultipleOrder()) {
                        supplierOrder.setIsMultipleOrders(TzOrderSupplierMultipleOrdersEnum.YES);
                        // 获取多个订单 ID 使用 , 分割
                        String orderIds = createOrderRespDTO.getOrderList().stream()
                            .filter(Objects::nonNull).map(CreateOrderRespDTO.AlibabaCreateOrderRespDTO::getOrderId)
                            .collect(Collectors.joining(","));
                        supplierOrder.setPlatformOrderNo(orderIds);
                    } else {
                        supplierOrder.setIsMultipleOrders(TzOrderSupplierMultipleOrdersEnum.NO);
                        supplierOrder.setPlatformOrderId(createOrderRespDTO.getOrderId());
                    }
                    // 使用统一状态管理器更新同步成功状态
                    syncStatusManager.updateSupplierSyncStatus(
                        supplierOrder,
                        OrderSupplierSyncStatusEnums.SYNCED,
                        "阿里巴巴订单创建成功",
                        createOrderRespDTO.getOrderId()
                    );
                    supplierOrder.setMetadataJson(metadataJson);
                    // 设置金额
                    // 应付金额
                    supplierOrder.setPayableAmountTotal(BigDecimal.valueOf(createOrderRespDTO.getTotalSuccessAmount() / 100));
                    // 运费
                    supplierOrder.setPayableFreightAmount(
                        createOrderRespDTO.getPostFee() != null ? BigDecimal.valueOf(createOrderRespDTO.getPostFee() / 100)
                            : BigDecimal.ZERO);
                    // 商品金额 = 应付金额 - 应付运费
                    supplierOrder.setPayableGoodsAmount(
                        supplierOrder.getPayableAmountTotal().subtract(supplierOrder.getPayableFreightAmount()));
                    orderSupplierMapper.updateById(supplierOrder);
                    // 更新订单项信息
                    orderContextDTO.getOrderItems().forEach(item -> {
                        item.setPlatformOrderId(createOrderRespDTO.getOrderId());
                        item.setStatus(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
                        item.setLogisticsStatus(TzOrderItemLogisticsStatusEnum.NOT_SHIPPED);
                    });
                    tzOrderItemMapper.updateBatchById(orderContextDTO.getOrderItems());
                    log.info("供应商订单提交成功，外部订单ID: {}", createOrderRespDTO.getOrderId());

                } catch (Exception e) {
                    log.error("供应商订单 {} Alibaba同步失败: {}", supplierOrder.getSupplierOrderNo(), e.getMessage(), e);
                    // 抛出异常让事务回滚，失败状态在调用方处理
                    throw new RuntimeException("Alibaba订单同步失败: " + e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 更新成功的订单状态
     *
     * @param orderContextDTO 采购订单
     * @param supplierOrders  供应商订单
     */
    private void updatePurchaseOrderStatus(OrderContextDTO orderContextDTO, List<TzOrderSupplier> supplierOrders) {
        // 如果支付了，则需要处理订单逻辑
        if (!orderContextDTO.isPurchaseOrderPayCompleted()) {
            log.warn("该采购订单未支付 : [{}] ", orderContextDTO.getPurchaseOrderNo());
            return;
        }
        // 获取 wms 订单列表。对应的采购单号
        List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes = this.wmsManager.queryOrderDetail(
            PurchaseOrderDetailReq.builder().nayaPurchaseNo(orderContextDTO.getPurchaseOrderNo()).build());
        if (CollectionUtils.isEmpty(wmsPurchaseOrderDetailsRes)) {
            log.warn("无法获取 wms 采购订单，采购单号 : [{}] ", orderContextDTO.getPurchaseOrderNo());
            return;
        }
        // wms 同步成功状态，并且以 wmsPurchaseNo 作为 key 。 value 是供应商实体。
        Map<String, TzOrderSupplier> wmsPurchaseNoToSupplierOrderMap = supplierOrders.stream()
            .filter(tzOrderSupplier -> Objects.nonNull(tzOrderSupplier.getWmsPurchaseOrderNo()))
            .collect(Collectors.toMap(TzOrderSupplier::getWmsPurchaseOrderNo, order -> order));

        List<WmsPurchaseOrderDetailReq> wmsPurchaseOrderDetailReqList = Lists.newArrayList();
        // 更新成功的订单状态
        wmsPurchaseOrderDetailsRes.forEach(wmsPurchaseOrderDetails -> {
            TzOrderSupplier tzOrderSupplier = wmsPurchaseNoToSupplierOrderMap
                .get(wmsPurchaseOrderDetails.getPurchaseNo());
            WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
                .purchaseNo(wmsPurchaseOrderDetails.getPurchaseNo())
                .shopOrderId(tzOrderSupplier.getSupplierOrderNo())
                .status(WmsOrderStatusEnum.PAID_PENDING_REVIEW)
                // 对应供应商的订单
                .productSalesTotalAmount(tzOrderSupplier.getCustomerGoodsAmount())
                .shippingFee(tzOrderSupplier.getCustomerFreightAmount())
                .serviceFee(tzOrderSupplier.getServiceFee())
                .total(tzOrderSupplier.getCustomerTotalAmount())
                .orderId(Long.valueOf(tzOrderSupplier.getPlatformOrderId()))
                .build();
            wmsPurchaseOrderDetailReqList.add(wmsPurchaseOrderDetailReq);
        });
        // 更新 wms 客户的订单支付
        this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReqList);
    }

    /**
     * 构建1688订单创建请求
     */
    private OrderCreateRequestRecord buildOrderCreateRequest(TzOrderSupplier supplierOrder,
        OrderContextDTO orderContextDTO) {
        log.debug("构建1688订单创建请求，供应商订单ID: {}", supplierOrder.getId());

        // 1. 获取仓库信息
        Long warehouseId = orderContextDTO.getPurchaseOrder().getRecipientWarehouseId();
        TenantWarehouse tenantWarehouse;
        if (warehouseId != null) {
            tenantWarehouse = tenantWarehouseMapper.selectById(warehouseId);
            if (tenantWarehouse == null) {
                log.error("仓库信息不存在，仓库ID: {}", warehouseId);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "仓库");
            }
        } else {
            // 如果没有指定仓库，则使用默认仓库
            LambdaQueryWrapper<TenantWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            tenantWarehouse = tenantWarehouseMapper.selectOne(queryWrapper
                .eq(TenantWarehouse::getTenantId, UserContextHolder.getTenantId())
                .eq(TenantWarehouse::getIsDefault, 1));
            if (tenantWarehouse == null) {
                log.error("请设置租户默认仓库地址，租户ID: {}", UserContextHolder.getTenantId());
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.TENANT_WAREHOUSE_NOT_FOUND);
            }
        }
        // fixed : 创建 1688 订单的时候收件人信息
        String wmsCusCode = UserContextHolder.getWmsCusCodeOrTenantCusCode();
        String contactName = tenantWarehouse.getContactName();
        String receiveAddress = tenantWarehouse.getAddress();
        if (Objects.nonNull(wmsCusCode)) {
            contactName = contactName + " " + wmsCusCode;
            receiveAddress = receiveAddress + "-" + wmsCusCode;
        }
        // 2. 构建收货地址
        OrderCreateRequestRecord.AddressParamRecord addressParam = OrderCreateRequestRecord.AddressParamRecord.builder()
            // 读取用户姓名
            .fullName(contactName)
            // 读取用户手机号
            .mobile(tenantWarehouse.getContactMobile())
            // 读取用户邮编
            .postCode(tenantWarehouse.getPostcode())
            // 读取用户城市
            .cityText(tenantWarehouse.getCity())
            // 读取用户省份
            .provinceText(tenantWarehouse.getProvince())
            // 读取用户区县
            .areaText(tenantWarehouse.getDistrict())
            // 读取用户详细地址
            .address(receiveAddress)
            // 读取用户地区码
            .districtCode(tenantWarehouse.getDistrictCode())
            .build();

        // 3. 构建商品列表
        List<OrderCreateRequestRecord.CargoParamRecord> cargoList = buildCargoList(supplierOrder);
        // 4. 设置采购单号，为下游单号
        String outOrderId = orderContextDTO.getPurchaseOrder().getPurchaseOrderNo();

        return OrderCreateRequestRecord.builder()
            .flow("general")
            .addressParam(addressParam)
            .cargoParamList(cargoList)
            .outOrderId(outOrderId)
            .build();
    }

    /**
     * 根据供应商订单构建商品列表
     */
    private List<OrderCreateRequestRecord.CargoParamRecord> buildCargoList(TzOrderSupplier supplierOrder) {
        // 查询供应商订单对应的商品明细
        List<TzOrderItem> orderItems = orderItemMapper.selectList(
            new LambdaQueryWrapper<TzOrderItem>().eq(TzOrderItem::getSupplierOrderId, supplierOrder.getId()));

        return orderItems.stream()
            .map(orderItem -> {
                //
                String specId = orderItem.getIsSingleItem() == TzProductSpuSingleItemEnum.YES ? null
                    : orderItem.getPlatformSpecId();
                // 如果是单品，则不需要设置specId
                // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                // .openOfferId(orderItem.getOpenOfferId())
                // .outMemberId(orderItem.getOutMemberId())
                return OrderCreateRequestRecord.CargoParamRecord.builder()
                    .offerId(Long.valueOf(orderItem.getPlatformProductId()))
                    // 如果是单品，则不需要设置specId
                    .specId(specId)
                    .quantity(orderItem.getQuantity().doubleValue())
                    // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                    // .openOfferId(orderItem.getOpenOfferId())
                    // .outMemberId(orderItem.getOutMemberId())
                    .build();
            })
            .collect(Collectors.toList());
    }
}
