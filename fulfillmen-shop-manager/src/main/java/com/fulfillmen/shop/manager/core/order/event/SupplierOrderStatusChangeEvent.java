/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.time.LocalDateTime;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 供应商订单状态变更事件
 *
 * <pre>
 * 当供应商订单状态发生变化时发布此事件，用于：
 * 1. 触发采购订单状态聚合
 * 2. 记录供应商订单状态变更日志
 * 3. 发送供应商订单级别的通知
 * 4. 同步外部系统状态
 * 5. 更新相关统计数据
 * 6. 触发业务规则检查
 *
 * 架构定位：
 * - 三层状态聚合体系的中间层事件
 * - 连接订单项和采购订单的桥梁
 * - 支持由订单项聚合和直接状态变更触发
 * - 自动触发上层采购订单状态聚合
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/31
 * @description 供应商订单状态变更事件，三层状态聚合的中间层
 * @since 1.0.0
 */
@Getter
public class SupplierOrderStatusChangeEvent extends ApplicationEvent {

    /**
     * 供应商订单ID
     */
    private final Long supplierOrderId;

    /**
     * 采购订单ID - 用于触发上级聚合
     */
    private final Long purchaseOrderId;

    /**
     * 供应商ID - 用于统计和分析
     */
    private final String supplierId;

    /**
     * 原始状态
     */
    private final TzOrderSupplierStatusEnum originalStatus;

    /**
     * 新状态
     */
    private final TzOrderSupplierStatusEnum newStatus;

    /**
     * 状态变更原因
     */
    private final String changeReason;

    /**
     * 状态变更时间
     */
    private final LocalDateTime changeTime;

    /**
     * 事件链ID - 用于防循环和事件追踪
     */
    private final String eventChainId;

    /**
     * 变更来源
     */
    private final ChangeSource changeSource;

    /**
     * 触发方式
     */
    private final TriggerType triggerType;

    /**
     * 租户ID
     */
    private final String tenantId;

    /**
     * 用户ID
     */
    private final String userId;

    /**
     * 聚合上下文信息 - 当由订单项聚合触发时提供
     */
    private final AggregationContext aggregationContext;

    /**
     * 构造函数
     *
     * @param supplierOrderId    供应商订单ID
     * @param purchaseOrderId    采购订单ID
     * @param supplierId         供应商ID
     * @param originalStatus     原始状态
     * @param newStatus          新状态
     * @param changeReason       变更原因
     * @param changeTime         变更时间
     * @param eventChainId       事件链ID
     * @param changeSource       变更来源
     * @param triggerType        触发方式
     * @param tenantId           租户ID
     * @param userId             用户ID
     * @param aggregationContext 聚合上下文
     */
    public SupplierOrderStatusChangeEvent(Long supplierOrderId,
        Long purchaseOrderId,
        String supplierId,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        String changeReason,
        LocalDateTime changeTime,
        String eventChainId,
        ChangeSource changeSource,
        TriggerType triggerType,
        String tenantId,
        String userId,
        AggregationContext aggregationContext) {
        super(supplierOrderId);
        this.supplierOrderId = supplierOrderId;
        this.purchaseOrderId = purchaseOrderId;
        this.supplierId = supplierId;
        this.originalStatus = originalStatus;
        this.newStatus = newStatus;
        this.changeReason = changeReason;
        this.changeTime = changeTime;
        this.eventChainId = eventChainId;
        this.changeSource = changeSource;
        this.triggerType = triggerType;
        this.tenantId = tenantId;
        this.userId = userId;
        this.aggregationContext = aggregationContext;
    }

    /**
     * 简化构造函数 - 直接状态变更
     */
    public SupplierOrderStatusChangeEvent(Long supplierOrderId,
        Long purchaseOrderId,
        String supplierId,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        ChangeSource changeSource,
        String tenantId,
        String userId) {
        this(supplierOrderId, purchaseOrderId, supplierId, originalStatus, newStatus,
            changeReason, LocalDateTime.now(), eventChainId, changeSource,
            TriggerType.DIRECT_CHANGE, tenantId, userId, null);
    }

    /**
     * 聚合触发构造函数
     */
    public SupplierOrderStatusChangeEvent(Long supplierOrderId,
        Long purchaseOrderId,
        String supplierId,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        ChangeSource changeSource,
        String tenantId,
        String userId,
        AggregationContext aggregationContext) {
        this(supplierOrderId, purchaseOrderId, supplierId, originalStatus, newStatus,
            changeReason, LocalDateTime.now(), eventChainId, changeSource,
            TriggerType.ITEM_AGGREGATION, tenantId, userId, aggregationContext);
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange() {
        return newStatus == TzOrderSupplierStatusEnum.COMPLETED ||
            newStatus == TzOrderSupplierStatusEnum.CANCELLED ||
            newStatus == TzOrderSupplierStatusEnum.SHIPPED;
    }

    /**
     * 判断是否需要触发采购订单聚合
     */
    public boolean shouldTriggerPurchaseAggregation() {
        // 所有状态变更都应该触发采购订单聚合，除非是测试场景
        return changeSource != ChangeSource.TEST_SCENARIO;
    }

    /**
     * 判断是否由订单项聚合触发
     */
    public boolean isTriggeredByItemAggregation() {
        return triggerType == TriggerType.ITEM_AGGREGATION;
    }

    /**
     * 获取用户友好的状态变更描述
     */
    public String getUserFriendlyDescription() {
        return switch (newStatus) {
            case PENDING_PAYMENT -> "供应商订单待支付";
            case PENDING_SHIPMENT -> "供应商订单待发货";
            case PARTIALLY_SHIPPED -> "供应商订单部分发货";
            case SHIPPED -> "供应商订单已发货";
            case WAREHOUSE_PENDING_RECEIPT -> "商品运输中，等待仓库签收";
            case WAREHOUSE_RECEIVED -> "仓库已签收，正在质检中";
            case COMPLETED -> "供应商订单已完成";
            case CANCELLED -> "供应商订单已取消";
        };
    }

    /**
     * 获取事件的唯一标识
     */
    public String getEventId() {
        return String.format("SUPPLIER_ORDER_STATUS_CHANGE-%d-%s-%d",
            supplierOrderId, newStatus.name(), changeTime.toEpochSecond(java.time.ZoneOffset.UTC));
    }

    /**
     * 获取状态变更的严重程度
     */
    public StatusChangeSeverity getSeverity() {
        return switch (newStatus) {
            case CANCELLED -> StatusChangeSeverity.CRITICAL;
            case SHIPPED, COMPLETED -> StatusChangeSeverity.HIGH;
            case WAREHOUSE_RECEIVED, PARTIALLY_SHIPPED -> StatusChangeSeverity.MEDIUM;
            default -> StatusChangeSeverity.LOW;
        };
    }

    /**
     * 变更来源枚举
     */
    public enum ChangeSource {

        /**
         * 订单项聚合触发
         */
        ITEM_AGGREGATION("订单项聚合"),

        /**
         * 系统自动处理
         */
        SYSTEM_AUTO("系统自动"),

        /**
         * 用户手动操作
         */
        USER_MANUAL("用户手动"),

        /**
         * 外部系统同步
         */
        EXTERNAL_SYNC("外部同步"),

        /**
         * 供应商平台同步
         */
        SUPPLIER_PLATFORM_SYNC("供应商平台同步"),

        /**
         * WMS系统同步
         */
        WMS_SYNC("WMS同步"),

        /**
         * 定时任务处理
         */
        SCHEDULED_TASK("定时任务"),

        /**
         * API接口调用
         */
        API_CALL("API调用"),

        /**
         * 测试场景
         */
        TEST_SCENARIO("测试场景"),

        /**
         * 数据修复
         */
        DATA_REPAIR("数据修复");

        private final String description;

        ChangeSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 触发方式枚举
     */
    public enum TriggerType {

        /**
         * 直接状态变更
         */
        DIRECT_CHANGE("直接变更"),

        /**
         * 订单项聚合触发
         */
        ITEM_AGGREGATION("订单项聚合"),

        /**
         * 外部事件触发
         */
        EXTERNAL_EVENT("外部事件"),

        /**
         * 系统规则触发
         */
        SYSTEM_RULE("系统规则");

        private final String description;

        TriggerType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 状态变更严重程度枚举
     */
    public enum StatusChangeSeverity {

        LOW(1, "低"),
        MEDIUM(2, "中"),
        HIGH(3, "高"),
        CRITICAL(4, "关键");

        private final int level;
        private final String description;

        StatusChangeSeverity(int level, String description) {
            this.level = level;
            this.description = description;
        }

        public int getLevel() {
            return level;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 聚合上下文信息
     */
    @Getter
    public static class AggregationContext {

        /**
         * 触发聚合的订单项数量
         */
        private final int triggerItemCount;

        /**
         * 聚合时的订单项总数
         */
        private final int totalItemCount;

        /**
         * 聚合算法版本
         */
        private final String algorithmVersion;

        /**
         * 聚合完成率
         */
        private final double completionRate;

        /**
         * 聚合详情描述
         */
        private final String aggregationDetails;

        public AggregationContext(int triggerItemCount,
            int totalItemCount,
            String algorithmVersion,
            double completionRate,
            String aggregationDetails) {
            this.triggerItemCount = triggerItemCount;
            this.totalItemCount = totalItemCount;
            this.algorithmVersion = algorithmVersion;
            this.completionRate = completionRate;
            this.aggregationDetails = aggregationDetails;
        }
    }

    @Override
    public String toString() {
        return String.format("SupplierOrderStatusChangeEvent{supplierOrderId=%d, purchaseOrderId=%d, " +
            "supplier='%s', %s -> %s, reason='%s', source=%s, trigger=%s, severity=%s, chainId='%s', time=%s}",
            supplierOrderId, purchaseOrderId, supplierId,
            originalStatus.getDescription(), newStatus.getDescription(),
            changeReason, changeSource.getDescription(), triggerType.getDescription(),
            getSeverity().getDescription(), eventChainId, changeTime);
    }
}
