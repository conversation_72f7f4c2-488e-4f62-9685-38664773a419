/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import java.time.LocalDateTime;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 订单状态变更事件 - Manager层
 *
 * <pre>
 * 当订单状态发生变化时发布此事件，用于：
 * 1. 通知相关系统和服务
 * 2. 记录状态变更日志
 * 3. 触发后续业务流程
 * 4. 发送用户通知
 * 5. 更新缓存和统计数据
 * 6. 集成外部系统
 *
 * 架构定位：
 * - Manager层负责业务事件的定义和发布
 * - 事件包含完整的业务上下文信息
 * - 支持异步和同步的事件处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态变更事件，用于事件驱动的业务流程
 * @since 1.0.0
 */
@Getter
public class OrderStatusChangeEvent extends ApplicationEvent {

    /**
     * 采购订单ID
     */
    private final Long purchaseOrderId;

    /**
     * 原始状态
     */
    private final TzOrderPurchaseStatusEnum originalStatus;

    /**
     * 新状态
     */
    private final TzOrderPurchaseStatusEnum newStatus;

    /**
     * 状态变更原因
     */
    private final String changeReason;

    /**
     * 状态变更时间
     */
    private final LocalDateTime changeTime;

    /**
     * 事件类型
     */
    private final EventType eventType;

    /**
     * 事件链ID - 用于防循环和事件追踪
     */
    private final String eventChainId;

    /**
     * 变更来源
     */
    private final ChangeSource changeSource;

    /**
     * 触发方式
     */
    private final TriggerType triggerType;

    /**
     * 租户ID
     */
    private final String tenantId;

    /**
     * 用户ID
     */
    private final String userId;

    /**
     * 完整构造函数
     *
     * @param purchaseOrderId 采购订单ID
     * @param originalStatus  原始状态
     * @param newStatus       新状态
     * @param changeReason    变更原因
     * @param changeTime      变更时间
     * @param eventChainId    事件链ID
     * @param changeSource    变更来源
     * @param triggerType     触发方式
     * @param tenantId        租户ID
     * @param userId          用户ID
     */
    public OrderStatusChangeEvent(Long purchaseOrderId,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        String changeReason,
        LocalDateTime changeTime,
        String eventChainId,
        ChangeSource changeSource,
        TriggerType triggerType,
        String tenantId,
        String userId) {
        super(purchaseOrderId);
        this.purchaseOrderId = purchaseOrderId;
        this.originalStatus = originalStatus;
        this.newStatus = newStatus;
        this.changeReason = changeReason;
        this.changeTime = changeTime;
        this.eventType = determineEventType(originalStatus, newStatus);
        this.eventChainId = eventChainId;
        this.changeSource = changeSource;
        this.triggerType = triggerType;
        this.tenantId = tenantId;
        this.userId = userId;
    }

    /**
     * 向后兼容的构造函数
     *
     * @param purchaseOrderId 采购订单ID
     * @param originalStatus  原始状态
     * @param newStatus       新状态
     * @param changeReason    变更原因
     * @param changeTime      变更时间
     */
    public OrderStatusChangeEvent(Long purchaseOrderId,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        String changeReason,
        LocalDateTime changeTime) {
        this(purchaseOrderId, originalStatus, newStatus, changeReason, changeTime,
            null, ChangeSource.DIRECT_CHANGE, TriggerType.DIRECT_CHANGE, null, null);
    }

    /**
     * 简化构造函数，使用当前时间
     */
    public OrderStatusChangeEvent(Long purchaseOrderId,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        String changeReason) {
        this(purchaseOrderId, originalStatus, newStatus, changeReason, LocalDateTime.now());
    }

    /**
     * 供应商聚合触发构造函数
     */
    public OrderStatusChangeEvent(Long purchaseOrderId,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        ChangeSource changeSource,
        String tenantId,
        String userId) {
        this(purchaseOrderId, originalStatus, newStatus, changeReason, LocalDateTime.now(),
            eventChainId, changeSource, TriggerType.SUPPLIER_AGGREGATION, tenantId, userId);
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange() {
        return eventType == EventType.PAYMENT_COMPLETED ||
            eventType == EventType.ORDER_COMPLETED ||
            eventType == EventType.ORDER_CANCELLED;
    }

    /**
     * 判断是否需要用户通知
     */
    public boolean shouldNotifyUser() {
        return eventType == EventType.PAYMENT_COMPLETED ||
            eventType == EventType.SHIPPED ||
            eventType == EventType.ORDER_COMPLETED ||
            eventType == EventType.ORDER_CANCELLED;
    }

    /**
     * 获取用户友好的状态变更描述
     */
    public String getUserFriendlyDescription() {
        return switch (eventType) {
            case PAYMENT_COMPLETED -> "支付成功，我们正在为您准备商品";
            case PROCUREMENT_STARTED -> "开始采购您的商品";
            case SHIPPED -> "商品已发货，正在运输途中";
            case DELIVERED_TO_WAREHOUSE -> "商品已到达仓库，正在质检中";
            case ORDER_COMPLETED -> "订单已完成，感谢您的购买";
            case ORDER_CANCELLED -> "订单已取消";
            case PARTIAL_FULFILLMENT -> "部分商品已处理完成";
            case STATUS_PROGRESSION -> String.format("订单状态已更新：%s", newStatus.getDescription());
        };
    }

    /**
     * 确定事件类型
     */
    private EventType determineEventType(TzOrderPurchaseStatusEnum originalStatus, TzOrderPurchaseStatusEnum newStatus) {
        if (newStatus == TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
            return EventType.ORDER_CANCELLED;
        }

        return switch (newStatus) {
            case PAYMENT_COMPLETED -> EventType.PAYMENT_COMPLETED;
            case PROCUREMENT_IN_PROGRESS -> EventType.PROCUREMENT_STARTED;
//            case PARTIALLY_FULFILLED -> EventType.PARTIAL_FULFILLMENT;
            case SUPPLIER_SHIPPED -> EventType.SHIPPED;
            case WAREHOUSE_RECEIVED -> EventType.DELIVERED_TO_WAREHOUSE;
            case IN_STOCK -> EventType.ORDER_COMPLETED;
            default -> EventType.STATUS_PROGRESSION;
        };
    }

    /**
     * 事件类型枚举
     */
    @Getter
    public enum EventType {

        /**
         * 支付完成
         */
        PAYMENT_COMPLETED("支付完成"),

        /**
         * 开始采购
         */
        PROCUREMENT_STARTED("开始采购"),

        /**
         * 部分履约
         */
        PARTIAL_FULFILLMENT("部分履约"),

        /**
         * 已发货
         */
        SHIPPED("已发货"),

        /**
         * 已送达仓库
         */
        DELIVERED_TO_WAREHOUSE("已送达仓库"),

        /**
         * 订单完成
         */
        ORDER_COMPLETED("订单完成"),

        /**
         * 订单取消
         */
        ORDER_CANCELLED("订单取消"),

        /**
         * 一般状态进展
         */
        STATUS_PROGRESSION("状态进展");

        private final String description;

        EventType(String description) {
            this.description = description;
        }

    }

    /**
     * 变更来源枚举
     */
    public enum ChangeSource {

        /**
         * 直接状态变更
         */
        DIRECT_CHANGE("直接变更"),

        /**
         * 供应商订单聚合触发
         */
        SUPPLIER_AGGREGATION("供应商聚合"),

        /**
         * 系统自动处理
         */
        SYSTEM_AUTO("系统自动"),

        /**
         * 用户手动操作
         */
        USER_MANUAL("用户手动"),

        /**
         * 外部系统同步
         */
        EXTERNAL_SYNC("外部同步"),

        /**
         * 定时任务处理
         */
        SCHEDULED_TASK("定时任务"),

        /**
         * API接口调用
         */
        API_CALL("API调用"),

        /**
         * 测试场景
         */
        TEST_SCENARIO("测试场景");

        private final String description;

        ChangeSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 触发方式枚举
     */
    public enum TriggerType {

        /**
         * 直接状态变更
         */
        DIRECT_CHANGE("直接变更"),

        /**
         * 供应商订单聚合触发
         */
        SUPPLIER_AGGREGATION("供应商聚合"),

        /**
         * 外部事件触发
         */
        EXTERNAL_EVENT("外部事件"),

        /**
         * 系统规则触发
         */
        SYSTEM_RULE("系统规则"),

        /**
         * 批量处理触发
         */
        BATCH_PROCESS("批量处理");

        private final String description;

        TriggerType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 判断是否由供应商聚合触发
     */
    public boolean isTriggeredBySupplierAggregation() {
        return triggerType == TriggerType.SUPPLIER_AGGREGATION;
    }

    /**
     * 判断是否需要防循环检查
     */
    public boolean needsCircularCheck() {
        return eventChainId != null && !eventChainId.isEmpty();
    }

    /**
     * 获取事件的唯一标识
     */
    public String getEventId() {
        return String.format("PURCHASE_ORDER_STATUS_CHANGE-%d-%s-%d",
            purchaseOrderId, newStatus.name(), changeTime.toEpochSecond(java.time.ZoneOffset.UTC));
    }

    @Override
    public String toString() {
        return String.format("OrderStatusChangeEvent{orderId=%d, %s -> %s, reason='%s', type=%s, " +
            "source=%s, trigger=%s, chainId='%s', time=%s}",
            purchaseOrderId,
            originalStatus.getDescription(),
            newStatus.getDescription(),
            changeReason,
            eventType,
            changeSource != null ? changeSource.getDescription() : "N/A",
            triggerType != null ? triggerType.getDescription() : "N/A",
            eventChainId,
            changeTime);
    }
}
