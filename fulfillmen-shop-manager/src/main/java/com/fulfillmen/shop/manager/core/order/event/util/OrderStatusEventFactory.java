/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event.util;

import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.SupplierOrderStatusChangeEvent;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单状态事件工厂类
 *
 * <pre>
 * 统一创建各种订单状态变更事件的工厂类，提供：
 * 1. 标准化的事件创建流程
 * 2. 统一的参数校验和默认值处理
 * 3. 事件链ID自动生成和管理
 * 4. 上下文信息自动注入
 * 5. 批量事件创建支持
 * 6. 事件模板和预设配置
 *
 * 使用方式：
 * <code>
 * // 创建订单项状态变更事件
 * OrderItemStatusChangeEvent event = eventFactory.createOrderItemStatusChangeEvent(
 * orderItemId, supplierOrderId, purchaseOrderId,
 * oldStatus, newStatus, reason, changeSource);
 *
 * // 创建带事件链的供应商订单状态变更事件
 * SupplierOrderStatusChangeEvent event = eventFactory.createSupplierOrderStatusChangeEvent(
 * supplierOrderId, purchaseOrderId, supplierId,
 * oldStatus, newStatus, reason, eventChainId, changeSource, aggregationContext);
 * </code>
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/31
 * @description 订单状态事件工厂，简化事件创建和管理
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderStatusEventFactory {

    // ========== 订单项状态变更事件创建 ==========

    /**
     * 创建订单项状态变更事件（完整参数）
     *
     * @param orderItemId     订单项ID
     * @param supplierOrderId 供应商订单ID
     * @param purchaseOrderId 采购订单ID
     * @param originalStatus  原始状态
     * @param newStatus       新状态
     * @param changeReason    变更原因
     * @param changeTime      变更时间
     * @param eventChainId    事件链ID
     * @param changeSource    变更来源
     * @param tenantId        租户ID
     * @param userId          用户ID
     * @return 订单项状态变更事件
     */
    public OrderItemStatusChangeEvent createOrderItemStatusChangeEvent(Long orderItemId,
        Long supplierOrderId,
        Long purchaseOrderId,
        TzOrderItemStatusEnum originalStatus,
        TzOrderItemStatusEnum newStatus,
        String changeReason,
        LocalDateTime changeTime,
        String eventChainId,
        OrderItemStatusChangeEvent.ChangeSource changeSource,
        String tenantId,
        String userId) {

        // 参数校验
        validateBasicParameters(orderItemId, "orderItemId");
        validateBasicParameters(supplierOrderId, "supplierOrderId");
        validateBasicParameters(purchaseOrderId, "purchaseOrderId");
        validateStatusChange(originalStatus, newStatus, "订单项状态");

        // 使用默认值填充空参数
        changeTime = changeTime != null ? changeTime : LocalDateTime.now();
        eventChainId = eventChainId != null ? eventChainId : generateEventChainId();
        tenantId = tenantId != null ? tenantId : getCurrentTenantId();
        userId = userId != null ? userId : getCurrentUserId();
        changeReason = changeReason != null ? changeReason : generateDefaultChangeReason(originalStatus, newStatus);

        return new OrderItemStatusChangeEvent(
            orderItemId, supplierOrderId, purchaseOrderId,
            originalStatus, newStatus, changeReason, changeTime,
            eventChainId, changeSource, tenantId, userId
        );
    }

    /**
     * 创建订单项状态变更事件（简化版本）
     */
    public OrderItemStatusChangeEvent createOrderItemStatusChangeEvent(Long orderItemId,
        Long supplierOrderId,
        Long purchaseOrderId,
        TzOrderItemStatusEnum originalStatus,
        TzOrderItemStatusEnum newStatus,
        String changeReason,
        OrderItemStatusChangeEvent.ChangeSource changeSource) {

        return createOrderItemStatusChangeEvent(
            orderItemId, supplierOrderId, purchaseOrderId,
            originalStatus, newStatus, changeReason, null, null,
            changeSource, null, null
        );
    }

    /**
     * 创建带事件链的订单项状态变更事件
     */
    public OrderItemStatusChangeEvent createOrderItemStatusChangeEventWithChain(Long orderItemId,
        Long supplierOrderId,
        Long purchaseOrderId,
        TzOrderItemStatusEnum originalStatus,
        TzOrderItemStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        OrderItemStatusChangeEvent.ChangeSource changeSource) {

        return createOrderItemStatusChangeEvent(
            orderItemId, supplierOrderId, purchaseOrderId,
            originalStatus, newStatus, changeReason, null, eventChainId,
            changeSource, null, null
        );
    }

    /**
     * 创建批量订单项状态变更事件
     *
     * @param batchId         批次ID
     * @param supplierOrderId 供应商订单ID
     * @param purchaseOrderId 采购订单ID
     * @param statusChanges   状态变更列表
     * @param eventChainId    事件链ID
     * @param changeSource    变更来源
     * @return 批量订单项状态变更事件
     */
    public OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent createBatchOrderItemStatusChangeEvent(
        String batchId,
        Long supplierOrderId,
        Long purchaseOrderId,
        List<OrderItemStatusChangeEvent.OrderItemStatusChange> statusChanges,
        String eventChainId,
        OrderItemStatusChangeEvent.ChangeSource changeSource) {

        // 参数校验
        validateBasicParameters(batchId, "batchId");
        validateBasicParameters(supplierOrderId, "supplierOrderId");
        validateBasicParameters(purchaseOrderId, "purchaseOrderId");
        if (statusChanges == null || statusChanges.isEmpty()) {
            throw new IllegalArgumentException("状态变更列表不能为空");
        }

        // 使用默认值
        eventChainId = eventChainId != null ? eventChainId : generateEventChainId();

        return new OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent(
            batchId, supplierOrderId, purchaseOrderId, statusChanges,
            eventChainId, changeSource, getCurrentTenantId(), getCurrentUserId()
        );
    }

    // ========== 供应商订单状态变更事件创建 ==========

    /**
     * 创建供应商订单状态变更事件
     */
    public SupplierOrderStatusChangeEvent createSupplierOrderStatusChangeEvent(Long supplierOrderId,
        Long purchaseOrderId,
        String supplierId,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        SupplierOrderStatusChangeEvent.ChangeSource changeSource,
        SupplierOrderStatusChangeEvent.AggregationContext aggregationContext) {

        // 参数校验
        validateBasicParameters(supplierOrderId, "supplierOrderId");
        validateBasicParameters(purchaseOrderId, "purchaseOrderId");
        validateStatusChange(originalStatus, newStatus, "供应商订单状态");

        // 使用默认值
        eventChainId = eventChainId != null ? eventChainId : generateEventChainId();
        changeReason = changeReason != null ? changeReason : generateDefaultChangeReason(originalStatus, newStatus);

        return new SupplierOrderStatusChangeEvent(
            supplierOrderId, purchaseOrderId, supplierId,
            originalStatus, newStatus, changeReason, eventChainId,
            changeSource, getCurrentTenantId(), getCurrentUserId(), aggregationContext
        );
    }

    /**
     * 创建供应商订单状态变更事件（简化版本）
     */
    public SupplierOrderStatusChangeEvent createSupplierOrderStatusChangeEvent(Long supplierOrderId,
        Long purchaseOrderId,
        String supplierId,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        String changeReason,
        SupplierOrderStatusChangeEvent.ChangeSource changeSource) {

        return createSupplierOrderStatusChangeEvent(
            supplierOrderId, purchaseOrderId, supplierId,
            originalStatus, newStatus, changeReason, null, changeSource, null
        );
    }

    // ========== 采购订单状态变更事件创建 ==========

    /**
     * 创建采购订单状态变更事件
     */
    public OrderStatusChangeEvent createPurchaseOrderStatusChangeEvent(Long purchaseOrderId,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        OrderStatusChangeEvent.ChangeSource changeSource) {

        // 参数校验
        validateBasicParameters(purchaseOrderId, "purchaseOrderId");
        validateStatusChange(originalStatus, newStatus, "采购订单状态");

        // 使用默认值
        eventChainId = eventChainId != null ? eventChainId : generateEventChainId();
        changeReason = changeReason != null ? changeReason : generateDefaultChangeReason(originalStatus, newStatus);

        return new OrderStatusChangeEvent(
            purchaseOrderId, originalStatus, newStatus, changeReason,
            eventChainId, changeSource, getCurrentTenantId(), getCurrentUserId()
        );
    }

    /**
     * 创建采购订单状态变更事件（简化版本）
     */
    public OrderStatusChangeEvent createPurchaseOrderStatusChangeEvent(Long purchaseOrderId,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        String changeReason) {

        return createPurchaseOrderStatusChangeEvent(
            purchaseOrderId, originalStatus, newStatus, changeReason, null,
            OrderStatusChangeEvent.ChangeSource.DIRECT_CHANGE
        );
    }

    // ========== 工具方法 ==========

    /**
     * 生成事件链ID
     */
    public String generateEventChainId() {
        return "CHAIN-" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase() +
            "-" + System.currentTimeMillis();
    }

    /**
     * 生成批次ID
     */
    public String generateBatchId() {
        return "BATCH-" + UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase() +
            "-" + System.currentTimeMillis();
    }

    /**
     * 获取当前租户ID
     */
    private String getCurrentTenantId() {
        try {
            return EnhancedTenantContextHolder.getCurrentTenantId();
        } catch (Exception e) {
            log.debug("无法获取租户ID，使用默认值: {}", e.getMessage());
            return "0";
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            return String.valueOf(UserContextHolder.getUserId());
        } catch (Exception e) {
            log.debug("无法获取用户ID，使用默认值: {}", e.getMessage());
            return "0";
        }
    }

    /**
     * 校验基础参数
     */
    private void validateBasicParameters(Object value, String paramName) {
        if (value == null) {
            throw new IllegalArgumentException(paramName + " 不能为空");
        }
        if (value instanceof String && ((String) value).trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + " 不能为空字符串");
        }
    }

    /**
     * 校验状态变更
     */
    private <T extends Enum<T>> void validateStatusChange(T originalStatus, T newStatus, String statusType) {
        if (originalStatus == null) {
            throw new IllegalArgumentException(statusType + " 原始状态不能为空");
        }
        if (newStatus == null) {
            throw new IllegalArgumentException(statusType + " 新状态不能为空");
        }
        if (originalStatus.equals(newStatus)) {
            log.warn("{}状态未发生变更: {}", statusType, originalStatus);
        }
    }

    /**
     * 生成默认变更原因
     */
    private <T extends Enum<T>> String generateDefaultChangeReason(T originalStatus, T newStatus) {
        return String.format("状态变更: %s -> %s", originalStatus.name(), newStatus.name());
    }

    /**
     * 创建聚合上下文
     */
    public SupplierOrderStatusChangeEvent.AggregationContext createAggregationContext(
        int triggerItemCount,
        int totalItemCount,
        String algorithmVersion,
        double completionRate,
        String aggregationDetails) {

        return new SupplierOrderStatusChangeEvent.AggregationContext(
            triggerItemCount, totalItemCount, algorithmVersion, completionRate, aggregationDetails
        );
    }

    /**
     * 创建订单项状态变更记录
     */
    public OrderItemStatusChangeEvent.OrderItemStatusChange createOrderItemStatusChange(
        Long orderItemId,
        TzOrderItemStatusEnum originalStatus,
        TzOrderItemStatusEnum newStatus,
        String changeReason) {

        return new OrderItemStatusChangeEvent.OrderItemStatusChange(
            orderItemId, originalStatus, newStatus, changeReason
        );
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange(TzOrderItemStatusEnum status) {
        return status == TzOrderItemStatusEnum.COMPLETED ||
            status == TzOrderItemStatusEnum.CANCELLED ||
            status == TzOrderItemStatusEnum.FAILED;
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange(TzOrderSupplierStatusEnum status) {
        return status == TzOrderSupplierStatusEnum.COMPLETED ||
            status == TzOrderSupplierStatusEnum.CANCELLED ||
            status == TzOrderSupplierStatusEnum.SHIPPED;
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange(TzOrderPurchaseStatusEnum status) {
        return status == TzOrderPurchaseStatusEnum.ORDER_CANCELLED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK ||
            status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
    }
}
