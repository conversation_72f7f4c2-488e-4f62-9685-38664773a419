/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.manager.core.order.OrderEventPublisher;
import com.fulfillmen.shop.manager.core.order.status.OrderStatusAggregator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 供应商订单状态变更事件监听器
 *
 * <pre>
 * 处理供应商订单状态变更事件，执行以下业务操作：
 * 1. 记录供应商订单状态变更日志
 * 2. 调用采购订单状态聚合算法
 * 3. 发布采购订单状态变更事件（如果聚合结果有变化）
 * 4. 防循环触发机制
 * 5. 同步外部系统状态
 * 6. 更新相关统计数据
 * 7. 异步处理以提高系统响应性能
 *
 * 架构定位：
 * - 三层状态聚合体系的第二环节
 * - 连接供应商订单状态变更和采购订单状态聚合的桥梁
 * - 处理由订单项聚合和直接状态变更触发的事件
 * - 支持事件链追踪和防循环机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/31
 * @description 供应商订单状态变更事件监听器，三层状态聚合的第二层处理器
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SupplierOrderStatusChangeEventListener {

    private final TzOrderSupplierMapper supplierOrderMapper;
    private final TzOrderPurchaseMapper purchaseOrderMapper;
    private final OrderStatusAggregator orderStatusAggregator;
    private final OrderEventPublisher orderEventPublisher;

    /**
     * 防循环触发的事件链集合
     */
    private final Set<String> processingEventChains = ConcurrentHashMap.newKeySet();

    /**
     * 处理供应商订单状态变更事件（异步）
     *
     * @param event 供应商订单状态变更事件
     */
    @EventListener
    @Async
    public void handleSupplierOrderStatusChangeEvent(SupplierOrderStatusChangeEvent event) {
        log.info("收到供应商订单状态变更事件: {}", event);

        try {
            // 防循环检查
            if (shouldSkipProcessing(event)) {
                log.debug("跳过事件处理以防止循环: eventChainId={}", event.getEventChainId());
                return;
            }

            // 设置上下文
            setProcessingContext(event);

            // 记录事件链处理
            markEventChainProcessing(event.getEventChainId());

            // 1. 记录供应商订单状态变更日志
            logSupplierOrderStatusChange(event);

            // 2. 检查是否需要触发采购订单聚合
            if (!event.shouldTriggerPurchaseAggregation()) {
                log.debug("供应商订单状态变更不需要触发采购订单聚合: {}", event.getSupplierOrderId());
                return;
            }

            // 3. 同步外部系统状态（如果是关键状态变更）
            if (event.isCriticalStatusChange()) {
                syncExternalSystem(event);
            }

            // 4. 执行采购订单状态聚合
            processPurchaseOrderAggregation(event);

            // 5. 更新统计数据
            updateStatistics(event);

            log.debug("供应商订单状态变更事件处理完成: {}", event.getSupplierOrderId());

        } catch (Exception e) {
            log.error("处理供应商订单状态变更事件失败: {}", event, e);
        } finally {
            // 清理事件链标记
            unmarkEventChainProcessing(event.getEventChainId());
            clearProcessingContext();
        }
    }

    /**
     * 处理采购订单状态聚合
     */
    private void processPurchaseOrderAggregation(SupplierOrderStatusChangeEvent event) {
        try {
            // 获取当前采购订单信息
            TzOrderPurchase purchaseOrder = getPurchaseOrder(event.getPurchaseOrderId());
            if (purchaseOrder == null) {
                log.warn("未找到采购订单: purchaseOrderId={}", event.getPurchaseOrderId());
                return;
            }

            TzOrderPurchaseStatusEnum currentStatus = purchaseOrder.getOrderStatus();

            // 获取同一采购订单下的所有供应商订单
            List<TzOrderSupplier> supplierOrders = getSupplierOrdersByPurchaseOrder(event.getPurchaseOrderId());
            if (supplierOrders.isEmpty()) {
                log.warn("采购订单下没有找到供应商订单: purchaseOrderId={}", event.getPurchaseOrderId());
                return;
            }

            // 调用聚合算法
            TzOrderPurchaseStatusEnum aggregatedStatus = orderStatusAggregator
                .aggregateSupplierStatus(supplierOrders, currentStatus);

            log.debug("供应商订单聚合结果: purchaseOrderId={}, {} -> {}",
                event.getPurchaseOrderId(), currentStatus.getDescription(), aggregatedStatus.getDescription());

            // 如果聚合结果与当前状态不同，发布采购订单状态变更事件
            if (!aggregatedStatus.equals(currentStatus)) {
                publishPurchaseOrderStatusChangeEvent(event, purchaseOrder, currentStatus, aggregatedStatus, supplierOrders);
            } else {
                log.debug("采购订单状态无变化，无需发布事件: purchaseOrderId={}, status={}",
                    event.getPurchaseOrderId(), currentStatus.getDescription());
            }

        } catch (Exception e) {
            log.error("处理采购订单状态聚合失败: supplierOrderId={}, purchaseOrderId={}",
                event.getSupplierOrderId(), event.getPurchaseOrderId(), e);
        }
    }

    /**
     * 发布采购订单状态变更事件
     */
    private void publishPurchaseOrderStatusChangeEvent(SupplierOrderStatusChangeEvent triggerEvent,
        TzOrderPurchase purchaseOrder,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        List<TzOrderSupplier> supplierOrders) {

        try {
            // 创建采购订单状态变更事件
            OrderStatusChangeEvent event = new OrderStatusChangeEvent(
                purchaseOrder.getId(),
                originalStatus,
                newStatus,
                String.format("由供应商订单状态聚合触发: supplierOrderId=%d, %s",
                    triggerEvent.getSupplierOrderId(),
                    triggerEvent.isTriggeredByItemAggregation() ? "订单项聚合" : "直接变更"),
                triggerEvent.getEventChainId(),
                OrderStatusChangeEvent.ChangeSource.SUPPLIER_AGGREGATION,
                triggerEvent.getTenantId(),
                triggerEvent.getUserId()
            );

            // 发布事件
            orderEventPublisher.publishPurchaseOrderStatusChangeEvent(event);

            log.info("已发布采购订单状态变更事件: purchaseOrderId={}, {} -> {}, 触发供应商订单: {}",
                purchaseOrder.getId(), originalStatus.getDescription(), newStatus.getDescription(),
                triggerEvent.getSupplierOrderId());

        } catch (Exception e) {
            log.error("发布采购订单状态变更事件失败: purchaseOrderId={}", purchaseOrder.getId(), e);
        }
    }

    /**
     * 获取采购订单信息
     */
    private TzOrderPurchase getPurchaseOrder(Long purchaseOrderId) {
        return purchaseOrderMapper.selectById(purchaseOrderId);
    }

    /**
     * 获取采购订单下的所有供应商订单
     */
    private List<TzOrderSupplier> getSupplierOrdersByPurchaseOrder(Long purchaseOrderId) {
        return supplierOrderMapper.selectList(
            new LambdaQueryWrapper<TzOrderSupplier>()
                .eq(TzOrderSupplier::getPurchaseOrderId, purchaseOrderId)
                .orderByDesc(TzOrderSupplier::getId) // 使用ID排序，如果没有updateTime方法
        );
    }

    /**
     * 同步外部系统状态
     */
    private void syncExternalSystem(SupplierOrderStatusChangeEvent event) {
        try {
            // TODO: 实现外部系统同步逻辑
            // 1. 同步WMS系统
            // 2. 同步供应商平台
            // 3. 同步ERP系统
            // 4. 发送状态变更通知

            log.info("外部系统同步触发: supplierOrderId={}, 状态={}",
                event.getSupplierOrderId(),
                event.getNewStatus().getDescription());

        } catch (Exception e) {
            log.error("同步外部系统失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }

    /**
     * 更新统计数据
     */
    private void updateStatistics(SupplierOrderStatusChangeEvent event) {
        try {
            // TODO: 实现统计数据更新逻辑
            // 1. 更新供应商订单统计
            // 2. 更新采购订单统计
            // 3. 更新供应商绩效统计
            // 4. 更新状态变更趋势数据

            log.debug("统计数据已更新: supplierOrderId={}", event.getSupplierOrderId());

        } catch (Exception e) {
            log.error("更新统计数据失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }

    /**
     * 检查是否应该跳过处理（防循环）
     */
    private boolean shouldSkipProcessing(SupplierOrderStatusChangeEvent event) {
        String eventChainId = event.getEventChainId();
        return eventChainId != null && processingEventChains.contains(eventChainId);
    }

    /**
     * 标记事件链正在处理
     */
    private void markEventChainProcessing(String eventChainId) {
        if (eventChainId != null) {
            processingEventChains.add(eventChainId);
        }
    }

    /**
     * 取消标记事件链处理
     */
    private void unmarkEventChainProcessing(String eventChainId) {
        if (eventChainId != null) {
            processingEventChains.remove(eventChainId);
        }
    }

    /**
     * 设置处理上下文
     */
    private void setProcessingContext(SupplierOrderStatusChangeEvent event) {
        if (event.getTenantId() != null) {
            // 注意：EnhancedTenantContextHolder需要完整的上下文对象，这里只是简单设置
            // 实际使用中可能需要重新设计租户上下文的设置方式
            // TenantContextHolder.setTenantId(Long.parseLong(event.getTenantId()));
        }
        if (event.getUserId() != null) {
            // 注意：异步事件处理中，用户上下文设置可能不适用
            // UserContextHolder使用不同的架构，暂时跳过设置用户上下文
            // UserContextHolder.setUserId(Long.parseLong(event.getUserId()));
        }
    }

    /**
     * 清理处理上下文
     */
    private void clearProcessingContext() {
        EnhancedTenantContextHolder.clear();
        // UserContextHolder 不需要显式清理，暂时跳过
        // UserContextHolder.clear();
    }

    /**
     * 记录供应商订单状态变更日志
     */
    private void logSupplierOrderStatusChange(SupplierOrderStatusChangeEvent event) {
        try {
            // TODO: 实现详细的状态变更日志记录
            log.info("供应商订单状态变更记录: supplierOrderId={}, supplier={}, {} -> {}, 原因: {}, 来源: {}, 触发: {}",
                event.getSupplierOrderId(),
                event.getSupplierId(),
                event.getOriginalStatus().getDescription(),
                event.getNewStatus().getDescription(),
                event.getChangeReason(),
                event.getChangeSource().getDescription(),
                event.getTriggerType().getDescription());

            // 如果有聚合上下文信息，记录详细信息
            if (event.getAggregationContext() != null) {
                SupplierOrderStatusChangeEvent.AggregationContext context = event.getAggregationContext();
                log.debug("聚合上下文详情: triggerCount={}, totalCount={}, completionRate={:.2f}%, details={}",
                    context.getTriggerItemCount(),
                    context.getTotalItemCount(),
                    context.getCompletionRate(),
                    context.getAggregationDetails());
            }

        } catch (Exception e) {
            log.error("记录供应商订单状态变更日志失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }

    /**
     * 处理供应商订单发货事件（特殊处理）
     */
    @EventListener(condition = "#event.newStatus.name() == 'SHIPPED'")
    public void handleSupplierOrderShippedEvent(SupplierOrderStatusChangeEvent event) {
        log.info("供应商订单已发货事件特殊处理: supplierOrderId={}", event.getSupplierOrderId());

        try {
            // TODO: 发货后的特殊处理
            // 1. 发送发货通知给客户
            // 2. 启动物流追踪
            // 3. 更新预计到货时间
            // 4. 通知仓库准备收货

        } catch (Exception e) {
            log.error("处理供应商订单发货事件失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }

    /**
     * 处理供应商订单完成事件（特殊处理）
     */
    @EventListener(condition = "#event.newStatus.name() == 'COMPLETED'")
    public void handleSupplierOrderCompletedEvent(SupplierOrderStatusChangeEvent event) {
        log.info("供应商订单完成事件特殊处理: supplierOrderId={}", event.getSupplierOrderId());

        try {
            // TODO: 完成后的特殊处理
            // 1. 更新供应商绩效评分
            // 2. 生成供应商订单完成报告
            // 3. 处理质检和入库流程
            // 4. 更新库存数据

        } catch (Exception e) {
            log.error("处理供应商订单完成事件失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }

    /**
     * 处理供应商订单取消事件（特殊处理）
     */
    @EventListener(condition = "#event.newStatus.name() == 'CANCELLED'")
    public void handleSupplierOrderCancelledEvent(SupplierOrderStatusChangeEvent event) {
        log.info("供应商订单取消事件特殊处理: supplierOrderId={}", event.getSupplierOrderId());

        try {
            // TODO: 取消后的特殊处理
            // 1. 通知客户订单取消
            // 2. 处理退款流程
            // 3. 释放预占资源
            // 4. 寻找替代供应商
            // 5. 记录取消原因分析

        } catch (Exception e) {
            log.error("处理供应商订单取消事件失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }
}
