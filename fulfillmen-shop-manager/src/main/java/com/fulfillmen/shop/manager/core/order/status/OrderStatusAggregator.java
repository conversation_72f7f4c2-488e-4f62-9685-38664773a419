/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import static com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.IN_STOCK;
import static com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED;

import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 订单状态聚合器 核心算法：供应商订单状态 → 采购订单状态聚合
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 实现三层状态聚合机制的核心算法
 * @since 1.0.0
 */
@Component
public class OrderStatusAggregator {

    private static final Logger logger = LoggerFactory.getLogger(OrderStatusAggregator.class);

    /**
     * 聚合供应商订单状态到采购订单状态
     *
     * @param supplierOrders        供应商订单列表
     * @param currentPurchaseStatus 当前采购订单状态
     * @return 聚合后的采购订单状态
     */
    public TzOrderPurchaseStatusEnum aggregateSupplierStatus(List<TzOrderSupplier> supplierOrders,
        TzOrderPurchaseStatusEnum currentPurchaseStatus) {

        if (supplierOrders == null || supplierOrders.isEmpty()) {
            logger.warn("供应商订单列表为空，保持当前状态: {}", currentPurchaseStatus);
            return currentPurchaseStatus;
        }

        // 检查是否在可聚合的状态范围内（支付完成后才开始聚合）
        if (!isAggregatable(currentPurchaseStatus)) {
            logger.debug("当前状态 {} 不支持聚合，保持原状态", currentPurchaseStatus);
            return currentPurchaseStatus;
        }

        // 统计各状态供应商订单数量
        StatusStatistics stats = calculateStatusStatistics(supplierOrders);

        logger.debug("状态统计 - 总数: {}, 进行中: {}, 已完成: {}, 已取消: {}, 物流中: {}",
            stats.totalCount, stats.inProgressCount, stats.completedCount,
            stats.cancelledCount, stats.shippingCount);

        // 执行聚合算法
        return executeAggregationAlgorithm(stats, currentPurchaseStatus);
    }

    /**
     * 检查当前状态是否支持聚合
     */
    private boolean isAggregatable(TzOrderPurchaseStatusEnum status) {
        // 只有支付完成后的状态才参与聚合
        return status != null &&
            status.getValue() >= TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED.getValue() &&
            status != TzOrderPurchaseStatusEnum.ORDER_CANCELLED &&
            status != IN_STOCK;
    }

    /**
     * 计算状态统计信息
     */
    private StatusStatistics calculateStatusStatistics(List<TzOrderSupplier> supplierOrders) {
        StatusStatistics stats = new StatusStatistics();
        stats.totalCount = supplierOrders.size();

        for (TzOrderSupplier supplier : supplierOrders) {
            TzOrderSupplierStatusEnum status = supplier.getStatus();

            switch (status) {
                case PENDING_PAYMENT, PENDING_SHIPMENT:
                    stats.inProgressCount++;
                    break;
                case PARTIALLY_SHIPPED, SHIPPED, WAREHOUSE_PENDING_RECEIPT:
                    stats.shippingCount++;
                    break;
                case WAREHOUSE_RECEIVED, COMPLETED:
                    stats.completedCount++;
                    break;
                case CANCELLED:
                    stats.cancelledCount++;
                    break;
                default:
                    stats.inProgressCount++; // 默认归类为进行中
                    break;
            }
        }

        return stats;
    }

    /**
     * 执行聚合算法核心逻辑
     */
    private TzOrderPurchaseStatusEnum executeAggregationAlgorithm(StatusStatistics stats,
        TzOrderPurchaseStatusEnum currentStatus) {

        // 算法 1：全部取消 → 订单取消
        if (stats.cancelledCount == stats.totalCount) {
            logger.info("所有供应商订单已取消，聚合状态: ORDER_CANCELLED");
            return TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
        }

        // 算法 2：全部完成 → 已入库
        if (stats.completedCount == stats.totalCount) {
            logger.info("所有供应商订单已完成，聚合状态: IN_STOCK");
            return IN_STOCK;
        }

        // 算法 3：物流阶段判断（优先级高）
        if (stats.shippingCount > 0) {
            double shippingRate = (double) stats.shippingCount / stats.totalCount;

            if (shippingRate >= 0.5) {
                // 50%以上在物流中
                logger.info("物流率 {:.1%}，聚合状态: SUPPLIER_SHIPPED", shippingRate);
                return SUPPLIER_SHIPPED;
            }
        }

        // 算法 4：部分完成 → 根据完成比例决定
        if (stats.completedCount > 0) {
            double completionRate = (double) stats.completedCount / stats.totalCount;

            if (completionRate >= 0.8) {
                // 80%以上完成，认为基本完成
                logger.info("完成率 {:.1%}，聚合状态: WAREHOUSE_RECEIVED", completionRate);
                return TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
            } else {
                // 部分完成，但要检查是否有物流中的
                if (stats.shippingCount > 0) {
                    // 有物流中的，优先考虑部分履约
                    logger.info("部分完成但有物流中，聚合状态: PARTIALLY_PROCUREMENT");
                    return TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT;
                } else if (stats.inProgressCount > 0) {
                    // 有进行中的订单，应该是部分履约状态
                    logger.info("部分完成但仍有进行中，聚合状态: PARTIALLY_PROCUREMENT");
                    return TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT;
                } else {
                    // 仓库收货阶段：有订单已完成但没有物流中的
                    logger.info("部分供应商订单已到仓库，聚合状态: WAREHOUSE_RECEIVED");
                    return TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
                }
            }
        }

        // 算法 5：全部进行中 → 采购中
        if (stats.inProgressCount == stats.totalCount) {
            logger.info("所有供应商订单进行中，聚合状态: PROCUREMENT_IN_PROGRESS");
            return TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
        }

        // 算法 6：混合状态 → 部分履约
        if (stats.inProgressCount > 0 && (stats.completedCount > 0 || stats.shippingCount > 0)) {
            logger.info("混合状态，聚合状态: PARTIALLY_PROCUREMENT");
            return TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT;
        }

        // 默认保持当前状态
        logger.debug("无法确定聚合状态，保持当前状态: {}", currentStatus);
        return currentStatus;
    }

    /**
     * 检查是否有已到仓库但未入库的订单
     */
    private boolean hasWarehouseReceived(StatusStatistics stats) {
        // 这里需要更详细的状态信息，当前简化处理
        return stats.completedCount > 0 && stats.shippingCount == 0;
    }

    /**
     * 状态统计信息
     */
    private static class StatusStatistics {

        int totalCount = 0;
        int inProgressCount = 0;      // 进行中
        int completedCount = 0;       // 已完成（收货+入库）
        int cancelledCount = 0;       // 已取消
        int shippingCount = 0;        // 物流中（发货+待收货）
    }

    /**
     * 获取聚合状态描述（用于日志和调试）
     */
    public String getAggregationDescription(List<TzOrderSupplier> supplierOrders,
        TzOrderPurchaseStatusEnum aggregatedStatus) {
        if (supplierOrders == null || supplierOrders.isEmpty()) {
            return "无供应商订单";
        }

        StatusStatistics stats = calculateStatusStatistics(supplierOrders);
        return String.format("供应商订单状态分布 - 总数:%d, 进行中:%d, 物流中:%d, 已完成:%d, 已取消:%d → 聚合状态:%s",
            stats.totalCount, stats.inProgressCount, stats.shippingCount,
            stats.completedCount, stats.cancelledCount, aggregatedStatus.getDescription());
    }

    /**
     * 验证聚合结果的合理性
     */
    public boolean validateAggregationResult(List<TzOrderSupplier> supplierOrders,
        TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum aggregatedStatus) {

        // 基本验证：不能逆向聚合（除非是取消状态）
        if (aggregatedStatus != TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
            if (aggregatedStatus.getValue() < originalStatus.getValue()) {
                logger.warn("检测到逆向状态聚合: {} → {}", originalStatus, aggregatedStatus);
                return false;
            }
        }

        // 状态一致性验证
        StatusStatistics stats = calculateStatusStatistics(supplierOrders);

        // 如果所有订单都取消了，聚合状态必须是取消
        if (stats.cancelledCount == stats.totalCount &&
            aggregatedStatus != TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
            logger.error("状态不一致：所有供应商订单已取消，但聚合状态不是ORDER_CANCELLED");
            return false;
        }

        // 如果所有订单都完成了，聚合状态必须是完成
        if (stats.completedCount == stats.totalCount &&
            aggregatedStatus != IN_STOCK) {
            logger.error("状态不一致：所有供应商订单已完成，但聚合状态不是IN_STOCK");
            return false;
        }

        logger.debug("聚合结果验证通过: {} → {}", originalStatus, aggregatedStatus);
        return true;
    }
}
