/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.order.OrderEventPublisher;
import com.fulfillmen.shop.manager.core.order.status.SupplierStatusCalculator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 订单项状态变更事件监听器
 *
 * <pre>
 * 处理订单项状态变更事件，执行以下业务操作：
 * 1. 记录订单项状态变更日志
 * 2. 调用供应商订单状态聚合算法
 * 3. 发布供应商订单状态变更事件（如果聚合结果有变化）
 * 4. 防循环触发机制
 * 5. 更新相关统计数据
 * 6. 异步处理以提高系统响应性能
 *
 * 架构定位：
 * - 三层状态聚合体系的第一环节
 * - 连接订单项状态变更和供应商订单状态聚合的桥梁
 * - 处理单个和批量订单项状态变更
 * - 支持事件链追踪和防循环机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/31
 * @description 订单项状态变更事件监听器，三层状态聚合的第一层处理器
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderItemStatusChangeEventListener {

    private final TzOrderItemMapper orderItemMapper;
    private final TzOrderSupplierMapper supplierOrderMapper;
    private final SupplierStatusCalculator supplierStatusCalculator;
    private final OrderEventPublisher orderEventPublisher;

    /**
     * 防循环触发的事件链集合
     */
    private final Set<String> processingEventChains = ConcurrentHashMap.newKeySet();

    /**
     * 处理订单项状态变更事件（异步）
     *
     * @param event 订单项状态变更事件
     */
    @EventListener
    @Async
    public void handleOrderItemStatusChangeEvent(OrderItemStatusChangeEvent event) {
        log.info("收到订单项状态变更事件: {}", event);

        try {
            // 防循环检查
            if (shouldSkipProcessing(event)) {
                log.debug("跳过事件处理以防止循环: eventChainId={}", event.getEventChainId());
                return;
            }

            // 设置上下文
            setProcessingContext(event);

            // 记录事件链处理
            markEventChainProcessing(event.getEventChainId());

            // 1. 记录订单项状态变更日志
            logOrderItemStatusChange(event);

            // 2. 检查是否需要触发供应商订单聚合
            if (!event.shouldTriggerSupplierAggregation()) {
                log.debug("订单项状态变更不需要触发供应商聚合: {}", event.getOrderItemId());
                return;
            }

            // 3. 执行供应商订单状态聚合
            processSupplierOrderAggregation(event);

            log.debug("订单项状态变更事件处理完成: {}", event.getOrderItemId());

        } catch (Exception e) {
            log.error("处理订单项状态变更事件失败: {}", event, e);
        } finally {
            // 清理事件链标记
            unmarkEventChainProcessing(event.getEventChainId());
            clearProcessingContext();
        }
    }

    /**
     * 处理批量订单项状态变更事件（异步）
     *
     * @param event 批量订单项状态变更事件
     */
    @EventListener
    @Async
    public void handleBatchOrderItemStatusChangeEvent(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent event) {
        log.info("收到批量订单项状态变更事件: batchId={}, 变更数量={}", event.getBatchId(), event.getChangeCount());

        try {
            // 防循环检查
            if (shouldSkipBatchProcessing(event)) {
                log.debug("跳过批量事件处理以防止循环: eventChainId={}", event.getEventChainId());
                return;
            }

            // 设置上下文
            setBatchProcessingContext(event);

            // 记录事件链处理
            markEventChainProcessing(event.getEventChainId());

            // 1. 记录批量状态变更日志
            logBatchOrderItemStatusChange(event);

            // 2. 执行供应商订单状态聚合（批量处理只针对单一供应商订单）
            processBatchSupplierOrderAggregation(event);

            log.info("批量订单项状态变更事件处理完成: batchId={}, 处理数量={}",
                event.getBatchId(), event.getChangeCount());

        } catch (Exception e) {
            log.error("处理批量订单项状态变更事件失败: {}", event, e);
        } finally {
            // 清理事件链标记
            unmarkEventChainProcessing(event.getEventChainId());
            clearProcessingContext();
        }
    }

    /**
     * 处理供应商订单状态聚合
     */
    private void processSupplierOrderAggregation(OrderItemStatusChangeEvent event) {
        try {
            // 获取当前供应商订单信息
            TzOrderSupplier supplierOrder = getSupplierOrder(event.getSupplierOrderId());
            if (supplierOrder == null) {
                log.warn("未找到供应商订单: supplierOrderId={}", event.getSupplierOrderId());
                return;
            }

            TzOrderSupplierStatusEnum currentStatus = supplierOrder.getStatus();

            // 获取同一供应商订单下的所有订单项
            List<TzOrderItem> orderItems = getOrderItemsBySupplierOrder(event.getSupplierOrderId());
            if (orderItems.isEmpty()) {
                log.warn("供应商订单下没有找到订单项: supplierOrderId={}", event.getSupplierOrderId());
                return;
            }

            // 调用聚合算法
            TzOrderSupplierStatusEnum aggregatedStatus = supplierStatusCalculator
                .aggregateItemStatus(orderItems, currentStatus);

            log.debug("订单项聚合结果: supplierOrderId={}, {} -> {}",
                event.getSupplierOrderId(), currentStatus.getDescription(), aggregatedStatus.getDescription());

            // 如果聚合结果与当前状态不同，发布供应商订单状态变更事件
            if (!aggregatedStatus.equals(currentStatus)) {
                publishSupplierOrderStatusChangeEvent(event, supplierOrder, currentStatus, aggregatedStatus, orderItems);
            } else {
                log.debug("供应商订单状态无变化，无需发布事件: supplierOrderId={}, status={}",
                    event.getSupplierOrderId(), currentStatus.getDescription());
            }

        } catch (Exception e) {
            log.error("处理供应商订单状态聚合失败: orderItemId={}, supplierOrderId={}",
                event.getOrderItemId(), event.getSupplierOrderId(), e);
        }
    }

    /**
     * 处理批量供应商订单状态聚合
     */
    private void processBatchSupplierOrderAggregation(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent event) {
        try {
            // 获取当前供应商订单信息
            TzOrderSupplier supplierOrder = getSupplierOrder(event.getSupplierOrderId());
            if (supplierOrder == null) {
                log.warn("未找到供应商订单: supplierOrderId={}", event.getSupplierOrderId());
                return;
            }

            TzOrderSupplierStatusEnum currentStatus = supplierOrder.getStatus();

            // 获取同一供应商订单下的所有订单项
            List<TzOrderItem> orderItems = getOrderItemsBySupplierOrder(event.getSupplierOrderId());
            if (orderItems.isEmpty()) {
                log.warn("供应商订单下没有找到订单项: supplierOrderId={}", event.getSupplierOrderId());
                return;
            }

            // 调用聚合算法
            TzOrderSupplierStatusEnum aggregatedStatus = supplierStatusCalculator
                .aggregateItemStatus(orderItems, currentStatus);

            log.debug("批量订单项聚合结果: batchId={}, supplierOrderId={}, {} -> {}",
                event.getBatchId(), event.getSupplierOrderId(),
                currentStatus.getDescription(), aggregatedStatus.getDescription());

            // 如果聚合结果与当前状态不同，发布供应商订单状态变更事件
            if (!aggregatedStatus.equals(currentStatus)) {
                publishBatchSupplierOrderStatusChangeEvent(event, supplierOrder, currentStatus, aggregatedStatus, orderItems);
            }

        } catch (Exception e) {
            log.error("处理批量供应商订单状态聚合失败: batchId={}, supplierOrderId={}",
                event.getBatchId(), event.getSupplierOrderId(), e);
        }
    }

    /**
     * 发布供应商订单状态变更事件
     */
    private void publishSupplierOrderStatusChangeEvent(OrderItemStatusChangeEvent triggerEvent,
        TzOrderSupplier supplierOrder,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        List<TzOrderItem> orderItems) {

        try {
            // 创建聚合上下文
            SupplierOrderStatusChangeEvent.AggregationContext aggregationContext = new SupplierOrderStatusChangeEvent.AggregationContext(
                1, // 单个订单项触发
                orderItems.size(),
                "v1.0",
                supplierStatusCalculator.calculateCompletionRate(orderItems),
                supplierStatusCalculator.getAggregationDescription(orderItems, newStatus)
            );

            // 创建供应商订单状态变更事件
            SupplierOrderStatusChangeEvent event = new SupplierOrderStatusChangeEvent(
                supplierOrder.getId(),
                supplierOrder.getPurchaseOrderId(),
                supplierOrder.getSupplierId(),
                originalStatus,
                newStatus,
                String.format("由订单项状态聚合触发: orderItemId=%d", triggerEvent.getOrderItemId()),
                triggerEvent.getEventChainId(),
                SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION,
                triggerEvent.getTenantId(),
                triggerEvent.getUserId(),
                aggregationContext
            );

            // 发布事件
            orderEventPublisher.publishSupplierOrderStatusChangeEvent(event);

            log.info("已发布供应商订单状态变更事件: supplierOrderId={}, {} -> {}, 触发订单项: {}",
                supplierOrder.getId(), originalStatus.getDescription(), newStatus.getDescription(),
                triggerEvent.getOrderItemId());

        } catch (Exception e) {
            log.error("发布供应商订单状态变更事件失败: supplierOrderId={}", supplierOrder.getId(), e);
        }
    }

    /**
     * 发布批量供应商订单状态变更事件
     */
    private void publishBatchSupplierOrderStatusChangeEvent(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent triggerEvent,
        TzOrderSupplier supplierOrder,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum newStatus,
        List<TzOrderItem> orderItems) {

        try {
            // 创建聚合上下文
            SupplierOrderStatusChangeEvent.AggregationContext aggregationContext = new SupplierOrderStatusChangeEvent.AggregationContext(
                triggerEvent.getChangeCount(),
                orderItems.size(),
                "v1.0",
                supplierStatusCalculator.calculateCompletionRate(orderItems),
                supplierStatusCalculator.getAggregationDescription(orderItems, newStatus)
            );

            // 创建供应商订单状态变更事件
            SupplierOrderStatusChangeEvent event = new SupplierOrderStatusChangeEvent(
                supplierOrder.getId(),
                supplierOrder.getPurchaseOrderId(),
                supplierOrder.getSupplierId(),
                originalStatus,
                newStatus,
                String.format("由批量订单项状态聚合触发: batchId=%s, 变更数量=%d",
                    triggerEvent.getBatchId(), triggerEvent.getChangeCount()),
                triggerEvent.getEventChainId(),
                SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION,
                triggerEvent.getTenantId(),
                triggerEvent.getUserId(),
                aggregationContext
            );

            // 发布事件
            orderEventPublisher.publishSupplierOrderStatusChangeEvent(event);

            log.info("已发布批量供应商订单状态变更事件: supplierOrderId={}, {} -> {}, 批次: {}",
                supplierOrder.getId(), originalStatus.getDescription(), newStatus.getDescription(),
                triggerEvent.getBatchId());

        } catch (Exception e) {
            log.error("发布批量供应商订单状态变更事件失败: supplierOrderId={}", supplierOrder.getId(), e);
        }
    }

    /**
     * 获取供应商订单信息
     */
    private TzOrderSupplier getSupplierOrder(Long supplierOrderId) {
        return supplierOrderMapper.selectById(supplierOrderId);
    }

    /**
     * 获取供应商订单下的所有订单项
     */
    private List<TzOrderItem> getOrderItemsBySupplierOrder(Long supplierOrderId) {
        return orderItemMapper.selectList(
            new LambdaQueryWrapper<TzOrderItem>()
                .eq(TzOrderItem::getSupplierOrderId, supplierOrderId)
                .orderByDesc(TzOrderItem::getId) // 使用ID排序，如果没有updateTime方法
        );
    }

    /**
     * 检查是否应该跳过处理（防循环）
     */
    private boolean shouldSkipProcessing(OrderItemStatusChangeEvent event) {
        String eventChainId = event.getEventChainId();
        return eventChainId != null && processingEventChains.contains(eventChainId);
    }

    /**
     * 检查是否应该跳过批量处理（防循环）
     */
    private boolean shouldSkipBatchProcessing(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent event) {
        String eventChainId = event.getEventChainId();
        return eventChainId != null && processingEventChains.contains(eventChainId);
    }

    /**
     * 标记事件链正在处理
     */
    private void markEventChainProcessing(String eventChainId) {
        if (eventChainId != null) {
            processingEventChains.add(eventChainId);
        }
    }

    /**
     * 取消标记事件链处理
     */
    private void unmarkEventChainProcessing(String eventChainId) {
        if (eventChainId != null) {
            processingEventChains.remove(eventChainId);
        }
    }

    /**
     * 设置处理上下文
     */
    private void setProcessingContext(OrderItemStatusChangeEvent event) {
        if (event.getTenantId() != null) {
            // 注意：EnhancedTenantContextHolder需要完整的上下文对象，这里只是简单设置
            // 实际使用中可能需要重新设计租户上下文的设置方式
            // TenantContextHolder.setTenantId(Long.parseLong(event.getTenantId()));
        }
        if (event.getUserId() != null) {
            // 注意：异步事件处理中，用户上下文设置可能不适用
            // UserContextHolder使用不同的架构，暂时跳过设置用户上下文
            // UserContextHolder.setUserId(Long.parseLong(event.getUserId()));
        }
    }

    /**
     * 设置批量处理上下文
     */
    private void setBatchProcessingContext(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent event) {
        if (event.getTenantId() != null) {
            // 注意：EnhancedTenantContextHolder需要完整的上下文对象，这里只是简单设置
            // 实际使用中可能需要重新设计租户上下文的设置方式
            // TenantContextHolder.setTenantId(Long.parseLong(event.getTenantId()));
        }
        if (event.getUserId() != null) {
            // 注意：异步事件处理中，用户上下文设置可能不适用
            // UserContextHolder使用不同的架构，暂时跳过设置用户上下文
            // UserContextHolder.setUserId(Long.parseLong(event.getUserId()));
        }
    }

    /**
     * 清理处理上下文
     */
    private void clearProcessingContext() {
        EnhancedTenantContextHolder.clear();
        // UserContextHolder 不需要显式清理，暂时跳过
        // UserContextHolder.clear();
    }

    /**
     * 记录订单项状态变更日志
     */
    private void logOrderItemStatusChange(OrderItemStatusChangeEvent event) {
        try {
            // TODO: 实现详细的状态变更日志记录
            log.info("订单项状态变更记录: orderItemId={}, {} -> {}, 原因: {}, 来源: {}",
                event.getOrderItemId(),
                event.getOriginalStatus().getDesc(),
                event.getNewStatus().getDesc(),
                event.getChangeReason(),
                event.getChangeSource().getDescription());

        } catch (Exception e) {
            log.error("记录订单项状态变更日志失败: orderItemId={}", event.getOrderItemId(), e);
        }
    }

    /**
     * 记录批量订单项状态变更日志
     */
    private void logBatchOrderItemStatusChange(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent event) {
        try {
            // TODO: 实现详细的批量状态变更日志记录
            log.info("批量订单项状态变更记录: batchId={}, 变更数量={}, 供应商订单: {}, 来源: {}",
                event.getBatchId(),
                event.getChangeCount(),
                event.getSupplierOrderId(),
                event.getChangeSource().getDescription());

            // 详细记录每个变更
            event.getStatusChanges().forEach(change -> log.debug("订单项变更详情: orderItemId={}, {} -> {}, 原因: {}",
                change.getOrderItemId(),
                change.getOriginalStatus().getDesc(),
                change.getNewStatus().getDesc(),
                change.getChangeReason())
            );

        } catch (Exception e) {
            log.error("记录批量订单项状态变更日志失败: batchId={}", event.getBatchId(), e);
        }
    }
}
