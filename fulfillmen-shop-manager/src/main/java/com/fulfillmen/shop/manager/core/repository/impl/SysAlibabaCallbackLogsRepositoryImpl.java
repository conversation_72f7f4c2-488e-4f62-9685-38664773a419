/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.domain.entity.enums.AlibabaCallbackLogsProcessStatusEnum;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/8/8 09:36
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Repository
public class SysAlibabaCallbackLogsRepositoryImpl extends CrudRepository<SysAlibabaCallbackLogsMapper, SysAlibabaCallbackLogs> implements SysAlibabaCallbackLogsRepository {

    @Override
    public Long createProcessingLog(String message, String signature, String eventType, String bizId,
        LocalDateTime receivedAt) {
        try {
            IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
            Long id = idGenerator.generate();

            LocalDateTime now = LocalDateTime.now();
            SysAlibabaCallbackLogs entity = SysAlibabaCallbackLogs.builder()
                .id(id)
                .metadata(message)
                .eventType(eventType)
                .orderId(bizId)
                .receivedTimestamp(receivedAt != null ? receivedAt : now)
                .processStatus(AlibabaCallbackLogsProcessStatusEnum.PROCESSING)
                .processFailedMsg(null)
                .sign(signature)
                .build();

            if (this.save(entity)) {
                log.debug("创建Webhook处理日志成功(含事件与订单): id={}, eventType={}, bizId={} ", id, eventType, bizId);
                return id;
            }
            log.warn("创建Webhook处理日志失败(含事件与订单)，返回0行");
        } catch (Exception e) {
            log.error("创建Webhook处理日志异常(含事件与订单)", e);
        }
        return null;
    }

    @Override
    public void markSuccess(Long logId) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
                .id(logId)
                .processStatus(AlibabaCallbackLogsProcessStatusEnum.SUCCESS)
                .build();
            this.updateById(update);
        } catch (Exception e) {
            log.error("标记Webhook日志成功状态失败: id={}", logId, e);
        }
    }

    @Override
    public void markFailed(Long logId, String reason) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
                .id(logId)
                .processStatus(AlibabaCallbackLogsProcessStatusEnum.FAILED)
                .processFailedMsg(reason)
                .build();
            this.updateById(update);
        } catch (Exception e) {
            log.error("标记Webhook日志失败状态失败: id={}", logId, e);
        }
    }

    @Override
    public List<SysAlibabaCallbackLogs> findFailedLogsForRetry(int maxRetryCount, int timeHours, int limit) {
        LocalDateTime timeThreshold = LocalDateTime.now().minusHours(timeHours);
        // 查询失败记录，重试次数小于最大重试次数，创建时间大于时间阈值，订单ID不为空，按创建时间降序，限制数量
        LambdaQueryWrapper<SysAlibabaCallbackLogs> wrapper = Wrappers.<SysAlibabaCallbackLogs>lambdaQuery()
            .eq(SysAlibabaCallbackLogs::getProcessStatus, AlibabaCallbackLogsProcessStatusEnum.FAILED)
            .lt(SysAlibabaCallbackLogs::getRetryCount, maxRetryCount)
            .ge(SysAlibabaCallbackLogs::getGmtCreated, timeThreshold)
            .isNotNull(SysAlibabaCallbackLogs::getOrderId)
            .orderByDesc(SysAlibabaCallbackLogs::getGmtCreated)
            .last("LIMIT " + limit);

        try {
            return this.list(wrapper);
        } catch (Exception e) {
            log.error("查询需要重试的失败记录异常: maxRetryCount={}, timeHours={}, limit={}",
                maxRetryCount, timeHours, limit, e);
            return List.of();
        }
    }

    @Override
    public void updateRetryCount(Long logId, int retryCount) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
                .id(logId)
                .retryCount(retryCount)
                .processStatus(AlibabaCallbackLogsProcessStatusEnum.PROCESSING)
                // 清除之前的失败信息
                .processFailedMsg(null)
                .build();
            this.updateById(update);
            log.debug("更新重试次数成功: logId={}, retryCount={}", logId, retryCount);
        } catch (Exception e) {
            log.error("更新重试次数失败: logId={}, retryCount={}", logId, retryCount, e);
        }
    }

    @Override
    public SysAlibabaCallbackLogs findLatestByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }

        LambdaQueryWrapper<SysAlibabaCallbackLogs> wrapper = Wrappers.<SysAlibabaCallbackLogs>lambdaQuery()
            .eq(SysAlibabaCallbackLogs::getOrderId, orderId)
            .orderByDesc(SysAlibabaCallbackLogs::getGmtBornVirtual)
            .last("LIMIT 1");

        try {
            return this.getOne(wrapper);
        } catch (Exception e) {
            log.error("根据订单ID查找最新回调记录异常: orderId={}", orderId, e);
            return null;
        }
    }

    @Override
    public long countFailedLogs(int timeHours) {
        LocalDateTime timeThreshold = LocalDateTime.now().minusHours(timeHours);

        LambdaQueryWrapper<SysAlibabaCallbackLogs> wrapper = Wrappers.<SysAlibabaCallbackLogs>lambdaQuery()
            .eq(SysAlibabaCallbackLogs::getProcessStatus, AlibabaCallbackLogsProcessStatusEnum.FAILED)
            .ge(SysAlibabaCallbackLogs::getGmtCreated, timeThreshold);

        try {
            return this.count(wrapper);
        } catch (Exception e) {
            log.error("统计失败记录数量异常: timeHours={}", timeHours, e);
            return 0L;
        }
    }

    @Override
    public List<SysAlibabaCallbackLogs> findLatestUnprocessedLogsByOrderId(int limit, int processHours) {
        try {
            List<SysAlibabaCallbackLogs> result = this.baseMapper.findLatestUnprocessedLogs(limit, processHours);
            log.debug("查询 {} 小时内未处理记录完成: limit={}, found={}", processHours, limit, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询 {} 小时内未处理记录异常: limit={}", processHours, limit, e);
            return List.of();
        }
    }

    @Override
    public List<SysAlibabaCallbackLogs> findAllUnprocessedLogsByOrderId(int limit, int processHours) {
        try {
            List<SysAlibabaCallbackLogs> result = this.baseMapper.findAllUnprocessedLogs(limit, processHours);
            log.debug("查询 {} 小时内所有未处理记录完成: limit={}, found={}", processHours, limit, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询 {} 小时内所有未处理记录异常: limit={}", processHours, limit, e);
            return List.of();
        }
    }

    @Override
    public void markProcessed(Long logId) {
        if (logId == null) {
            return;
        }
        try {
            SysAlibabaCallbackLogs update = SysAlibabaCallbackLogs.builder()
                .id(logId)
                .processStatus(AlibabaCallbackLogsProcessStatusEnum.SUCCESS)
                .build();
            this.updateById(update);
            log.debug("标记记录为已处理成功: logId={}", logId);
        } catch (Exception e) {
            log.error("标记记录为已处理失败: logId={}", logId, e);
        }
    }

    @Override
    public int markOrderProcessedByLatestTimestamp(Long orderId, Long latestGmtBornVirtual) {
        if (orderId == null || latestGmtBornVirtual == null) {
            return 0;
        }
        try {
            int updatedCount = this.baseMapper.markOrderProcessedByLatestTimestamp(orderId, latestGmtBornVirtual);
            log.debug("批量标记订单记录为已处理: orderId={}, latestGmtBornVirtual={}, updatedCount={}",
                orderId, latestGmtBornVirtual, updatedCount);
            return updatedCount;
        } catch (Exception e) {
            log.error("批量标记订单记录为已处理失败: orderId={}, latestGmtBornVirtual={}",
                orderId, latestGmtBornVirtual, e);
            return 0;
        }
    }
}
