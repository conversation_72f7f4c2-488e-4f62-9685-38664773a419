/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.consistency;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单数据一致性管理器
 *
 * <p>实现订单状态的原子性更新和乐观锁机制，确保并发环境下的数据一致性</p>
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 订单数据一致性管理，防止并发更新冲突
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderDataConsistencyManager {

    private final TzOrderSupplierMapper supplierOrderMapper;
    private final TzOrderPurchaseMapper purchaseOrderMapper;

    /**
     * 原子性更新供应商订单状态和同步状态
     *
     * @param supplierOrderId 供应商订单ID
     * @param newStatus       新的订单状态
     * @param newSyncStatus   新的同步状态
     * @param metadataJson    元数据JSON
     * @throws OptimisticLockingFailureException 当乐观锁冲突时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public void atomicUpdateSupplierOrderStatus(Long supplierOrderId,
        TzOrderSupplierStatusEnum newStatus,
        OrderSupplierSyncStatusEnums newSyncStatus,
        String metadataJson) {

        if (supplierOrderId == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "供应商订单ID不能为空");
        }

        // 查询最新版本的订单数据
        TzOrderSupplier currentOrder = supplierOrderMapper.selectById(supplierOrderId);
        if (currentOrder == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "供应商订单不存在");
        }

        String orderNo = currentOrder.getSupplierOrderNo();
        log.info("原子性更新供应商订单 {} 状态：{} -> {}，同步状态：{} -> {}",
            orderNo, currentOrder.getStatus(), newStatus,
            currentOrder.getExternalSyncStatus(), newSyncStatus);

        // 构建乐观锁更新条件
        LambdaUpdateWrapper<TzOrderSupplier> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TzOrderSupplier::getId, supplierOrderId)
            .eq(TzOrderSupplier::getRevision, currentOrder.getRevision()); // 乐观锁条件

        // 设置更新字段
        updateWrapper.set(TzOrderSupplier::getStatus, newStatus)
            .set(TzOrderSupplier::getExternalSyncStatus, newSyncStatus)
            .set(TzOrderSupplier::getGmtModified, LocalDateTime.now());

        if (metadataJson != null) {
            updateWrapper.set(TzOrderSupplier::getMetadataJson, metadataJson);
        }

        // 执行乐观锁更新
        int affectedRows = supplierOrderMapper.update(null, updateWrapper);

        if (affectedRows == 0) {
            log.warn("供应商订单 {} 乐观锁更新失败，当前版本：{}，可能存在并发修改",
                orderNo, currentOrder.getRevision());
            throw new OptimisticLockingFailureException(
                String.format("供应商订单 %s 更新冲突，请重试", orderNo));
        }

        log.debug("供应商订单 {} 状态更新成功，影响行数：{}", orderNo, affectedRows);
    }

    /**
     * 原子性批量更新供应商订单状态
     *
     * @param supplierOrderIds 供应商订单ID列表
     * @param newSyncStatus    新的同步状态
     * @param reason           更新原因
     * @return 成功更新的订单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchAtomicUpdateSyncStatus(List<Long> supplierOrderIds,
        OrderSupplierSyncStatusEnums newSyncStatus,
        String reason) {

        if (supplierOrderIds == null || supplierOrderIds.isEmpty()) {
            log.warn("批量原子更新：订单ID列表为空");
            return 0;
        }

        log.info("批量原子更新 {} 个供应商订单同步状态为：{}，原因：{}",
            supplierOrderIds.size(), newSyncStatus, reason);

        int successCount = 0;
        int failureCount = 0;

        for (Long orderId : supplierOrderIds) {
            try {
                // 查询当前订单
                TzOrderSupplier currentOrder = supplierOrderMapper.selectById(orderId);
                if (currentOrder == null) {
                    log.warn("订单ID {} 不存在，跳过更新", orderId);
                    failureCount++;
                    continue;
                }

                // 原子性更新
                atomicUpdateSupplierOrderStatus(
                    orderId,
                    currentOrder.getStatus(), // 保持原状态
                    newSyncStatus,
                    buildBatchUpdateMetadata(reason)
                );

                successCount++;

            } catch (OptimisticLockingFailureException e) {
                log.warn("订单ID {} 乐观锁冲突，更新失败", orderId, e);
                failureCount++;
            } catch (Exception e) {
                log.error("订单ID {} 更新异常", orderId, e);
                failureCount++;
            }
        }

        log.info("批量原子更新完成：成功 {} 个，失败 {} 个", successCount, failureCount);
        return successCount;
    }

    /**
     * 检查并修复数据一致性问题
     *
     * @param purchaseOrderId 采购订单ID
     * @return 一致性检查报告
     */
    @Transactional(rollbackFor = Exception.class)
    public ConsistencyCheckReport checkAndRepairConsistency(Long purchaseOrderId) {

        if (purchaseOrderId == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "采购订单ID不能为空");
        }

        // 查询采购订单
        TzOrderPurchase purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "采购订单不存在");
        }

        String purchaseOrderNo = purchaseOrder.getPurchaseOrderNo();
        log.info("开始检查采购订单 {} 的数据一致性", purchaseOrderNo);

        // 查询关联的供应商订单
        LambdaQueryWrapper<TzOrderSupplier> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzOrderSupplier::getPurchaseOrderId, purchaseOrderId);
        // 不需要显式声明 isDeleted，MP逻辑删除插件自动处理

        List<TzOrderSupplier> supplierOrders = supplierOrderMapper.selectList(queryWrapper);

        // 执行一致性检查
        ConsistencyIssue issue = detectConsistencyIssues(purchaseOrder, supplierOrders);

        boolean repairNeeded = issue != ConsistencyIssue.NONE;
        boolean repairSuccess = false;

        // 如果发现问题，尝试修复
        if (repairNeeded) {
            log.warn("采购订单 {} 发现一致性问题：{}，开始修复", purchaseOrderNo, issue);
            repairSuccess = repairConsistencyIssue(purchaseOrder, supplierOrders, issue);
        }

        return ConsistencyCheckReport.builder()
            .purchaseOrderNo(purchaseOrderNo)
            .issueType(issue)
            .supplierOrderCount(supplierOrders.size())
            .repairNeeded(repairNeeded)
            .repairSuccess(repairSuccess)
            .checkTime(LocalDateTime.now())
            .message(buildConsistencyMessage(issue, repairNeeded, repairSuccess))
            .build();
    }

    /**
     * 安全的状态回滚操作（带手动重试机制）
     *
     * @param supplierOrderId 供应商订单ID
     * @param targetStatus    目标状态
     * @param rollbackReason  回滚原因
     */
    public void safeRollbackOrderStatus(Long supplierOrderId,
        TzOrderSupplierStatusEnum targetStatus,
        String rollbackReason) {

        int maxAttempts = 5;
        int attempt = 0;

        while (attempt < maxAttempts) {
            try {
                doRollbackOrderStatus(supplierOrderId, targetStatus, rollbackReason);
                return; // 成功则退出
            } catch (OptimisticLockingFailureException e) {
                attempt++;
                log.warn("供应商订单 {} 回滚第 {} 次尝试失败，乐观锁冲突", supplierOrderId, attempt);

                if (attempt >= maxAttempts) {
                    log.error("供应商订单 {} 回滚达到最大重试次数 {}，放弃操作", supplierOrderId, maxAttempts);
                    throw e;
                }

                // 指数退避延迟
                randomDelayWithBackoff(attempt);
            }
        }
    }

    /**
     * 实际执行回滚操作
     */
    @Transactional(rollbackFor = Exception.class)
    private void doRollbackOrderStatus(Long supplierOrderId,
        TzOrderSupplierStatusEnum targetStatus,
        String rollbackReason) {

        log.info("开始安全回滚供应商订单 {} 到状态：{}，原因：{}",
            supplierOrderId, targetStatus, rollbackReason);

        TzOrderSupplier currentOrder = supplierOrderMapper.selectById(supplierOrderId);
        if (currentOrder == null) {
            log.warn("回滚失败：供应商订单 {} 不存在", supplierOrderId);
            return;
        }

        // 构建回滚元数据
        String rollbackMetadata = String.format(
            "{\"rollback\": true, \"fromStatus\": \"%s\", \"toStatus\": \"%s\", \"reason\": \"%s\", \"rollbackTime\": \"%s\"}",
            currentOrder.getStatus(), targetStatus, rollbackReason, LocalDateTime.now());

        // 原子性回滚
        atomicUpdateSupplierOrderStatus(
            supplierOrderId,
            targetStatus,
            OrderSupplierSyncStatusEnums.NOT_SYNCED, // 回滚时重置同步状态
            rollbackMetadata
        );

        log.info("供应商订单 {} 状态回滚成功：{} -> {}",
            currentOrder.getSupplierOrderNo(), currentOrder.getStatus(), targetStatus);
    }

    /**
     * 检测一致性问题
     */
    private ConsistencyIssue detectConsistencyIssues(TzOrderPurchase purchaseOrder,
        List<TzOrderSupplier> supplierOrders) {

        if (supplierOrders.isEmpty()) {
            return ConsistencyIssue.MISSING_SUPPLIER_ORDERS;
        }

        // 检查同步状态不一致
        boolean hasSyncInconsistency = supplierOrders.stream()
            .anyMatch(order -> order.getExternalSyncStatus() == null);

        if (hasSyncInconsistency) {
            return ConsistencyIssue.SYNC_STATUS_INCONSISTENT;
        }

        // 检查状态流转逻辑错误
        boolean hasStatusError = supplierOrders.stream()
            .anyMatch(order -> isStatusTransitionInvalid(order.getStatus(), purchaseOrder.getOrderStatus()));

        if (hasStatusError) {
            return ConsistencyIssue.STATUS_TRANSITION_INVALID;
        }

        return ConsistencyIssue.NONE;
    }

    /**
     * 修复一致性问题
     */
    private boolean repairConsistencyIssue(TzOrderPurchase purchaseOrder,
        List<TzOrderSupplier> supplierOrders,
        ConsistencyIssue issue) {
        try {
            switch (issue) {
                case SYNC_STATUS_INCONSISTENT -> {
                    // 修复同步状态不一致
                    for (TzOrderSupplier supplierOrder : supplierOrders) {
                        if (supplierOrder.getExternalSyncStatus() == null) {
                            atomicUpdateSupplierOrderStatus(
                                supplierOrder.getId(),
                                supplierOrder.getStatus(),
                                OrderSupplierSyncStatusEnums.NOT_SYNCED,
                                "{\"repaired\": true, \"issue\": \"sync_status_null\"}"
                            );
                        }
                    }
                }
                case STATUS_TRANSITION_INVALID -> {
                    // 修复状态流转错误（这里可以根据业务逻辑实现具体修复策略）
                    log.warn("发现状态流转问题，需要人工介入处理");
                    return false;
                }
                case MISSING_SUPPLIER_ORDERS -> {
                    log.error("缺失供应商订单，需要业务层面处理");
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("修复一致性问题失败", e);
            return false;
        }
    }

    /**
     * 检查状态流转是否有效
     */
    private boolean isStatusTransitionInvalid(TzOrderSupplierStatusEnum supplierStatus,
        TzOrderPurchaseStatusEnum purchaseStatus) {
        // 简单的业务规则检查：如果采购订单已取消，供应商订单不应该是进行中状态
        if (purchaseStatus == TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
            return supplierStatus == TzOrderSupplierStatusEnum.PENDING_SHIPMENT ||
                supplierStatus == TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        }
        return false;
    }

    /**
     * 构建批量更新元数据
     */
    private String buildBatchUpdateMetadata(String reason) {
        return String.format("{\"batchUpdate\": true, \"reason\": \"%s\", \"updateTime\": \"%s\"}",
            reason, LocalDateTime.now());
    }

    /**
     * 构建一致性检查消息
     */
    private String buildConsistencyMessage(ConsistencyIssue issue, boolean repairNeeded, boolean repairSuccess) {
        if (!repairNeeded) {
            return "数据一致性检查通过";
        }

        String baseMessage = String.format("发现一致性问题：%s", issue.getDescription());
        if (repairSuccess) {
            return baseMessage + "，已自动修复";
        } else {
            return baseMessage + "，修复失败，需要人工处理";
        }
    }

    /**
     * 随机延迟，避免并发冲突
     */
    private void randomDelay() {
        try {
            Thread.sleep(ThreadLocalRandom.current().nextInt(50, 150));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 指数退避延迟
     */
    private void randomDelayWithBackoff(int attempt) {
        try {
            int baseDelay = 200;
            int maxDelay = baseDelay * (int) Math.pow(1.5, attempt);
            int randomDelay = ThreadLocalRandom.current().nextInt(baseDelay, maxDelay + 1);
            Thread.sleep(randomDelay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 一致性问题类型枚举
     */
    public enum ConsistencyIssue {

        NONE("无问题"),
        SYNC_STATUS_INCONSISTENT("同步状态不一致"),
        STATUS_TRANSITION_INVALID("状态流转无效"),
        MISSING_SUPPLIER_ORDERS("缺失供应商订单"),
        DATA_CORRUPTION("数据损坏");

        private final String description;

        ConsistencyIssue(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 一致性检查报告
     */
    public static class ConsistencyCheckReport {

        private String purchaseOrderNo;
        private ConsistencyIssue issueType;
        private int supplierOrderCount;
        private boolean repairNeeded;
        private boolean repairSuccess;
        private LocalDateTime checkTime;
        private String message;

        public static ConsistencyCheckReportBuilder builder() {
            return new ConsistencyCheckReportBuilder();
        }

        public static class ConsistencyCheckReportBuilder {

            private String purchaseOrderNo;
            private ConsistencyIssue issueType;
            private int supplierOrderCount;
            private boolean repairNeeded;
            private boolean repairSuccess;
            private LocalDateTime checkTime;
            private String message;

            public ConsistencyCheckReportBuilder purchaseOrderNo(String purchaseOrderNo) {
                this.purchaseOrderNo = purchaseOrderNo;
                return this;
            }

            public ConsistencyCheckReportBuilder issueType(ConsistencyIssue issueType) {
                this.issueType = issueType;
                return this;
            }

            public ConsistencyCheckReportBuilder supplierOrderCount(int supplierOrderCount) {
                this.supplierOrderCount = supplierOrderCount;
                return this;
            }

            public ConsistencyCheckReportBuilder repairNeeded(boolean repairNeeded) {
                this.repairNeeded = repairNeeded;
                return this;
            }

            public ConsistencyCheckReportBuilder repairSuccess(boolean repairSuccess) {
                this.repairSuccess = repairSuccess;
                return this;
            }

            public ConsistencyCheckReportBuilder checkTime(LocalDateTime checkTime) {
                this.checkTime = checkTime;
                return this;
            }

            public ConsistencyCheckReportBuilder message(String message) {
                this.message = message;
                return this;
            }

            public ConsistencyCheckReport build() {
                ConsistencyCheckReport report = new ConsistencyCheckReport();
                report.purchaseOrderNo = this.purchaseOrderNo;
                report.issueType = this.issueType;
                report.supplierOrderCount = this.supplierOrderCount;
                report.repairNeeded = this.repairNeeded;
                report.repairSuccess = this.repairSuccess;
                report.checkTime = this.checkTime;
                report.message = this.message;
                return report;
            }
        }

        // Getters
        public String getPurchaseOrderNo() {
            return purchaseOrderNo;
        }

        public ConsistencyIssue getIssueType() {
            return issueType;
        }

        public int getSupplierOrderCount() {
            return supplierOrderCount;
        }

        public boolean isRepairNeeded() {
            return repairNeeded;
        }

        public boolean isRepairSuccess() {
            return repairSuccess;
        }

        public LocalDateTime getCheckTime() {
            return checkTime;
        }

        public String getMessage() {
            return message;
        }
    }
}