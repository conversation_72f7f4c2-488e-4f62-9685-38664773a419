/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.handler;

import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单事件处理器 - 优化版本
 *
 * <pre>
 * 职责：
 * 1. 接收和验证webhook消息
 * 2. 路由到相应的业务处理服务
 * 3. 统一的异常处理和日志记录
 * 4. 支持新旧版本数据的兼容处理
 *
 * 优化要点：
 * - 简化Handler职责，将业务逻辑委托给Service层
 * - 改进异常处理和日志记录
 * - 集成订单状态管理系统
 * - 支持数据补齐和兼容性处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/11 10:52
 * @description 订单webhook事件处理器 物流信息的处理
 * @since 1.0.0
 */
@Slf4j
@Component
public class LogisticsHandler extends AbstractTypedMessageHandler<LogisticsMessage> {

    /**
     * 支持的消息类型
     */
    private static final CallbackMessageType[] SUPPORTED_TYPES = List.of(
        // 物流轨迹
        LogisticsMessageTypeEnums.LOGISTICS_BUYER_VIEW_TRACE,
        // 物流单号变更
        LogisticsMessageTypeEnums.LOGISTICS_MAIL_NO_CHANGE).toArray(new CallbackMessageType[0]);

    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;

    public LogisticsHandler(SysAlibabaCallbackLogsRepository callbackLogsRepository) {
        // 构造器中指定支持的消息类型
        super(SUPPORTED_TYPES);
        this.callbackLogsRepository = callbackLogsRepository;
    }

    @Override
    public List<CallbackMessageType> getSupportedTypes() {
        log.info("支持的消息类型: {}", super.getSupportedTypes());
        return super.getSupportedTypes();
    }

    @Override
    protected void doHandle(LogisticsMessage data, MessageEvent<LogisticsMessage> event) {
        String messageType = event.getType().getMessageType();
        // String orderId = String.valueOf(data.getOrderId());
        String logisticsId = data.getLogisticsId();
        String msgId = event.getMsgId();

        try {
            log.info("接收到 物流 webhook消息: msgId={}, type={}, logisticsId={}, status={},gmtBorn={}", msgId, messageType, logisticsId, data.getOrderLogsItems(), event.getGmtBorn());

            // 仅记录日志，不处理业务逻辑 - 业务逻辑由延迟处理任务处理
            Long logId = callbackLogsRepository.createProcessingLog(
                event.getRawData(),
                null,
                messageType,
                logisticsId,
                event.getReceivedAt()
            );

            if (logId != null) {
                log.info("Webhook消息已记录，等待延迟处理: msgId={}, logId={}, logisticsId={}, gmtBorn={}",
                    msgId, logId, logisticsId, event.getGmtBorn());
                // 记录成功后立即标记为成功（这里的成功指的是成功记录，不是业务处理成功）
                callbackLogsRepository.markSuccess(logId);
            } else {
                log.error("记录Webhook消息失败: msgId={}, logisticsId={}", msgId, logisticsId);
            }

        } catch (Exception e) {
            log.error("处理Webhook消息记录异常: msgId={}, type={}, logisticsId={}, error={}",
                msgId, messageType, logisticsId, e.getMessage(), e);
            // 记录失败不抛异常，避免影响webhook响应
        }
    }

    @Override
    public boolean canHandle(CallbackMessageType messageType) {
        return super.canHandle(messageType);
    }

    @Override
    protected Class<LogisticsMessage> getDataClass() {
        return LogisticsMessage.class;
    }
}
