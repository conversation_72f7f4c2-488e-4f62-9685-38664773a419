/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单同步状态管理器
 *
 * <p>统一管理WMS和外部平台（如阿里巴巴1688）的同步状态，确保状态一致性和数据完整性</p>
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 实现统一的同步状态管理，避免状态不一致问题
 * @since 1.0.0
 */
@Component
public class OrderSyncStatusManager {

    private static final Logger log = LoggerFactory.getLogger(OrderSyncStatusManager.class);

    /**
     * 同步状态变更记录缓存，用于审计和调试
     * Key: 订单号, Value: 状态变更历史
     */
    private final Map<String, List<SyncStatusChangeRecord>> syncStatusHistory = new ConcurrentHashMap<>();

    /**
     * 更新供应商订单的外部同步状态
     *
     * @param supplierOrder   供应商订单
     * @param newSyncStatus   新的同步状态
     * @param syncReason      同步原因
     * @param externalOrderId 外部订单ID（可选）
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierSyncStatus(TzOrderSupplier supplierOrder,
        OrderSupplierSyncStatusEnums newSyncStatus,
        String syncReason,
        String externalOrderId) {

        if (supplierOrder == null) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAM_ERROR, "供应商订单不能为空");
        }

        String orderNo = supplierOrder.getSupplierOrderNo();
        OrderSupplierSyncStatusEnums currentStatus = supplierOrder.getExternalSyncStatus();

        log.info("更新供应商订单 {} 同步状态：{} -> {}，原因：{}",
            orderNo, currentStatus, newSyncStatus, syncReason);

        // 验证状态转换的合法性
        validateSyncStatusTransition(currentStatus, newSyncStatus, orderNo);

        // 记录状态变更历史
        recordSyncStatusChange(orderNo, currentStatus, newSyncStatus, syncReason);

        // 更新订单同步状态
        supplierOrder.setExternalSyncStatus(newSyncStatus);
        // 注意：lastSyncTime 和 externalOrderNo 字段需要在实体类中确认存在

        // 如果有外部订单ID，可以通过其他字段记录（如metadataJson）
        if (externalOrderId != null && !externalOrderId.trim().isEmpty()) {
            // 可以通过 metadataJson 存储外部订单ID
            log.debug("记录外部订单ID {} 到订单 {}", externalOrderId, orderNo);
        }

        // 如果同步失败，记录失败原因
        if (newSyncStatus == OrderSupplierSyncStatusEnums.SYNC_FAILED) {
            supplierOrder.setMetadataJson(buildFailureMetadata(syncReason));
        }

        log.debug("供应商订单 {} 同步状态更新完成", orderNo);
    }

    /**
     * 批量更新供应商订单同步状态
     *
     * @param supplierOrders 供应商订单列表
     * @param newSyncStatus  新的同步状态
     * @param syncReason     同步原因
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateSupplierSyncStatus(List<TzOrderSupplier> supplierOrders,
        OrderSupplierSyncStatusEnums newSyncStatus,
        String syncReason) {

        if (supplierOrders == null || supplierOrders.isEmpty()) {
            log.warn("批量更新同步状态：供应商订单列表为空");
            return;
        }

        log.info("批量更新 {} 个供应商订单同步状态为：{}，原因：{}",
            supplierOrders.size(), newSyncStatus, syncReason);

        for (TzOrderSupplier supplierOrder : supplierOrders) {
            try {
                updateSupplierSyncStatus(supplierOrder, newSyncStatus, syncReason, null);
            } catch (Exception e) {
                log.error("批量更新供应商订单 {} 同步状态失败",
                    supplierOrder.getSupplierOrderNo(), e);
                // 继续处理其他订单，不中断整个批量操作
            }
        }

        log.info("批量更新供应商订单同步状态完成");
    }

    /**
     * 检查采购订单的同步完整性
     *
     * @param purchaseOrder  采购订单
     * @param supplierOrders 关联的供应商订单列表
     * @return 同步完整性报告
     */
    public SyncIntegrityReport checkSyncIntegrity(TzOrderPurchase purchaseOrder,
        List<TzOrderSupplier> supplierOrders) {

        if (purchaseOrder == null || supplierOrders == null || supplierOrders.isEmpty()) {
            return SyncIntegrityReport.builder()
                .purchaseOrderNo(purchaseOrder != null ? purchaseOrder.getPurchaseOrderNo() : "Unknown")
                .isIntegrityValid(false)
                .message("采购订单或供应商订单为空")
                .build();
        }

        String purchaseOrderNo = purchaseOrder.getPurchaseOrderNo();

        // 统计各同步状态的订单数量
        Map<OrderSupplierSyncStatusEnums, Long> statusCount = supplierOrders.stream()
            .collect(Collectors.groupingBy(
                TzOrderSupplier::getExternalSyncStatus,
                Collectors.counting()));

        // 检查是否存在同步失败的订单
        long failedCount = statusCount.getOrDefault(OrderSupplierSyncStatusEnums.SYNC_FAILED, 0L);
        long notSyncedCount = statusCount.getOrDefault(OrderSupplierSyncStatusEnums.NOT_SYNCED, 0L);
        long syncedCount = statusCount.getOrDefault(OrderSupplierSyncStatusEnums.SYNCED, 0L);

        boolean isIntegrityValid = failedCount == 0 && notSyncedCount == 0;

        String message = String.format("同步状态统计 - 已同步: %d, 未同步: %d, 同步失败: %d",
            syncedCount, notSyncedCount, failedCount);

        log.debug("采购订单 {} 同步完整性检查：{}", purchaseOrderNo, message);

        return SyncIntegrityReport.builder()
            .purchaseOrderNo(purchaseOrderNo)
            .isIntegrityValid(isIntegrityValid)
            .syncedCount((int) syncedCount)
            .notSyncedCount((int) notSyncedCount)
            .failedCount((int) failedCount)
            .totalSupplierOrders(supplierOrders.size())
            .message(message)
            .statusDistribution(statusCount)
            .build();
    }

    /**
     * 获取订单同步状态变更历史
     *
     * @param orderNo 订单号
     * @return 状态变更记录列表
     */
    public List<SyncStatusChangeRecord> getSyncStatusHistory(String orderNo) {
        return syncStatusHistory.getOrDefault(orderNo, List.of());
    }

    /**
     * 清理指定订单的状态变更历史（用于释放内存）
     *
     * @param orderNo 订单号
     */
    public void clearSyncStatusHistory(String orderNo) {
        syncStatusHistory.remove(orderNo);
        log.debug("清理订单 {} 的同步状态历史记录", orderNo);
    }

    /**
     * 验证同步状态转换的合法性
     */
    private void validateSyncStatusTransition(OrderSupplierSyncStatusEnums currentStatus,
        OrderSupplierSyncStatusEnums newStatus,
        String orderNo) {

        // 已同步状态不能直接转换为未同步状态
        if (currentStatus == OrderSupplierSyncStatusEnums.SYNCED &&
            newStatus == OrderSupplierSyncStatusEnums.NOT_SYNCED) {

            log.warn("订单 {} 状态转换异常：已同步状态不能直接转为未同步状态", orderNo);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND,
                "已同步订单不能重置为未同步状态");
        }

        // 可以从任何状态转换为同步失败状态（异常处理）
        // 可以从失败状态重新尝试同步
        log.debug("订单 {} 状态转换验证通过：{} -> {}", orderNo, currentStatus, newStatus);
    }

    /**
     * 记录同步状态变更历史
     */
    private void recordSyncStatusChange(String orderNo,
        OrderSupplierSyncStatusEnums oldStatus,
        OrderSupplierSyncStatusEnums newStatus,
        String reason) {

        SyncStatusChangeRecord record = SyncStatusChangeRecord.builder()
            .orderNo(orderNo)
            .oldStatus(oldStatus)
            .newStatus(newStatus)
            .reason(reason)
            .changeTime(LocalDateTime.now())
            .build();

        syncStatusHistory.computeIfAbsent(orderNo, k -> new java.util.ArrayList<>()).add(record);

        // 限制历史记录数量，避免内存泄露
        List<SyncStatusChangeRecord> history = syncStatusHistory.get(orderNo);
        if (history.size() > 50) {
            // 移除最旧的记录
            history.removeFirst();
        }
    }

    /**
     * 构建失败元数据JSON
     */
    private String buildFailureMetadata(String reason) {
        return String.format("{\"syncFailureReason\": \"%s\", \"failureTime\": \"%s\"}",
            reason, LocalDateTime.now());
    }

    /**
     * 同步完整性报告
     */
    @Getter
    public static class SyncIntegrityReport {

        // Getters
        private String purchaseOrderNo;
        private boolean isIntegrityValid;
        private int syncedCount;
        private int notSyncedCount;
        private int failedCount;
        private int totalSupplierOrders;
        private String message;
        private Map<OrderSupplierSyncStatusEnums, Long> statusDistribution;

        // 使用lombok的@Builder和@Data注解，这里手动实现
        public static SyncIntegrityReportBuilder builder() {
            return new SyncIntegrityReportBuilder();
        }

        // Builder 内部类
        public static class SyncIntegrityReportBuilder {

            private String purchaseOrderNo;
            private boolean isIntegrityValid;
            private int syncedCount;
            private int notSyncedCount;
            private int failedCount;
            private int totalSupplierOrders;
            private String message;
            private Map<OrderSupplierSyncStatusEnums, Long> statusDistribution;

            public SyncIntegrityReportBuilder purchaseOrderNo(String purchaseOrderNo) {
                this.purchaseOrderNo = purchaseOrderNo;
                return this;
            }

            public SyncIntegrityReportBuilder isIntegrityValid(boolean isIntegrityValid) {
                this.isIntegrityValid = isIntegrityValid;
                return this;
            }

            public SyncIntegrityReportBuilder syncedCount(int syncedCount) {
                this.syncedCount = syncedCount;
                return this;
            }

            public SyncIntegrityReportBuilder notSyncedCount(int notSyncedCount) {
                this.notSyncedCount = notSyncedCount;
                return this;
            }

            public SyncIntegrityReportBuilder failedCount(int failedCount) {
                this.failedCount = failedCount;
                return this;
            }

            public SyncIntegrityReportBuilder totalSupplierOrders(int totalSupplierOrders) {
                this.totalSupplierOrders = totalSupplierOrders;
                return this;
            }

            public SyncIntegrityReportBuilder message(String message) {
                this.message = message;
                return this;
            }

            public SyncIntegrityReportBuilder statusDistribution(Map<OrderSupplierSyncStatusEnums, Long> statusDistribution) {
                this.statusDistribution = statusDistribution;
                return this;
            }

            public SyncIntegrityReport build() {
                SyncIntegrityReport report = new SyncIntegrityReport();
                report.purchaseOrderNo = this.purchaseOrderNo;
                report.isIntegrityValid = this.isIntegrityValid;
                report.syncedCount = this.syncedCount;
                report.notSyncedCount = this.notSyncedCount;
                report.failedCount = this.failedCount;
                report.totalSupplierOrders = this.totalSupplierOrders;
                report.message = this.message;
                report.statusDistribution = this.statusDistribution;
                return report;
            }
        }

    }

    /**
     * 同步状态变更记录
     */
    @Getter
    public static class SyncStatusChangeRecord {

        // Getters
        private String orderNo;
        private OrderSupplierSyncStatusEnums oldStatus;
        private OrderSupplierSyncStatusEnums newStatus;
        private String reason;
        private LocalDateTime changeTime;

        public static SyncStatusChangeRecordBuilder builder() {
            return new SyncStatusChangeRecordBuilder();
        }

        // Builder 内部类
        public static class SyncStatusChangeRecordBuilder {

            private String orderNo;
            private OrderSupplierSyncStatusEnums oldStatus;
            private OrderSupplierSyncStatusEnums newStatus;
            private String reason;
            private LocalDateTime changeTime;

            public SyncStatusChangeRecordBuilder orderNo(String orderNo) {
                this.orderNo = orderNo;
                return this;
            }

            public SyncStatusChangeRecordBuilder oldStatus(OrderSupplierSyncStatusEnums oldStatus) {
                this.oldStatus = oldStatus;
                return this;
            }

            public SyncStatusChangeRecordBuilder newStatus(OrderSupplierSyncStatusEnums newStatus) {
                this.newStatus = newStatus;
                return this;
            }

            public SyncStatusChangeRecordBuilder reason(String reason) {
                this.reason = reason;
                return this;
            }

            public SyncStatusChangeRecordBuilder changeTime(LocalDateTime changeTime) {
                this.changeTime = changeTime;
                return this;
            }

            public SyncStatusChangeRecord build() {
                SyncStatusChangeRecord record = new SyncStatusChangeRecord();
                record.orderNo = this.orderNo;
                record.oldStatus = this.oldStatus;
                record.newStatus = this.newStatus;
                record.reason = this.reason;
                record.changeTime = this.changeTime;
                return record;
            }
        }

    }
}
