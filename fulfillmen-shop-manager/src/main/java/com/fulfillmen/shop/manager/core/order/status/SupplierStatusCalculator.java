/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 供应商状态计算器 核心算法：订单项状态 → 供应商订单状态聚合
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 实现订单项到供应商订单的状态聚合算法
 * @since 1.0.0
 */
@Component
public class SupplierStatusCalculator {

    private static final Logger logger = LoggerFactory.getLogger(SupplierStatusCalculator.class);

    /**
     * 聚合订单项状态到供应商订单状态
     *
     * @param orderItems            订单项列表（同一供应商）
     * @param currentSupplierStatus 当前供应商订单状态
     * @return 聚合后的供应商订单状态
     */
    public TzOrderSupplierStatusEnum aggregateItemStatus(List<TzOrderItem> orderItems,
        TzOrderSupplierStatusEnum currentSupplierStatus) {

        if (orderItems == null || orderItems.isEmpty()) {
            logger.warn("订单项列表为空，保持当前状态: {}", currentSupplierStatus);
            return currentSupplierStatus;
        }

        // 检查是否在可聚合的状态范围内
        if (!isAggregatable(currentSupplierStatus)) {
            logger.debug("当前状态 {} 不支持聚合，保持原状态", currentSupplierStatus);
            return currentSupplierStatus;
        }

        // 统计各状态订单项数量
        ItemStatusStatistics stats = calculateItemStatusStatistics(orderItems);

        logger.debug("订单项状态统计 - 总数: {}, 待处理: {}, 进行中: {}, 已取消: {}, 物流中: {}, 已入库: {}",
            stats.totalCount, stats.pendingCount, stats.inProgressCount,
            stats.cancelledCount, stats.shippedCount, stats.stockedCount);

        // 执行聚合算法
        return executeItemAggregationAlgorithm(stats, currentSupplierStatus);
    }

    /**
     * 检查当前状态是否支持聚合
     */
    private boolean isAggregatable(TzOrderSupplierStatusEnum status) {
        // 最终状态不参与聚合
        return status != null &&
            status != TzOrderSupplierStatusEnum.CANCELLED &&
            status != TzOrderSupplierStatusEnum.COMPLETED;
    }

    /**
     * 计算订单项状态统计信息
     */
    private ItemStatusStatistics calculateItemStatusStatistics(List<TzOrderItem> orderItems) {
        ItemStatusStatistics stats = new ItemStatusStatistics();
        stats.totalCount = orderItems.size();

        for (TzOrderItem item : orderItems) {
            TzOrderItemStatusEnum status = item.getStatus();

            switch (status) {
                case PENDING:
                    stats.pendingCount++;
                    break;
                case PROCUREMENT_IN_PROGRESS:
                    stats.inProgressCount++;
                    break;
                case SHIPPED:
                    stats.shippedCount++;
                    break;
                case DELIVERED:
                    stats.receivedCount++;
                    break;
                case COMPLETED:
                    stats.stockedCount++;
                    break;
                case CANCELLED:
                    stats.cancelledCount++;
                    break;
                case FAILED:
                    stats.cancelledCount++; // 失败状态视为取消处理
                    break;
                default:
                    stats.pendingCount++; // 默认归类为待处理
                    break;
            }
        }

        return stats;
    }

    /**
     * 执行订单项聚合算法核心逻辑
     */
    private TzOrderSupplierStatusEnum executeItemAggregationAlgorithm(ItemStatusStatistics stats,
        TzOrderSupplierStatusEnum currentStatus) {

        // 算法 1：全部取消 → 供应商订单取消
        if (stats.cancelledCount == stats.totalCount) {
            logger.info("所有订单项已取消，聚合状态: CANCELLED");
            return TzOrderSupplierStatusEnum.CANCELLED;
        }

        // 算法 2：全部入库 → 供应商订单入库
        if (stats.stockedCount == stats.totalCount) {
            logger.info("所有订单项已入库，聚合状态: COMPLETED");
            return TzOrderSupplierStatusEnum.COMPLETED;
        }

        // 算法 3：全部或大部分已收货 → 仓库已收货
        if (stats.receivedCount + stats.stockedCount == stats.totalCount) {
            logger.info("所有订单项已收货，聚合状态: WAREHOUSE_RECEIVED");
            return TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED;
        }

        // 算法 4：全部或大部分已发货 → 已发货（待收货）
        if (stats.shippedCount + stats.receivedCount + stats.stockedCount >= stats.totalCount * 0.8) {
            double shippingRate = (double) (stats.shippedCount + stats.receivedCount + stats.stockedCount) / stats.totalCount;
            logger.info("发货率 {:.1%}，聚合状态: SHIPPED", shippingRate);
            return TzOrderSupplierStatusEnum.SHIPPED;
        }

        // 算法 5：全部待处理 → 待支付（特殊情况）
        if (stats.pendingCount == stats.totalCount) {
            logger.info("所有订单项待处理，聚合状态: PENDING_PAYMENT");
            return TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        }

        // 算法 6：单个采购中订单项 → 待支付（特殊情况）
        if (stats.totalCount == 1 && stats.inProgressCount == 1) {
            logger.info("单个采购中订单项，聚合状态: PENDING_PAYMENT");
            return TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        }

        // 算法 7：混合状态判断 - 基于业务优先级和场景分析
        if (stats.inProgressCount > 0 && stats.shippedCount > 0) {
            // 场景分析：存在物流中的订单项（已经开始发货）
            // 根据测试用例分析：
            // - 如果采购中占主导且没有其他待处理项：优先采购状态
            // - 如果物流中订单项存在：应优先考虑仓库待收货状态

            // 特殊场景：包含PENDING状态的混合场景，采购为主
            if (stats.pendingCount > 0) {
                logger.info("包含待处理项的混合状态，采购为主导，聚合状态: PENDING_SHIPMENT");
                return TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
            }
            // 纯物流+采购混合场景：物流为主（已经有订单项进入物流环节）
            else {
                logger.info("纯物流+采购混合状态，物流为主导，聚合状态: WAREHOUSE_PENDING_RECEIPT");
                return TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT;
            }
        }

        // 算法 8：纯物流中订单项 → 仓库待收货
        if (stats.shippedCount > 0 || stats.receivedCount > 0) {
            logger.info("存在纯物流中订单项，聚合状态: WAREHOUSE_PENDING_RECEIPT");
            return TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT;
        }

        // 算法 9：纯采购中订单项 → 待发货
        if (stats.inProgressCount > 0) {
            logger.info("存在纯采购中订单项，聚合状态: PENDING_SHIPMENT");
            return TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
        }

        // 默认保持当前状态
        logger.debug("无法确定聚合状态，保持当前状态: {}", currentStatus);
        return currentStatus;
    }

    /**
     * 订单项状态统计信息
     */
    private static class ItemStatusStatistics {

        int totalCount = 0;
        int pendingCount = 0;         // 待处理
        int inProgressCount = 0;      // 采购中
        int shippedCount = 0;         // 已发货
        int receivedCount = 0;        // 已收货
        int stockedCount = 0;         // 已入库
        int cancelledCount = 0;       // 已取消
    }

    /**
     * 获取聚合状态描述（用于日志和调试）
     */
    public String getAggregationDescription(List<TzOrderItem> orderItems,
        TzOrderSupplierStatusEnum aggregatedStatus) {
        if (orderItems == null || orderItems.isEmpty()) {
            return "无订单项";
        }

        ItemStatusStatistics stats = calculateItemStatusStatistics(orderItems);
        String statusName = aggregatedStatus != null ? aggregatedStatus.getDescription() : "未知";
        return String.format("订单项状态分布 - 总数:%d, 待处理:%d, 采购中:%d, 物流中:%d, 已入库:%d, 已取消:%d → 聚合状态:%s",
            stats.totalCount, stats.pendingCount, stats.inProgressCount,
            (stats.shippedCount + stats.receivedCount), stats.stockedCount, stats.cancelledCount,
            statusName);
    }

    /**
     * 验证聚合结果的合理性
     */
    public boolean validateAggregationResult(List<TzOrderItem> orderItems,
        TzOrderSupplierStatusEnum originalStatus,
        TzOrderSupplierStatusEnum aggregatedStatus) {

        // 基本验证：不能逆向聚合（除非是取消状态）
        if (aggregatedStatus != TzOrderSupplierStatusEnum.CANCELLED) {
            if (aggregatedStatus.getValue() < originalStatus.getValue()) {
                logger.warn("检测到逆向状态聚合: {} → {}", originalStatus, aggregatedStatus);
                return false;
            }
        }

        // 状态一致性验证
        ItemStatusStatistics stats = calculateItemStatusStatistics(orderItems);

        // 如果所有订单项都取消了，聚合状态必须是取消
        if (stats.cancelledCount == stats.totalCount &&
            aggregatedStatus != TzOrderSupplierStatusEnum.CANCELLED) {
            logger.error("状态不一致：所有订单项已取消，但聚合状态不是CANCELLED");
            return false;
        }

        // 如果所有订单项都入库了，聚合状态必须是完成
        if (stats.stockedCount == stats.totalCount &&
            aggregatedStatus != TzOrderSupplierStatusEnum.COMPLETED) {
            logger.error("状态不一致：所有订单项已入库，但聚合状态不是COMPLETED");
            return false;
        }

        logger.debug("聚合结果验证通过: {} → {}", originalStatus, aggregatedStatus);
        return true;
    }

    /**
     * 计算供应商订单完成度
     *
     * @param orderItems 订单项列表
     * @return 完成度百分比 (0-100)
     */
    public double calculateCompletionRate(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return 0.0;
        }

        ItemStatusStatistics stats = calculateItemStatusStatistics(orderItems);

        // 计算权重完成度
        double completionScore = 0.0;
        completionScore += stats.inProgressCount * 0.4;     // 采购中 40%
        completionScore += stats.shippedCount * 0.6;        // 已发货 60%
        completionScore += stats.receivedCount * 0.8;       // 已收货 80%
        completionScore += stats.stockedCount * 1.0;        // 已入库 100%

        return (completionScore / stats.totalCount) * 100;
    }

    /**
     * 获取供应商订单的关键瓶颈信息
     */
    public String getBottleneckAnalysis(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return "无订单项数据";
        }

        ItemStatusStatistics stats = calculateItemStatusStatistics(orderItems);

        if (stats.pendingCount > 0) {
            return String.format("存在 %d 个待处理订单项，可能影响整体进度", stats.pendingCount);
        }

        if (stats.inProgressCount > stats.totalCount * 0.6) {
            return "大部分订单项仍在采购中，建议关注采购进度";
        }

        if (stats.shippedCount > 0 && stats.receivedCount == 0) {
            return String.format("有 %d 个订单项已发货但未收货，建议关注物流状态", stats.shippedCount);
        }

        return "订单项进展正常";
    }
}
