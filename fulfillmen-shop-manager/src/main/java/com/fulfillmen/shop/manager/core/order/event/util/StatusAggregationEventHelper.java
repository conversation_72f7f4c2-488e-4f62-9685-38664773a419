/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event.util;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.SupplierOrderStatusChangeEvent;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 状态聚合事件助手类
 *
 * <pre>
 * 提供状态聚合过程中的事件处理辅助功能：
 * 1. 事件链管理和防循环检查
 * 2. 聚合上下文构建和分析
 * 3. 事件优先级和调度管理
 * 4. 聚合结果验证和一致性检查
 * 5. 批量事件处理优化
 * 6. 事件追踪和审计支持
 * 7. 异常处理和恢复机制
 *
 * 核心功能：
 * - 构建完整的事件上下文信息
 * - 提供聚合算法的事件适配器
 * - 管理事件传播链路和依赖关系
 * - 支持事件的异步处理和重试机制
 *
 * 使用场景：
 * - 订单项状态聚合为供应商订单状态
 * - 供应商订单状态聚合为采购订单状态
 * - 批量状态变更的事件协调
 * - 异常状态的自动修复
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/31
 * @description 状态聚合事件处理助手，优化三层聚合的事件协调
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StatusAggregationEventHelper {

    private final OrderStatusEventFactory eventFactory;

    // ========== 聚合上下文构建 ==========

    /**
     * 构建订单项聚合上下文
     *
     * @param triggerEvent     触发事件
     * @param allOrderItems    同供应商订单下的所有订单项
     * @param aggregatedStatus 聚合后的状态
     * @return 聚合上下文
     */
    public SupplierOrderStatusChangeEvent.AggregationContext buildItemAggregationContext(
        OrderItemStatusChangeEvent triggerEvent,
        List<TzOrderItem> allOrderItems,
        TzOrderSupplierStatusEnum aggregatedStatus) {

        try {
            // 统计信息
            int triggerItemCount = 1;
            int totalItemCount = allOrderItems.size();

            // 计算完成率
            double completionRate = calculateItemCompletionRate(allOrderItems);

            // 生成聚合详情描述
            String aggregationDetails = generateItemAggregationDescription(
                triggerEvent, allOrderItems, aggregatedStatus);

            return eventFactory.createAggregationContext(
                triggerItemCount,
                totalItemCount,
                "v1.0",
                completionRate,
                aggregationDetails
            );

        } catch (Exception e) {
            log.error("构建订单项聚合上下文失败: triggerEvent={}", triggerEvent, e);
            return createDefaultAggregationContext(triggerEvent, allOrderItems.size());
        }
    }

    /**
     * 构建批量订单项聚合上下文
     */
    public SupplierOrderStatusChangeEvent.AggregationContext buildBatchItemAggregationContext(
        OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent batchEvent,
        List<TzOrderItem> allOrderItems,
        TzOrderSupplierStatusEnum aggregatedStatus) {

        try {
            int triggerItemCount = batchEvent.getChangeCount();
            int totalItemCount = allOrderItems.size();

            double completionRate = calculateItemCompletionRate(allOrderItems);

            String aggregationDetails = generateBatchItemAggregationDescription(
                batchEvent, allOrderItems, aggregatedStatus);

            return eventFactory.createAggregationContext(
                triggerItemCount,
                totalItemCount,
                "v1.0",
                completionRate,
                aggregationDetails
            );

        } catch (Exception e) {
            log.error("构建批量订单项聚合上下文失败: batchEvent={}", batchEvent, e);
            return createDefaultAggregationContext(batchEvent, allOrderItems.size());
        }
    }

    // ========== 聚合分析和统计 ==========

    /**
     * 计算订单项完成率
     */
    public double calculateItemCompletionRate(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return 0.0;
        }

        long completedCount = orderItems.stream()
            .mapToLong(item -> {
                TzOrderItemStatusEnum status = item.getStatus();
                return (status == TzOrderItemStatusEnum.COMPLETED ||
                    status == TzOrderItemStatusEnum.DELIVERED) ? 1 : 0;
            })
            .sum();

        return (double) completedCount / orderItems.size() * 100.0;
    }

    /**
     * 计算供应商订单完成率
     */
    public double calculateSupplierOrderCompletionRate(List<TzOrderSupplier> supplierOrders) {
        if (supplierOrders == null || supplierOrders.isEmpty()) {
            return 0.0;
        }

        long completedCount = supplierOrders.stream()
            .mapToLong(order -> {
                TzOrderSupplierStatusEnum status = order.getStatus();
                return (status == TzOrderSupplierStatusEnum.COMPLETED ||
                    status == TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED) ? 1 : 0;
            })
            .sum();

        return (double) completedCount / supplierOrders.size() * 100.0;
    }

    /**
     * 分析订单项状态分布
     */
    public Map<TzOrderItemStatusEnum, Long> analyzeItemStatusDistribution(List<TzOrderItem> orderItems) {
        if (orderItems == null) {
            return Map.of();
        }

        return orderItems.stream()
            .collect(Collectors.groupingBy(TzOrderItem::getStatus, Collectors.counting()));
    }

    /**
     * 分析供应商订单状态分布
     */
    public Map<TzOrderSupplierStatusEnum, Long> analyzeSupplierOrderStatusDistribution(
        List<TzOrderSupplier> supplierOrders) {
        if (supplierOrders == null) {
            return Map.of();
        }

        return supplierOrders.stream()
            .collect(Collectors.groupingBy(TzOrderSupplier::getStatus, Collectors.counting()));
    }

    // ========== 事件优先级和调度 ==========

    /**
     * 判断事件优先级
     */
    public EventPriority determineEventPriority(OrderItemStatusChangeEvent event) {
        if (event.isCriticalStatusChange()) {
            return EventPriority.HIGH;
        }

        // 批量事件在单独的方法中处理，单个事件不会是批量事件类型
        // 这里的event始终是OrderItemStatusChangeEvent，不是BatchOrderItemStatusChangeEvent

        return EventPriority.NORMAL;
    }

    /**
     * 判断事件优先级
     */
    public EventPriority determineEventPriority(SupplierOrderStatusChangeEvent event) {
        if (event.isCriticalStatusChange()) {
            return EventPriority.HIGH;
        }

        if (event.isTriggeredByItemAggregation()) {
            return EventPriority.NORMAL;
        }

        return EventPriority.LOW;
    }

    /**
     * 判断事件优先级
     */
    public EventPriority determineEventPriority(OrderStatusChangeEvent event) {
        if (event.isCriticalStatusChange()) {
            return EventPriority.HIGH;
        }

        if (event.isTriggeredBySupplierAggregation()) {
            return EventPriority.NORMAL;
        }

        return EventPriority.LOW;
    }

    // ========== 事件链管理 ==========

    /**
     * 检查是否需要防循环处理
     */
    public boolean needsCircularProtection(String eventChainId) {
        return eventChainId != null && !eventChainId.trim().isEmpty();
    }

    /**
     * 生成新的事件链ID（继承原链）
     */
    public String generateInheritedEventChainId(String parentChainId) {
        if (parentChainId == null || parentChainId.trim().isEmpty()) {
            return eventFactory.generateEventChainId();
        }

        // 继承父链，添加层级标识
        return parentChainId + "_L" + System.nanoTime();
    }

    /**
     * 分析事件链深度
     */
    public int analyzeEventChainDepth(String eventChainId) {
        if (eventChainId == null) {
            return 0;
        }

        long layerCount = eventChainId.chars().filter(ch -> ch == '_').count();
        return (int) Math.min(layerCount, Integer.MAX_VALUE);
    }

    /**
     * 检查事件链深度是否超限
     */
    public boolean isEventChainDepthExceeded(String eventChainId, int maxDepth) {
        return analyzeEventChainDepth(eventChainId) > maxDepth;
    }

    // ========== 异常处理和恢复 ==========

    /**
     * 创建事件处理异常报告
     */
    public EventProcessingErrorReport createErrorReport(Exception error,
        Object event, String processingPhase) {

        return EventProcessingErrorReport.builder()
            .error(error)
            .event(event)
            .processingPhase(processingPhase)
            .timestamp(System.currentTimeMillis())
            .errorMessage(error.getMessage())
            .canRetry(isRetriableError(error))
            .build();
    }

    /**
     * 判断错误是否可重试
     */
    public boolean isRetriableError(Exception error) {
        // 业务逻辑错误通常不可重试
        if (error instanceof IllegalArgumentException ||
            error instanceof IllegalStateException) {
            return false;
        }

        // 网络、数据库等基础设施错误可重试
        return error instanceof RuntimeException;
    }

    /**
     * 生成聚合恢复建议
     */
    public String generateRecoveryRecommendation(EventProcessingErrorReport errorReport) {
        if (errorReport.isCanRetry()) {
            return "建议重试事件处理，使用指数退避策略";
        } else {
            return "需要人工介入处理，检查业务数据一致性";
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成订单项聚合描述
     */
    private String generateItemAggregationDescription(
        OrderItemStatusChangeEvent triggerEvent,
        List<TzOrderItem> allOrderItems,
        TzOrderSupplierStatusEnum aggregatedStatus) {

        Map<TzOrderItemStatusEnum, Long> statusDistribution = analyzeItemStatusDistribution(allOrderItems);

        return String.format(
            "订单项聚合: 触发项ID=%d (%s->%s), 总计%d项, 聚合状态=%s, 分布=%s",
            triggerEvent.getOrderItemId(),
            triggerEvent.getOriginalStatus().getDesc(),
            triggerEvent.getNewStatus().getDesc(),
            allOrderItems.size(),
            aggregatedStatus.getDescription(),
            formatStatusDistribution(statusDistribution)
        );
    }

    /**
     * 生成批量订单项聚合描述
     */
    private String generateBatchItemAggregationDescription(
        OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent batchEvent,
        List<TzOrderItem> allOrderItems,
        TzOrderSupplierStatusEnum aggregatedStatus) {

        Map<TzOrderItemStatusEnum, Long> statusDistribution = analyzeItemStatusDistribution(allOrderItems);

        return String.format(
            "批量订单项聚合: 批次ID=%s, 变更数量=%d, 总计%d项, 聚合状态=%s, 分布=%s",
            batchEvent.getBatchId(),
            batchEvent.getChangeCount(),
            allOrderItems.size(),
            aggregatedStatus.getDescription(),
            formatStatusDistribution(statusDistribution)
        );
    }

    /**
     * 格式化状态分布信息
     */
    private String formatStatusDistribution(Map<? extends Enum<?>, Long> distribution) {
        if (distribution.isEmpty()) {
            return "无数据";
        }

        return distribution.entrySet().stream()
            .map(entry -> entry.getKey().name() + ":" + entry.getValue())
            .collect(Collectors.joining(", "));
    }

    /**
     * 创建默认聚合上下文
     */
    private SupplierOrderStatusChangeEvent.AggregationContext createDefaultAggregationContext(
        Object triggerEvent, int totalItemCount) {

        return eventFactory.createAggregationContext(
            1,
            totalItemCount,
            "v1.0",
            0.0,
            "聚合上下文构建异常，使用默认值: " + triggerEvent.getClass().getSimpleName()
        );
    }

    // ========== 内部类和枚举 ==========

    /**
     * 事件优先级枚举
     */
    public enum EventPriority {

        LOW(1, "低优先级"),
        NORMAL(2, "正常优先级"),
        HIGH(3, "高优先级"),
        CRITICAL(4, "关键优先级");

        private final int level;
        private final String description;

        EventPriority(int level, String description) {
            this.level = level;
            this.description = description;
        }

        public int getLevel() {
            return level;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 事件处理错误报告
     */
    @lombok.Data
    @lombok.Builder
    public static class EventProcessingErrorReport {

        private Exception error;
        private Object event;
        private String processingPhase;
        private long timestamp;
        private String errorMessage;
        private boolean canRetry;
        private String recoveryRecommendation;
    }
}
