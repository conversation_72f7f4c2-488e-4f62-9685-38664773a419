/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order;

import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.manager.core.order.event.CancelPurchaseOrderEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderCreatedEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.ProcurementCompletedEvent;
import com.fulfillmen.shop.manager.core.order.event.ProcurementStartedEvent;
import com.fulfillmen.shop.manager.core.order.event.SupplierOrderStatusChangeEvent;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 订单事件发布器 - 统一的事件发布入口
 *
 * <pre>
 * 设计原则：
 * 1. 上层业务只需要调用相应的方法创建事件，无需关心具体的事件处理逻辑
 * 2. 所有事件处理逻辑都在event包中的监听器中实现
 * 3. 事件发布是同步的，事件处理是异步的
 * 4. 支持事件链和事件组合
 *
 * 使用方式：
 * <code>
 * // 发布订单创建事件
 * orderEventPublisher.publishOrderCreatedEvent(orderContext);
 *
 * // 发布订单支付事件
 * orderEventPublisher.publishOrderPaidEvent(orderId, paymentInfo);
 *
 * // 发布订单状态变更事件
 * orderEventPublisher.publishOrderStatusChangedEvent(orderId, oldStatus, newStatus, reason);
 * </code>
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 统一的订单事件发布器，提供简单易用的事件发布接口
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderEventPublisher {

    private final ApplicationEventPublisher eventPublisher;

    // ========== 订单生命周期事件发布 ==========

    /**
     * 发布订单创建事件
     *
     * @param orderContextDTO 订单上下文
     */
    public void publishOrderCreatedEvent(OrderContextDTO orderContextDTO) {
        try {
            OrderCreatedEvent event = new OrderCreatedEvent(
                orderContextDTO,
                String.valueOf(UserContextHolder.getTenantId()),
                String.valueOf(UserContextHolder.getUserId()),
                "ORDER_SERVICE"
            );
            eventPublisher.publishEvent(event);
            log.info("订单创建事件已发布: 采购订单号={}, 事件ID={}",
                event.getPurchaseOrderNo(), event.getEventId());
        } catch (Exception e) {
            log.error("发布订单创建事件失败: 采购订单号={}",
                orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), e);
        }
    }

    /**
     * 发布订单同步到WMS - 事件
     * <pre>
     * 同步到 WMS API 失败，需要重试处理或手动同步处理
     * </pre>
     *
     * @param orderContextDTO 订单上下文
     */
    public void publishOrderSyncWms(OrderContextDTO orderContextDTO) {
        try {
            OrderCreatedEvent createdEvent = new OrderCreatedEvent(
                orderContextDTO,
                String.valueOf(UserContextHolder.getTenantId()),
                String.valueOf(UserContextHolder.getUserId()),
                "ORDER_SERVICE"
            );

            eventPublisher.publishEvent(createdEvent);
            log.info("订单同步到WMS事件已发布: 采购订单号={}, 事件ID={}",
                createdEvent.getPurchaseOrderNo(), createdEvent.getEventId());
        } catch (Exception e) {
            log.error("发布订单同步到WMS事件失败: 采购订单号={}",
                orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), e);
        }
    }

    /**
     * 发布订单支付事件
     *
     * @param orderId       订单ID
     * @param paymentAmount 支付金额
     * @param transactionId 交易ID
     */
    public void publishOrderPaidEvent(Long orderId, BigDecimal paymentAmount,
        OrderContextDTO orderContextDTO, String transactionId) {
        try {
            // 支付成功后，发起创建 1688 订单采购单
            OrderCreatedEvent createdEvent = new OrderCreatedEvent(
                orderContextDTO,
                String.valueOf(UserContextHolder.getTenantId()),
                String.valueOf(UserContextHolder.getUserId()),
                "ORDER_SERVICE"
            );
            eventPublisher.publishEvent(createdEvent);
            log.info("订单支付事件已发布: 订单ID={}, 支付金额={}, 交易ID={}", orderId, paymentAmount, transactionId);

        } catch (Exception e) {
            log.error("发布订单支付事件失败: 订单ID={}", orderId, e);
        }
    }

//    /**
//     * 发布订单取消事件
//     *
//     * @param orderId      订单ID
//     * @param cancelReason 取消原因
//     * @param cancelledBy  取消操作人
//     */
//    public void publishOrderCancelledEvent(Long orderId, String cancelReason, String cancelledBy) {
//        try {
//            OrderCancelledEvent event = new OrderCancelledEvent(
//                orderId,
//                cancelReason,
//                cancelledBy,
//                String.valueOf(UserContextHolder.getTenantId()),
//                String.valueOf(UserContextHolder.getUserId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.info("订单取消事件已发布: 订单ID={}, 取消原因={}", orderId, cancelReason);
//
//        } catch (Exception e) {
//            log.error("发布订单取消事件失败: 订单ID={}", orderId, e);
//        }
//    }
//
//    /**
//     * 发布订单完成事件
//     *
//     * @param orderId        订单ID
//     * @param completionNote 完成备注
//     */
//    public void publishOrderCompletedEvent(Long orderId, String completionNote) {
//        try {
//            OrderCompletedEvent event = new OrderCompletedEvent(
//                orderId,
//                completionNote,
//                String.valueOf(UserContextHolder.getTenantId()),
//                String.valueOf(UserContextHolder.getUserId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.info("订单完成事件已发布: 订单ID={}", orderId);
//
//        } catch (Exception e) {
//            log.error("发布订单完成事件失败: 订单ID={}", orderId, e);
//        }
//    }

    // ========== 采购相关事件发布 ==========

    /**
     * 发布采购开始事件
     *
     * @param orderId      订单ID
     * @param supplierInfo 供应商信息
     */
    public void publishProcurementStartedEvent(Long orderId, String supplierInfo) {
        try {
            ProcurementStartedEvent event = new ProcurementStartedEvent(
                orderId,
                supplierInfo,
                String.valueOf(UserContextHolder.getTenantId())
            );

            eventPublisher.publishEvent(event);
            log.info("采购开始事件已发布: 订单ID={}, 供应商={}", orderId, supplierInfo);

        } catch (Exception e) {
            log.error("发布采购开始事件失败: 订单ID={}", orderId, e);
        }
    }

    /**
     * 发布采购完成事件
     *
     * @param orderId           订单ID
     * @param externalOrderId   外部订单ID
     * @param supplierOrderData 供应商订单数据
     */
    public void publishProcurementCompletedEvent(Long orderId, String externalOrderId,
        String supplierOrderData) {
        try {
            ProcurementCompletedEvent event = new ProcurementCompletedEvent(
                orderId,
                externalOrderId,
                supplierOrderData,
                String.valueOf(UserContextHolder.getTenantId())
            );

            eventPublisher.publishEvent(event);
            log.info("采购完成事件已发布: 订单ID={}, 外部订单ID={}", orderId, externalOrderId);

        } catch (Exception e) {
            log.error("发布采购完成事件失败: 订单ID={}", orderId, e);
        }
    }

//    /**
//     * 发布采购失败事件
//     *
//     * @param orderId       订单ID
//     * @param failureReason 失败原因
//     * @param errorDetails  错误详情
//     */
//    public void publishProcurementFailedEvent(Long orderId, String failureReason,
//        String errorDetails) {
//        try {
//            ProcurementFailedEvent event = new ProcurementFailedEvent(
//                orderId,
//                failureReason,
//                errorDetails,
//                String.valueOf(UserContextHolder.getTenantId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.warn("采购失败事件已发布: 订单ID={}, 失败原因={}", orderId, failureReason);
//
//        } catch (Exception e) {
//            log.error("发布采购失败事件失败: 订单ID={}", orderId, e);
//        }
//    }

    /**
     * 发布取消采购订单事件
     *
     * @param orderContextDTO 订单上下文
     */
    public void publishCancelPurchaseOrderEvent(OrderContextDTO orderContextDTO) {
        try {
            CancelPurchaseOrderEvent event = new CancelPurchaseOrderEvent(
                orderContextDTO,
                LocalDateTime.now(),
                String.valueOf(UserContextHolder.getTenantId()),
                String.valueOf(UserContextHolder.getUserId()),
                "ORDER_SERVICE",
                "USER"
            );

            eventPublisher.publishEvent(event);
            log.info("取消采购订单事件已发布: 采购订单号={}", event.getPurchaseOrderNo());

        } catch (Exception e) {
            log.error("发布取消采购订单事件失败: 采购订单号={}", orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), e);
        }
    }

    // ========== 三层状态变更事件发布 ==========

    /**
     * 发布订单项状态变更事件
     *
     * @param event 订单项状态变更事件
     */
    public void publishOrderItemStatusChangeEvent(OrderItemStatusChangeEvent event) {
        try {
            eventPublisher.publishEvent(event);
            log.info("订单项状态变更事件已发布: orderItemId={}, {} -> {}",
                event.getOrderItemId(),
                event.getOriginalStatus().getDesc(),
                event.getNewStatus().getDesc());
        } catch (Exception e) {
            log.error("发布订单项状态变更事件失败: orderItemId={}", event.getOrderItemId(), e);
        }
    }

    /**
     * 发布批量订单项状态变更事件
     *
     * @param event 批量订单项状态变更事件
     */
    public void publishBatchOrderItemStatusChangeEvent(OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent event) {
        try {
            eventPublisher.publishEvent(event);
            log.info("批量订单项状态变更事件已发布: batchId={}, 变更数量={}",
                event.getBatchId(), event.getChangeCount());
        } catch (Exception e) {
            log.error("发布批量订单项状态变更事件失败: batchId={}", event.getBatchId(), e);
        }
    }

    /**
     * 发布供应商订单状态变更事件
     *
     * @param event 供应商订单状态变更事件
     */
    public void publishSupplierOrderStatusChangeEvent(SupplierOrderStatusChangeEvent event) {
        try {
            eventPublisher.publishEvent(event);
            log.info("供应商订单状态变更事件已发布: supplierOrderId={}, {} -> {}",
                event.getSupplierOrderId(),
                event.getOriginalStatus().getDescription(),
                event.getNewStatus().getDescription());
        } catch (Exception e) {
            log.error("发布供应商订单状态变更事件失败: supplierOrderId={}", event.getSupplierOrderId(), e);
        }
    }

    /**
     * 发布采购订单状态变更事件
     *
     * @param event 采购订单状态变更事件
     */
    public void publishPurchaseOrderStatusChangeEvent(OrderStatusChangeEvent event) {
        try {
            eventPublisher.publishEvent(event);
            log.info("采购订单状态变更事件已发布: purchaseOrderId={}, {} -> {}",
                event.getPurchaseOrderId(),
                event.getOriginalStatus().getDescription(),
                event.getNewStatus().getDescription());
        } catch (Exception e) {
            log.error("发布采购订单状态变更事件失败: purchaseOrderId={}", event.getPurchaseOrderId(), e);
        }
    }

//
//    // ========== 物流相关事件发布 ==========
//
//    /**
//     * 发布商品发货事件
//     *
//     * @param orderId        订单ID
//     * @param trackingNumber 物流单号
//     * @param carrierName    承运商名称
//     */
//    public void publishGoodsShippedEvent(Long orderId, String trackingNumber, String carrierName) {
//        try {
//            GoodsShippedEvent event = new GoodsShippedEvent(
//                orderId,
//                trackingNumber,
//                carrierName,
//                String.valueOf(UserContextHolder.getTenantId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.info("商品发货事件已发布: 订单ID={}, 物流单号={}", orderId, trackingNumber);
//
//        } catch (Exception e) {
//            log.error("发布商品发货事件失败: 订单ID={}", orderId, e);
//        }
//    }
//
//    /**
//     * 发布商品到达仓库事件
//     *
//     * @param orderId          订单ID
//     * @param warehouseId      仓库ID
//     * @param receivedQuantity 接收数量
//     */
//    public void publishGoodsArrivedWarehouseEvent(Long orderId, String warehouseId,
//        Integer receivedQuantity) {
//        try {
//            GoodsArrivedWarehouseEvent event = new GoodsArrivedWarehouseEvent(
//                orderId,
//                warehouseId,
//                receivedQuantity,
//                String.valueOf(UserContextHolder.getTenantId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.info("商品到达仓库事件已发布: 订单ID={}, 仓库ID={}", orderId, warehouseId);
//
//        } catch (Exception e) {
//            log.error("发布商品到达仓库事件失败: 订单ID={}", orderId, e);
//        }
//    }
//
//    /**
//     * 发布商品入库事件
//     *
//     * @param orderId            订单ID
//     * @param stockedQuantity    入库数量
//     * @param qualityCheckResult 质检结果
//     */
//    public void publishGoodsStockedEvent(Long orderId, Integer stockedQuantity,
//        String qualityCheckResult) {
//        try {
//            GoodsStockedEvent event = new GoodsStockedEvent(
//                orderId,
//                stockedQuantity,
//                qualityCheckResult,
//                String.valueOf(UserContextHolder.getTenantId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.info("商品入库事件已发布: 订单ID={}, 入库数量={}", orderId, stockedQuantity);
//
//        } catch (Exception e) {
//            log.error("发布商品入库事件失败: 订单ID={}", orderId, e);
//        }
//    }

    // ========== 异常处理事件发布 ==========
//
//    /**
//     * 发布订单异常事件
//     *
//     * @param orderId          订单ID
//     * @param exceptionType    异常类型
//     * @param exceptionMessage 异常消息
//     * @param exceptionData    异常数据
//     */
//    public void publishOrderExceptionEvent(Long orderId, String exceptionType,
//        String exceptionMessage, String exceptionData) {
//        try {
//            OrderExceptionEvent event = new OrderExceptionEvent(
//                orderId,
//                exceptionType,
//                exceptionMessage,
//                exceptionData,
//                String.valueOf(UserContextHolder.getTenantId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.error("订单异常事件已发布: 订单ID={}, 异常类型={}, 异常消息={}",
//                orderId, exceptionType, exceptionMessage);
//
//        } catch (Exception e) {
//            log.error("发布订单异常事件失败: 订单ID={}", orderId, e);
//        }
//    }
//
//    // ========== 批量事件发布 ==========
//
//    /**
//     * 发布订单状态流转事件（支持批量状态变更）
//     *
//     * @param orderId    订单ID
//     * @param statusFlow 状态流转信息
//     */
//    public void publishOrderStatusFlowEvent(Long orderId, OrderStatusFlow statusFlow) {
//        try {
//            OrderStatusFlowEvent event = new OrderStatusFlowEvent(
//                orderId,
//                statusFlow,
//                String.valueOf(UserContextHolder.getTenantId())
//            );
//
//            eventPublisher.publishEvent(event);
//            log.info("订单状态流转事件已发布: 订单ID={}, 状态流转={}", orderId, statusFlow);
//
//        } catch (Exception e) {
//            log.error("发布订单状态流转事件失败: 订单ID={}", orderId, e);
//        }
//    }
//
//    /**
//     * 发布订单事件组合（同时发布多个相关事件）
//     *
//     * @param eventGroup 事件组合
//     */
//    public void publishOrderEventGroup(OrderEventGroup eventGroup) {
//        try {
//            eventGroup.getEvents().forEach(eventPublisher::publishEvent);
//
//            log.info("订单事件组合已发布: 事件数量={}, 主订单ID={}",
//                eventGroup.getEvents().size(), eventGroup.getPrimaryOrderId());
//
//        } catch (Exception e) {
//            log.error("发布订单事件组合失败: 主订单ID={}", eventGroup.getPrimaryOrderId(), e);
//        }
//    }
}
