/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 订单项状态变更事件
 *
 * <pre>
 * 当订单项状态发生变化时发布此事件，用于：
 * 1. 触发供应商订单状态聚合
 * 2. 记录订单项状态变更日志
 * 3. 发送订单项级别的通知
 * 4. 更新相关统计数据
 * 5. 触发业务规则检查
 *
 * 架构定位：
 * - 三层状态聚合体系的底层事件
 * - 订单项状态变更的统一入口
 * - 支持单个和批量订单项状态变更
 * - 自动触发上层供应商订单状态聚合
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/31
 * @description 订单项状态变更事件，触发三层状态聚合的起点
 * @since 1.0.0
 */
@Getter
public class OrderItemStatusChangeEvent extends ApplicationEvent {

    /**
     * 订单项ID
     */
    private final Long orderItemId;

    /**
     * 供应商订单ID - 用于触发上级聚合
     */
    private final Long supplierOrderId;

    /**
     * 采购订单ID - 用于事件追踪
     */
    private final Long purchaseOrderId;

    /**
     * 原始状态
     */
    private final TzOrderItemStatusEnum originalStatus;

    /**
     * 新状态
     */
    private final TzOrderItemStatusEnum newStatus;

    /**
     * 状态变更原因
     */
    private final String changeReason;

    /**
     * 状态变更时间
     */
    private final LocalDateTime changeTime;

    /**
     * 事件链ID - 用于防循环和事件追踪
     */
    private final String eventChainId;

    /**
     * 变更来源
     */
    private final ChangeSource changeSource;

    /**
     * 租户ID
     */
    private final String tenantId;

    /**
     * 用户ID
     */
    private final String userId;

    /**
     * 构造函数
     *
     * @param orderItemId     订单项ID
     * @param supplierOrderId 供应商订单ID
     * @param purchaseOrderId 采购订单ID
     * @param originalStatus  原始状态
     * @param newStatus       新状态
     * @param changeReason    变更原因
     * @param changeTime      变更时间
     * @param eventChainId    事件链ID
     * @param changeSource    变更来源
     * @param tenantId        租户ID
     * @param userId          用户ID
     */
    public OrderItemStatusChangeEvent(Long orderItemId,
        Long supplierOrderId,
        Long purchaseOrderId,
        TzOrderItemStatusEnum originalStatus,
        TzOrderItemStatusEnum newStatus,
        String changeReason,
        LocalDateTime changeTime,
        String eventChainId,
        ChangeSource changeSource,
        String tenantId,
        String userId) {
        super(orderItemId);
        this.orderItemId = orderItemId;
        this.supplierOrderId = supplierOrderId;
        this.purchaseOrderId = purchaseOrderId;
        this.originalStatus = originalStatus;
        this.newStatus = newStatus;
        this.changeReason = changeReason;
        this.changeTime = changeTime;
        this.eventChainId = eventChainId;
        this.changeSource = changeSource;
        this.tenantId = tenantId;
        this.userId = userId;
    }

    /**
     * 简化构造函数，使用当前时间
     */
    public OrderItemStatusChangeEvent(Long orderItemId,
        Long supplierOrderId,
        Long purchaseOrderId,
        TzOrderItemStatusEnum originalStatus,
        TzOrderItemStatusEnum newStatus,
        String changeReason,
        String eventChainId,
        ChangeSource changeSource,
        String tenantId,
        String userId) {
        this(orderItemId, supplierOrderId, purchaseOrderId, originalStatus, newStatus,
            changeReason, LocalDateTime.now(), eventChainId, changeSource, tenantId, userId);
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange() {
        return newStatus == TzOrderItemStatusEnum.COMPLETED ||
            newStatus == TzOrderItemStatusEnum.CANCELLED ||
            newStatus == TzOrderItemStatusEnum.FAILED;
    }

    /**
     * 判断是否需要触发供应商订单聚合
     */
    public boolean shouldTriggerSupplierAggregation() {
        // 所有状态变更都应该触发聚合，除非是测试场景
        return changeSource != ChangeSource.TEST_SCENARIO;
    }

    /**
     * 获取用户友好的状态变更描述
     */
    public String getUserFriendlyDescription() {
        return switch (newStatus) {
            case PENDING -> "订单项待处理";
            case PROCUREMENT_IN_PROGRESS -> "订单项采购中";
            case SHIPPED -> "订单项已发货";
            case DELIVERED -> "订单项已收货";
            case COMPLETED -> "订单项已完成";
            case CANCELLED -> "订单项已取消";
            case FAILED -> "订单项处理失败";
        };
    }

    /**
     * 获取事件的唯一标识
     */
    public String getEventId() {
        return String.format("ORDER_ITEM_STATUS_CHANGE-%d-%s-%d",
            orderItemId, newStatus.name(), changeTime.toEpochSecond(java.time.ZoneOffset.UTC));
    }

    /**
     * 变更来源枚举
     */
    public enum ChangeSource {

        /**
         * 系统自动处理
         */
        SYSTEM_AUTO("系统自动"),

        /**
         * 用户手动操作
         */
        USER_MANUAL("用户手动"),

        /**
         * 外部系统同步
         */
        EXTERNAL_SYNC("外部同步"),

        /**
         * 定时任务处理
         */
        SCHEDULED_TASK("定时任务"),

        /**
         * API接口调用
         */
        API_CALL("API调用"),

        /**
         * 测试场景
         */
        TEST_SCENARIO("测试场景"),

        /**
         * 数据修复
         */
        DATA_REPAIR("数据修复");

        private final String description;

        ChangeSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("OrderItemStatusChangeEvent{orderItemId=%d, supplierOrderId=%d, " +
            "purchaseOrderId=%d, %s -> %s, reason='%s', source=%s, chainId='%s', time=%s}",
            orderItemId, supplierOrderId, purchaseOrderId,
            originalStatus.getDesc(), newStatus.getDesc(),
            changeReason, changeSource.getDescription(), eventChainId, changeTime);
    }

    /**
     * 批量订单项状态变更事件
     */
    @Getter
    public static class BatchOrderItemStatusChangeEvent extends ApplicationEvent {

        /**
         * 批次ID
         */
        private final String batchId;

        /**
         * 供应商订单ID - 批量变更通常在同一供应商订单下
         */
        private final Long supplierOrderId;

        /**
         * 采购订单ID
         */
        private final Long purchaseOrderId;

        /**
         * 状态变更列表
         */
        private final List<OrderItemStatusChange> statusChanges;

        /**
         * 批次处理时间
         */
        private final LocalDateTime batchTime;

        /**
         * 事件链ID
         */
        private final String eventChainId;

        /**
         * 变更来源
         */
        private final ChangeSource changeSource;

        /**
         * 租户ID
         */
        private final String tenantId;

        /**
         * 用户ID
         */
        private final String userId;

        public BatchOrderItemStatusChangeEvent(String batchId,
            Long supplierOrderId,
            Long purchaseOrderId,
            List<OrderItemStatusChange> statusChanges,
            String eventChainId,
            ChangeSource changeSource,
            String tenantId,
            String userId) {
            super(batchId);
            this.batchId = batchId;
            this.supplierOrderId = supplierOrderId;
            this.purchaseOrderId = purchaseOrderId;
            this.statusChanges = statusChanges;
            this.batchTime = LocalDateTime.now();
            this.eventChainId = eventChainId;
            this.changeSource = changeSource;
            this.tenantId = tenantId;
            this.userId = userId;
        }

        /**
         * 获取变更数量
         */
        public int getChangeCount() {
            return statusChanges.size();
        }

        /**
         * 判断是否包含关键状态变更
         */
        public boolean containsCriticalChanges() {
            return statusChanges.stream()
                .anyMatch(change -> change.getNewStatus() == TzOrderItemStatusEnum.COMPLETED ||
                    change.getNewStatus() == TzOrderItemStatusEnum.CANCELLED ||
                    change.getNewStatus() == TzOrderItemStatusEnum.FAILED);
        }
    }

    /**
     * 订单项状态变更记录
     */
    @Getter
    public static class OrderItemStatusChange {

        private final Long orderItemId;
        private final TzOrderItemStatusEnum originalStatus;
        private final TzOrderItemStatusEnum newStatus;
        private final String changeReason;

        public OrderItemStatusChange(Long orderItemId,
            TzOrderItemStatusEnum originalStatus,
            TzOrderItemStatusEnum newStatus,
            String changeReason) {
            this.orderItemId = orderItemId;
            this.originalStatus = originalStatus;
            this.newStatus = newStatus;
            this.changeReason = changeReason;
        }
    }
}
