/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 供应商状态计算器测试
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 测试订单项状态到供应商订单状态的聚合算法
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("供应商状态计算器测试")
class SupplierStatusCalculatorTest {

    @InjectMocks
    private SupplierStatusCalculator supplierStatusCalculator;

    private TzOrderSupplierStatusEnum currentStatus;

    @BeforeEach
    void setUp() {
        currentStatus = TzOrderSupplierStatusEnum.PENDING_PAYMENT;
    }

    @Test
    @DisplayName("空订单项列表应保持当前状态")
    void shouldKeepCurrentStatusWhenOrderItemsEmpty() {
        // 测试null列表
        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(null, currentStatus);
        assertEquals(currentStatus, result, "空列表应保持当前状态");

        // 测试空列表
        result = supplierStatusCalculator.aggregateItemStatus(Collections.emptyList(), currentStatus);
        assertEquals(currentStatus, result, "空列表应保持当前状态");
    }

    @Test
    @DisplayName("最终状态应保持原状态")
    void shouldKeepOriginalStatusWhenNotAggregatable() {
        List<TzOrderItem> items = createOrderItems(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);

        // 已取消状态不参与聚合
        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(
            items, TzOrderSupplierStatusEnum.CANCELLED);
        assertEquals(TzOrderSupplierStatusEnum.CANCELLED, result);

        // 已入库状态不参与聚合
        result = supplierStatusCalculator.aggregateItemStatus(
            items, TzOrderSupplierStatusEnum.COMPLETED);
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, result);
    }

    @Test
    @DisplayName("全部取消应聚合为供应商订单取消")
    void shouldAggregateToOrderCancelledWhenAllCancelled() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.CANCELLED,
            TzOrderItemStatusEnum.CANCELLED,
            TzOrderItemStatusEnum.CANCELLED);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.CANCELLED, result,
            "所有订单项取消应聚合为供应商订单取消");
    }

    @Test
    @DisplayName("全部入库应聚合为供应商订单入库")
    void shouldAggregateToInStockWhenAllStocked() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, result,
            "所有订单项入库应聚合为供应商订单入库");
    }

    @Test
    @DisplayName("全部已收货应聚合为仓库已收货")
    void shouldAggregateToWarehouseReceivedWhenAllReceived() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.COMPLETED); // 包含部分入库

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED, result,
            "全部订单项已收货应聚合为仓库已收货");
    }

    @Test
    @DisplayName("大部分已发货应聚合为供应商已发货")
    void shouldAggregateToSupplierShippedWhenMostlyShipped() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.SHIPPED, result,
            "大部分订单项已发货应聚合为供应商已发货");
    }

    @Test
    @DisplayName("存在物流中订单项应聚合为仓库待收货")
    void shouldAggregateToWarehousePendingWhenSomeInLogistics() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT, result,
            "存在物流中订单项应聚合为仓库待收货");
    }

    @Test
    @DisplayName("全部采购完成应聚合为采购完成")
    void shouldAggregateToProcurementCompletedWhenAllCompleted() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.PENDING_SHIPMENT, result,
            "全部订单项采购完成应聚合为采购完成");
    }

    @Test
    @DisplayName("存在采购中订单项应聚合为采购中")
    void shouldAggregateToProcurementInProgressWhenSomeInProgress() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.PENDING);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.PENDING_SHIPMENT, result,
            "存在采购中订单项应聚合为采购中");
    }

    @Test
    @DisplayName("全部待处理应聚合为待审核")
    void shouldAggregateToPendingApprovalWhenAllPending() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.PENDING);

        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(items, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.PENDING_PAYMENT, result,
            "全部订单项待处理应聚合为待审核");
    }

    @Test
    @DisplayName("测试完成度计算")
    void shouldCalculateCompletionRate() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,  // 40%
            TzOrderItemStatusEnum.SHIPPED,       // 60%
            TzOrderItemStatusEnum.DELIVERED,     // 80%
            TzOrderItemStatusEnum.COMPLETED,               // 100%
            TzOrderItemStatusEnum.PENDING);   // 0%

        double completionRate = supplierStatusCalculator.calculateCompletionRate(items);

        // 期望完成率: (40 + 60 + 80 + 100 + 0) / 5 = 56%
        assertEquals(56.0, completionRate, 0.1, "完成度计算应该正确");

        // 测试空列表
        double emptyRate = supplierStatusCalculator.calculateCompletionRate(Collections.emptyList());
        assertEquals(0.0, emptyRate, "空列表完成度应为0");
    }

    @Test
    @DisplayName("测试瓶颈分析")
    void shouldAnalyzeBottleneck() {
        // 存在待处理订单项
        List<TzOrderItem> pendingItems = createOrderItems(
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
        String analysis = supplierStatusCalculator.getBottleneckAnalysis(pendingItems);
        assertTrue(analysis.contains("待处理"), "应检测到待处理瓶颈");

        // 大部分采购中
        List<TzOrderItem> mostlyInProgress = createOrderItems(
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.SHIPPED);
        analysis = supplierStatusCalculator.getBottleneckAnalysis(mostlyInProgress);
        assertTrue(analysis.contains("采购中"), "应检测到采购进度瓶颈");

        // 已发货未收货
        List<TzOrderItem> shippedItems = createOrderItems(
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
        analysis = supplierStatusCalculator.getBottleneckAnalysis(shippedItems);
        assertTrue(analysis.contains("物流"), "应检测到物流瓶颈");

        // 正常进展
        List<TzOrderItem> normalItems = createOrderItems(
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.COMPLETED);
        analysis = supplierStatusCalculator.getBottleneckAnalysis(normalItems);
        assertTrue(analysis.contains("正常"), "应检测到正常状态");
    }

    @Test
    @DisplayName("测试聚合结果验证")
    void shouldValidateAggregationResult() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED);

        TzOrderSupplierStatusEnum originalStatus = TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        TzOrderSupplierStatusEnum aggregatedStatus = TzOrderSupplierStatusEnum.COMPLETED;

        // 正常聚合应该通过验证
        boolean isValid = supplierStatusCalculator.validateAggregationResult(
            items, originalStatus, aggregatedStatus);
        assertTrue(isValid, "正常状态聚合应该通过验证");

        // 逆向聚合应该验证失败
        TzOrderSupplierStatusEnum reverseStatus = TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        boolean isValidReverse = supplierStatusCalculator.validateAggregationResult(
            items, originalStatus, reverseStatus);
        assertFalse(isValidReverse, "逆向状态聚合应该验证失败");
    }

    @Test
    @DisplayName("测试状态一致性验证")
    void shouldValidateStatusConsistency() {
        // 全部取消的订单项
        List<TzOrderItem> allCancelledItems = createOrderItems(
            TzOrderItemStatusEnum.CANCELLED,
            TzOrderItemStatusEnum.CANCELLED);

        // 如果所有订单项都取消了，聚合状态必须是取消
        boolean isValid = supplierStatusCalculator.validateAggregationResult(
            allCancelledItems, TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.COMPLETED); // 错误的聚合结果
        assertFalse(isValid, "所有订单项取消时聚合为其他状态应验证失败");

        // 全部入库的订单项
        List<TzOrderItem> allStockedItems = createOrderItems(
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED);

        // 如果所有订单项都入库了，聚合状态必须是入库
        isValid = supplierStatusCalculator.validateAggregationResult(
            allStockedItems, TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT); // 错误的聚合结果
        assertFalse(isValid, "所有订单项入库时聚合为其他状态应验证失败");
    }

    @Test
    @DisplayName("测试聚合描述生成")
    void shouldGenerateAggregationDescription() {
        List<TzOrderItem> items = createOrderItems(
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.CANCELLED);

        String description = supplierStatusCalculator.getAggregationDescription(
            items, TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT);

        assertNotNull(description, "聚合描述不应为空");
        assertTrue(description.contains("总数:4"), "描述应包含总数");
        assertTrue(description.contains("已取消:1"), "描述应包含取消数量");
        assertTrue(description.contains("等待仓库签收"), "描述应包含聚合状态");
    }

    @Test
    @DisplayName("边界条件测试：单个订单项")
    void shouldHandleSingleOrderItem() {
        // 单个进行中订单项
        List<TzOrderItem> singleInProgress = createOrderItems(
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(
            singleInProgress, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.PENDING_PAYMENT, result);

        // 单个完成订单项
        List<TzOrderItem> singleCompleted = createOrderItems(
            TzOrderItemStatusEnum.COMPLETED);
        result = supplierStatusCalculator.aggregateItemStatus(singleCompleted, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, result);

        // 单个取消订单项
        List<TzOrderItem> singleCancelled = createOrderItems(
            TzOrderItemStatusEnum.CANCELLED);
        result = supplierStatusCalculator.aggregateItemStatus(singleCancelled, currentStatus);
        assertEquals(TzOrderSupplierStatusEnum.CANCELLED, result);
    }

    /**
     * 创建测试用的订单项列表
     */
    private List<TzOrderItem> createOrderItems(TzOrderItemStatusEnum... statuses) {
        List<TzOrderItem> items = new ArrayList<>();

        for (int i = 0; i < statuses.length; i++) {
            TzOrderItem item = new TzOrderItem();
            item.setId((long) (i + 1));
            item.setStatus(statuses[i]);
            item.setSupplierOrderId(1L);
            items.add(item);
        }

        return items;
    }
}
