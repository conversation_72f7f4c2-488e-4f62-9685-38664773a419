/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import static org.junit.jupiter.api.Assertions.*;

import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * SupplierOrderStatusChangeEvent 单元测试
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 测试供应商订单状态变更事件的功能和业务逻辑
 * @since 1.0.0
 */
@DisplayName("供应商订单状态变更事件测试")
class SupplierOrderStatusChangeEventTest {

    private SupplierOrderStatusChangeEvent testEvent;
    private SupplierOrderStatusChangeEvent.AggregationContext testAggregationContext;

    @BeforeEach
    void setUp() {
        // 创建测试聚合上下文
        testAggregationContext = new SupplierOrderStatusChangeEvent.AggregationContext(
            2,                    // triggerItemCount
            5,                    // totalItemCount
            "v1.0",              // algorithmVersion
            40.0,                // completionRate
            "测试聚合详情"          // aggregationDetails
        );

        // 创建测试事件
        testEvent = new SupplierOrderStatusChangeEvent(
            2001L,                                                    // supplierOrderId
            3001L,                                                    // purchaseOrderId
            "supplier-001",                                           // supplierId
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,                // originalStatus
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,               // newStatus
            "订单项聚合触发",                                          // changeReason
            LocalDateTime.now(),                                      // changeTime
            "test-event-chain-001",                                   // eventChainId
            SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION, // changeSource
            SupplierOrderStatusChangeEvent.TriggerType.ITEM_AGGREGATION, // triggerType
            "10000",                                                  // tenantId
            "732080147390572",                                        // userId
            testAggregationContext                                    // aggregationContext
        );
    }

    @Test
    @DisplayName("测试事件基本属性")
    void testEventBasicProperties() {
        // 验证所有基本属性
        assertEquals(2001L, testEvent.getSupplierOrderId());
        assertEquals(3001L, testEvent.getPurchaseOrderId());
        assertEquals("supplier-001", testEvent.getSupplierId());
        assertEquals(TzOrderSupplierStatusEnum.PENDING_PAYMENT, testEvent.getOriginalStatus());
        assertEquals(TzOrderSupplierStatusEnum.PENDING_SHIPMENT, testEvent.getNewStatus());
        assertEquals("订单项聚合触发", testEvent.getChangeReason());
        assertNotNull(testEvent.getChangeTime());
        assertEquals("test-event-chain-001", testEvent.getEventChainId());
        assertEquals(SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION, testEvent.getChangeSource());
        assertEquals(SupplierOrderStatusChangeEvent.TriggerType.ITEM_AGGREGATION, testEvent.getTriggerType());
        assertEquals("10000", testEvent.getTenantId());
        assertEquals("732080147390572", testEvent.getUserId());
        assertEquals(testAggregationContext, testEvent.getAggregationContext());
    }

    @Test
    @DisplayName("测试简化构造函数")
    void testSimplifiedConstructor() {
        // Given & When: 使用简化构造函数（无聚合上下文）
        SupplierOrderStatusChangeEvent event = new SupplierOrderStatusChangeEvent(
            2002L,
            3002L,
            "supplier-002",
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,
            TzOrderSupplierStatusEnum.SHIPPED,
            "发货完成",
            "test-event-chain-002",
            SupplierOrderStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572",
            null
        );

        // Then: 验证属性设置正确
        assertEquals(2002L, event.getSupplierOrderId());
        assertEquals(TzOrderSupplierStatusEnum.PENDING_SHIPMENT, event.getOriginalStatus());
        assertEquals(TzOrderSupplierStatusEnum.SHIPPED, event.getNewStatus());
        assertEquals("发货完成", event.getChangeReason());
        assertNotNull(event.getChangeTime());
        assertTrue(event.getChangeTime().isBefore(LocalDateTime.now().plusSeconds(1)));
        assertNull(event.getAggregationContext());
    }

    @Test
    @DisplayName("测试关键状态变更判断")
    void testIsCriticalStatusChange() {
        // 测试完成状态
        SupplierOrderStatusChangeEvent completedEvent = createEventWithNewStatus(TzOrderSupplierStatusEnum.COMPLETED);
        assertTrue(completedEvent.isCriticalStatusChange());

        // 测试取消状态
        SupplierOrderStatusChangeEvent cancelledEvent = createEventWithNewStatus(TzOrderSupplierStatusEnum.CANCELLED);
        assertTrue(cancelledEvent.isCriticalStatusChange());

        // 测试发货状态（关键状态）
        SupplierOrderStatusChangeEvent shippedEvent = createEventWithNewStatus(TzOrderSupplierStatusEnum.SHIPPED);
        assertTrue(shippedEvent.isCriticalStatusChange());

        // 测试仓库已收货状态（非关键状态）
        SupplierOrderStatusChangeEvent warehouseReceivedEvent = createEventWithNewStatus(TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED);
        assertFalse(warehouseReceivedEvent.isCriticalStatusChange());

        // 测试非关键状态
        SupplierOrderStatusChangeEvent pendingEvent = createEventWithNewStatus(TzOrderSupplierStatusEnum.PENDING_PAYMENT);
        assertFalse(pendingEvent.isCriticalStatusChange());

        SupplierOrderStatusChangeEvent pendingShipmentEvent = createEventWithNewStatus(TzOrderSupplierStatusEnum.PENDING_SHIPMENT);
        assertFalse(pendingShipmentEvent.isCriticalStatusChange());
    }

    @Test
    @DisplayName("测试是否触发采购订单聚合判断")
    void testShouldTriggerPurchaseAggregation() {
        // 测试订单项聚合触发
        SupplierOrderStatusChangeEvent itemAggregationEvent = createEventWithSource(SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION);
        assertTrue(itemAggregationEvent.shouldTriggerPurchaseAggregation());

        // 测试系统自动触发
        SupplierOrderStatusChangeEvent systemEvent = createEventWithSource(SupplierOrderStatusChangeEvent.ChangeSource.SYSTEM_AUTO);
        assertTrue(systemEvent.shouldTriggerPurchaseAggregation());

        // 测试外部同步触发
        SupplierOrderStatusChangeEvent externalEvent = createEventWithSource(SupplierOrderStatusChangeEvent.ChangeSource.EXTERNAL_SYNC);
        assertTrue(externalEvent.shouldTriggerPurchaseAggregation());

        // 测试测试场景不触发
        SupplierOrderStatusChangeEvent testEvent = createEventWithSource(SupplierOrderStatusChangeEvent.ChangeSource.TEST_SCENARIO);
        assertFalse(testEvent.shouldTriggerPurchaseAggregation());
    }

    @Test
    @DisplayName("测试是否由订单项聚合触发")
    void testIsTriggeredByItemAggregation() {
        // 测试由订单项聚合触发 - 需要同时设置正确的TriggerType
        SupplierOrderStatusChangeEvent itemAggregationEvent = createEventWithSourceAndTrigger(SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION,
            SupplierOrderStatusChangeEvent.TriggerType.ITEM_AGGREGATION);
        assertTrue(itemAggregationEvent.isTriggeredByItemAggregation());

        // 测试非订单项聚合触发
        SupplierOrderStatusChangeEvent systemEvent = createEventWithSourceAndTrigger(SupplierOrderStatusChangeEvent.ChangeSource.SYSTEM_AUTO, SupplierOrderStatusChangeEvent.TriggerType.DIRECT_CHANGE);
        assertFalse(systemEvent.isTriggeredByItemAggregation());
    }

    @Test
    @DisplayName("测试用户友好描述")
    void testGetUserFriendlyDescription() {
        assertEquals("供应商订单待支付", createEventWithNewStatus(TzOrderSupplierStatusEnum.PENDING_PAYMENT).getUserFriendlyDescription());
        assertEquals("供应商订单待发货", createEventWithNewStatus(TzOrderSupplierStatusEnum.PENDING_SHIPMENT).getUserFriendlyDescription());
        assertEquals("供应商订单已发货", createEventWithNewStatus(TzOrderSupplierStatusEnum.SHIPPED).getUserFriendlyDescription());
        assertEquals("商品运输中，等待仓库签收", createEventWithNewStatus(TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT).getUserFriendlyDescription());
        assertEquals("仓库已签收，正在质检中", createEventWithNewStatus(TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED).getUserFriendlyDescription());
        assertEquals("供应商订单已完成", createEventWithNewStatus(TzOrderSupplierStatusEnum.COMPLETED).getUserFriendlyDescription());
        assertEquals("供应商订单已取消", createEventWithNewStatus(TzOrderSupplierStatusEnum.CANCELLED).getUserFriendlyDescription());
    }

    @Test
    @DisplayName("测试事件ID生成")
    void testGetEventId() {
        // Given: 创建事件
        LocalDateTime specificTime = LocalDateTime.of(2025, 9, 1, 10, 30, 45);
        SupplierOrderStatusChangeEvent event = new SupplierOrderStatusChangeEvent(
            2001L,
            3001L,
            "supplier-001",
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,
            "测试",
            specificTime,
            "test-chain",
            SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION,
            SupplierOrderStatusChangeEvent.TriggerType.ITEM_AGGREGATION,
            "10000",
            "732080147390572",
            null
        );

        // When: 获取事件ID
        String eventId = event.getEventId();

        // Then: 验证事件ID格式
        assertNotNull(eventId);
        assertTrue(eventId.startsWith("SUPPLIER_ORDER_STATUS_CHANGE-2001-PENDING_SHIPMENT-"));
        assertTrue(eventId.contains(String.valueOf(specificTime.toEpochSecond(java.time.ZoneOffset.UTC))));
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // When: 调用toString方法
        String eventString = testEvent.toString();

        // Then: 验证包含关键信息
        assertNotNull(eventString);
        assertTrue(eventString.contains("SupplierOrderStatusChangeEvent"));
        assertTrue(eventString.contains("supplierOrderId=2001"));
        assertTrue(eventString.contains("purchaseOrderId=3001"));
        assertTrue(eventString.contains("supplier-001"));
        assertTrue(eventString.contains("待支付"));
        assertTrue(eventString.contains("待发货"));
        assertTrue(eventString.contains("订单项聚合触发"));
        assertTrue(eventString.contains("订单项聚合"));
        assertTrue(eventString.contains("test-event-chain-001"));
    }

    @Test
    @DisplayName("测试变更来源枚举")
    void testChangeSourceEnum() {
        // 验证所有变更来源枚举
        assertEquals("订单项聚合", SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION.getDescription());
        assertEquals("系统自动", SupplierOrderStatusChangeEvent.ChangeSource.SYSTEM_AUTO.getDescription());
        assertEquals("外部同步", SupplierOrderStatusChangeEvent.ChangeSource.EXTERNAL_SYNC.getDescription());
        assertEquals("定时任务", SupplierOrderStatusChangeEvent.ChangeSource.SCHEDULED_TASK.getDescription());
        assertEquals("API调用", SupplierOrderStatusChangeEvent.ChangeSource.API_CALL.getDescription());
        assertEquals("测试场景", SupplierOrderStatusChangeEvent.ChangeSource.TEST_SCENARIO.getDescription());
        assertEquals("数据修复", SupplierOrderStatusChangeEvent.ChangeSource.DATA_REPAIR.getDescription());
    }

    @Test
    @DisplayName("测试触发类型枚举")
    void testTriggerTypeEnum() {
        // 验证所有触发类型枚举
        assertEquals("直接变更", SupplierOrderStatusChangeEvent.TriggerType.DIRECT_CHANGE.getDescription());
        assertEquals("订单项聚合", SupplierOrderStatusChangeEvent.TriggerType.ITEM_AGGREGATION.getDescription());
        assertEquals("外部事件", SupplierOrderStatusChangeEvent.TriggerType.EXTERNAL_EVENT.getDescription());
        assertEquals("系统规则", SupplierOrderStatusChangeEvent.TriggerType.SYSTEM_RULE.getDescription());
    }

    @Test
    @DisplayName("测试聚合上下文")
    void testAggregationContext() {
        // 验证聚合上下文属性
        assertEquals(2, testAggregationContext.getTriggerItemCount());
        assertEquals(5, testAggregationContext.getTotalItemCount());
        assertEquals("v1.0", testAggregationContext.getAlgorithmVersion());
        assertEquals(40.0, testAggregationContext.getCompletionRate(), 0.01);
        assertEquals("测试聚合详情", testAggregationContext.getAggregationDetails());
    }

    @Test
    @DisplayName("测试聚合上下文构造函数")
    void testAggregationContextConstructor() {
        // Given & When: 创建聚合上下文
        SupplierOrderStatusChangeEvent.AggregationContext context = new SupplierOrderStatusChangeEvent.AggregationContext(
            3,
            10,
            "v2.0",
            75.5,
            "完整测试聚合"
        );

        // Then: 验证属性
        assertEquals(3, context.getTriggerItemCount());
        assertEquals(10, context.getTotalItemCount());
        assertEquals("v2.0", context.getAlgorithmVersion());
        assertEquals(75.5, context.getCompletionRate(), 0.01);
        assertEquals("完整测试聚合", context.getAggregationDetails());
    }

    @Test
    @DisplayName("测试事件完整性")
    void testEventIntegrity() {
        // 验证事件源设置正确
        assertEquals(2001L, testEvent.getSource());

        // 验证时间戳设置
        assertNotNull(testEvent.getTimestamp());
        assertTrue(testEvent.getTimestamp() > 0);

        // 验证事件可以被正常处理
        assertDoesNotThrow(() -> testEvent.toString());
        assertDoesNotThrow(() -> testEvent.getEventId());
        assertDoesNotThrow(() -> testEvent.getUserFriendlyDescription());
    }

    @Test
    @DisplayName("测试边界条件处理")
    void testBoundaryConditions() {
        // 测试null聚合上下文
        SupplierOrderStatusChangeEvent eventWithoutContext = new SupplierOrderStatusChangeEvent(
            2001L, 3001L, "supplier-001",
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,
            "测试",
            "test-chain",
            SupplierOrderStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572",
            null
        );

        assertNull(eventWithoutContext.getAggregationContext());
        assertDoesNotThrow(() -> eventWithoutContext.toString());

        // 测试空字符串参数（构造函数不允许空参数，应该抛出异常）
        assertThrows(IllegalArgumentException.class, () -> new SupplierOrderStatusChangeEvent(
            null, null, "",
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,
            "",
            "",
            SupplierOrderStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "",
            "",
            null
        ));
    }

    @Test
    @DisplayName("测试聚合上下文边界条件")
    void testAggregationContextBoundaryConditions() {
        // 测试负数参数
        assertDoesNotThrow(() -> new SupplierOrderStatusChangeEvent.AggregationContext(
            -1, 0, null, -1.0, null
        ));

        // 测试极大数值
        assertDoesNotThrow(() -> new SupplierOrderStatusChangeEvent.AggregationContext(
            Integer.MAX_VALUE, Integer.MAX_VALUE, "test", Double.MAX_VALUE, "test"
        ));
    }

    // ========== 辅助方法 ==========

    private SupplierOrderStatusChangeEvent createEventWithNewStatus(TzOrderSupplierStatusEnum newStatus) {
        return new SupplierOrderStatusChangeEvent(
            2001L, 3001L, "supplier-001",
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            newStatus,
            "测试状态变更",
            "test-chain",
            SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION,
            "10000",
            "732080147390572",
            null
        );
    }

    private SupplierOrderStatusChangeEvent createEventWithSource(SupplierOrderStatusChangeEvent.ChangeSource source) {
        return new SupplierOrderStatusChangeEvent(
            2001L, 3001L, "supplier-001",
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,
            "测试来源",
            "test-chain",
            source,
            "10000",
            "732080147390572",
            null
        );
    }

    private SupplierOrderStatusChangeEvent createEventWithSourceAndTrigger(SupplierOrderStatusChangeEvent.ChangeSource source, SupplierOrderStatusChangeEvent.TriggerType triggerType) {
        return new SupplierOrderStatusChangeEvent(
            2001L, 3001L, "supplier-001",
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT,
            "测试来源",
            LocalDateTime.now(),
            "test-chain",
            source,
            triggerType,
            "10000",
            "732080147390572",
            null
        );
    }
}