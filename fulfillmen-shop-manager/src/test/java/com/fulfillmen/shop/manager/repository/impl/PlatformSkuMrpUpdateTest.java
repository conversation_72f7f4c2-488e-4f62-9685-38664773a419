/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.tenant.ShopTenantContext;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.manager.TestConfiguration;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * PlatformSku MRP 字段更新测试
 *
 * <pre>
 * 测试目标：
 * 1. 读取 platformsku-nayaskuId.txt 中的 SKU ID 和 MRP 信息
 * 2. 根据 SKU ID (platformSku) 查询对应的 nayaSkuId (TzProductSku.id)
 * 3. 生成 UPDATE SQL 脚本用于更新 GFF_Goods 表的 MRP 字段
 * 4. 生成 CSV 文件包含原始数据、查询结果和更新状态
 * 5. 支持批量查询和性能优化
 * </pre>
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("sealosDev")
class PlatformSkuMrpUpdateTest {

    private static final String INPUT_FILE_PATH = "platformsku-nayaskuId.txt";
    private static final Long TARGET_TENANT_ID = 10000L;
    private static final int BATCH_SIZE = 500;
    private static final int GFF_CUSTOMER_ID = 13100;

    @Autowired
    private TzProductSkuMapper tzProductSkuMapper;

    @BeforeEach
    void setUp() {
        // 设置租户上下文
        ShopTenantContext.setTenantId(TARGET_TENANT_ID.toString());
        log.info("测试环境初始化完成，目标租户ID: {}", TARGET_TENANT_ID);
    }

    @Test
    @DisplayName("根据 platformSku 查询 nayaSkuId 并生成 MRP 更新脚本")
    void testGenerateMrpUpdateScript() {
        log.info("🎯 开始执行 platformSku MRP 更新任务");
        long startTime = System.currentTimeMillis();

        try {
            // 1. 读取输入文件
            List<SkuMrpDataItem> skuDataItems = readInputFile();
            log.info("成功读取输入文件，共 {} 条记录", skuDataItems.size());

            assertNotNull(skuDataItems);
            assertFalse(skuDataItems.isEmpty(), "输入数据不能为空");

            // 2. 批量查询数据库获取 nayaSkuId
            queryNayaSkuIds(skuDataItems);

            // 3. 生成统计信息
            MrpUpdateStatistics statistics = generateStatistics(skuDataItems, startTime);

            // 4. 导出 CSV 文件
            exportToCsvFile(skuDataItems);

            // 5. 生成 MRP UPDATE SQL 脚本
            generateMrpUpdateSqlScript(skuDataItems);

            // 6. 输出统计报告
            printStatisticsReport(statistics);

            // 7. 验证结果
            validateResults(skuDataItems, statistics);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("🎯 ===== platformSku MRP 更新任务完成 =====");
            log.info("总执行时间: {} 秒", String.format("%.2f", totalTime / 1000.0));
            log.info("查询记录总数: {} 条", skuDataItems.size());

        } catch (Exception e) {
            log.error("执行 MRP 更新任务时发生错误", e);
            throw new RuntimeException("MRP 更新任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 读取输入文件数据
     */
    private List<SkuMrpDataItem> readInputFile() throws IOException {
        Path filePath = Paths.get("src/test/resources", INPUT_FILE_PATH);
        log.info("尝试读取输入文件: {}", filePath.toAbsolutePath());

        if (!Files.exists(filePath)) {
            throw new IOException("输入文件不存在: " + filePath.toAbsolutePath());
        }

        List<String> lines = Files.readAllLines(filePath);
        log.info("成功读取文件，行数: {}", lines.size());

        List<SkuMrpDataItem> inputData = new ArrayList<>();

        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i).trim();

            // 跳过空行和表头行
            if (StrUtil.isBlank(line) || line.startsWith("SKU ID")) {
                continue;
            }

            try {
                // 按制表符分割：SKU ID, MRP(Rs.), ASIN
                String[] parts = line.split("\\t");
                if (parts.length >= 3) {
                    SkuMrpDataItem item = SkuMrpDataItem.builder()
                        .skuId(parts[0].trim())
                        .mrp(parts[1].trim())
                        .asin(parts[2].trim())
                        .found(false)
                        .updateReady(false)
                        .build();

                    inputData.add(item);
                    log.debug("解析第{}行成功: SKU={}, MRP={}, ASIN={}",
                        i + 1, item.getSkuId(), item.getMrp(), item.getAsin());
                }
            } catch (Exception e) {
                log.warn("解析第{}行失败: {}, 错误: {}", i + 1, line, e.getMessage());
            }
        }

        log.info("解析完成，有效输入数据数量: {}", inputData.size());
        return inputData;
    }

    /**
     * 批量查询 nayaSkuId
     */
    private void queryNayaSkuIds(List<SkuMrpDataItem> skuDataItems) {
        log.info("开始批量查询 nayaSkuId...");

        // 提取所有的 platformSku
        List<String> platformSkus = skuDataItems.stream()
            .map(SkuMrpDataItem::getSkuId)
            .collect(Collectors.toList());

        // 批量查询（分批处理以避免 SQL IN 子句过长）
        Map<String, Long> platformSkuToNayaSkuIdMap = new HashMap<>();

        for (int i = 0; i < platformSkus.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, platformSkus.size());
            List<String> batchPlatformSkus = platformSkus.subList(i, endIndex);

            List<TzProductSku> skuList = tzProductSkuMapper.selectList(
                new LambdaQueryWrapper<TzProductSku>()
                    .in(TzProductSku::getPlatformSku, batchPlatformSkus)
                    .select(TzProductSku::getId, TzProductSku::getPlatformSku)
            );

            // 构建映射关系
            for (TzProductSku sku : skuList) {
                platformSkuToNayaSkuIdMap.put(sku.getPlatformSku(), sku.getId());
            }

            log.info("已查询第 {}-{} 批数据，查询到 {} 条匹配记录",
                i + 1, endIndex, skuList.size());
        }

        // 更新 skuDataItems
        for (SkuMrpDataItem item : skuDataItems) {
            Long nayaSkuId = platformSkuToNayaSkuIdMap.get(item.getSkuId());
            if (nayaSkuId != null) {
                item.setNayaSkuId(nayaSkuId);
                item.setFound(true);
                // 检查 MRP 是否有效（不为空且为有效数字）
                if (StrUtil.isNotBlank(item.getMrp()) && isValidMrp(item.getMrp())) {
                    item.setUpdateReady(true);
                }
            }
        }

        log.info("批量查询完成，共匹配到 {} 条记录", platformSkuToNayaSkuIdMap.size());
    }

    /**
     * 验证 MRP 是否为有效数值
     */
    private boolean isValidMrp(String mrp) {
        try {
            BigDecimal decimal = new BigDecimal(mrp);
            return decimal.compareTo(BigDecimal.ZERO) >= 0; // MRP 应该大于等于0
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 生成统计信息
     */
    private MrpUpdateStatistics generateStatistics(List<SkuMrpDataItem> skuDataItems, long startTime) {
        long executionTime = System.currentTimeMillis() - startTime;

        int totalRecords = skuDataItems.size();
        int foundRecords = skuDataItems.stream().mapToInt(item -> item.isFound() ? 1 : 0).sum();
        int updateReadyRecords = skuDataItems.stream().mapToInt(item -> item.isUpdateReady() ? 1 : 0).sum();
        int notFoundRecords = totalRecords - foundRecords;
        double matchSuccessRate = totalRecords > 0 ? (double) foundRecords / totalRecords * 100 : 0;
        double updateReadyRate = foundRecords > 0 ? (double) updateReadyRecords / foundRecords * 100 : 0;

        List<String> notFoundSkuIds = skuDataItems.stream()
            .filter(item -> !item.isFound())
            .map(SkuMrpDataItem::getSkuId)
            .collect(Collectors.toList());

        List<String> invalidMrpSkuIds = skuDataItems.stream()
            .filter(item -> item.isFound() && !item.isUpdateReady())
            .map(SkuMrpDataItem::getSkuId)
            .collect(Collectors.toList());

        return MrpUpdateStatistics.builder()
            .totalRecords(totalRecords)
            .foundRecords(foundRecords)
            .updateReadyRecords(updateReadyRecords)
            .notFoundRecords(notFoundRecords)
            .matchSuccessRate(matchSuccessRate)
            .updateReadyRate(updateReadyRate)
            .executionTimeMs(executionTime)
            .notFoundSkuIds(notFoundSkuIds)
            .invalidMrpSkuIds(invalidMrpSkuIds)
            .build();
    }

    /**
     * 导出 CSV 文件
     */
    private void exportToCsvFile(List<SkuMrpDataItem> skuDataItems) throws IOException {
        if (CollectionUtil.isEmpty(skuDataItems)) {
            log.warn("没有数据需要导出到CSV");
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "platformsku_mrp_update_export_" + timestamp + ".csv";
        Path outputPath = Paths.get("src/test/resources", fileName);

        log.info("开始导出 CSV 数据到文件: {}", outputPath.toAbsolutePath());

        try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8))) {
            // 写入 CSV 头部
            writer.println("\"SKU ID\",\"MRP(Rs.)\",\"ASIN\",\"nayaSkuId\",\"匹配状态\",\"更新状态\",\"备注\"");

            // 写入数据行
            for (SkuMrpDataItem item : skuDataItems) {
                String nayaSkuIdStr = item.isFound() ? String.valueOf(item.getNayaSkuId()) : "NOT_FOUND";
                String matchStatus = item.isFound() ? "匹配成功" : "未匹配";
                String updateStatus = item.isUpdateReady() ? "可更新" : "不可更新";
                String remark = "";

                if (!item.isFound()) {
                    remark = "未找到对应的 platformSku";
                } else if (!item.isUpdateReady()) {
                    remark = "MRP 值无效: " + item.getMrp();
                } else {
                    remark = "准备更新";
                }

                writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"%n",
                    escapeCsvField(item.getSkuId()),
                    escapeCsvField(item.getMrp()),
                    escapeCsvField(item.getAsin()),
                    escapeCsvField(nayaSkuIdStr),
                    escapeCsvField(matchStatus),
                    escapeCsvField(updateStatus),
                    escapeCsvField(remark));
            }
        }

        log.info("CSV 数据导出完成，文件: {}, 记录数: {}", outputPath.toAbsolutePath(), skuDataItems.size());
    }

    /**
     * 生成 MRP UPDATE SQL 脚本
     */
    private void generateMrpUpdateSqlScript(List<SkuMrpDataItem> skuDataItems) throws IOException {
        if (CollectionUtil.isEmpty(skuDataItems)) {
            log.warn("没有数据需要生成 MRP 更新 SQL 脚本");
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "update_gff_goods_mrp_script_" + timestamp + ".sql";
        Path outputPath = Paths.get("src/test/resources", fileName);

        log.info("开始生成 MRP UPDATE SQL 脚本: {}", outputPath.toAbsolutePath());

        try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8))) {
            // 写入脚本头部信息
            writer.println("-- GFF_Goods 表 MRP 字段更新脚本");
            writer.println("-- 生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            writer.println("-- 说明: 根据 platformsku-nayaskuId.txt 生成的 MRP 批量更新脚本");
            writer.println("-- 更新字段: MRP = 新MRP值");
            writer.printf("-- 更新条件: GFF_CustomerID = %d AND SKU = nayaSkuId%n", GFF_CUSTOMER_ID);
            writer.println();

            // 只为可更新的记录生成 SQL
            List<SkuMrpDataItem> updateReadyItems = skuDataItems.stream()
                .filter(SkuMrpDataItem::isUpdateReady)
                .toList();

            if (updateReadyItems.isEmpty()) {
                writer.println("-- 警告: 没有找到任何可更新的记录，无法生成更新脚本");
                log.warn("没有找到可更新的记录，MRP SQL脚本为空");
                return;
            }

            writer.println("-- 开始事务");
            writer.println("BEGIN TRANSACTION;");
            writer.println();

            // 生成 UPDATE 语句
            int updateCount = 0;
            for (SkuMrpDataItem item : updateReadyItems) {
                writer.printf("UPDATE GFF_Goods SET MRP = '%s' WHERE GFF_CustomerID = %d AND SKU = '%s';%n",
                    escapeSqlString(item.getMrp()),
                    GFF_CUSTOMER_ID,
                    item.getNayaSkuId());
                updateCount++;

                // 每100条语句添加一个注释
                if (updateCount % 100 == 0) {
                    writer.printf("-- 已处理 %d 条 MRP 更新记录%n", updateCount);
                    writer.println();
                }
            }

            writer.println();
            writer.printf("-- 总共生成 %d 条 MRP UPDATE 语句%n", updateCount);
            writer.println();
            writer.println("-- 提交事务（请在确认无误后取消注释）");
            writer.println("-- COMMIT;");
            writer.println();
            writer.println("-- 回滚事务（如果需要撤销更改）");
            writer.println("-- ROLLBACK;");
        }

        log.info("MRP UPDATE SQL 脚本生成完成: {}, 生成 {} 条更新语句",
            outputPath.toAbsolutePath(),
            skuDataItems.stream().filter(SkuMrpDataItem::isUpdateReady).count());
    }

    /**
     * 输出统计报告
     */
    private void printStatisticsReport(MrpUpdateStatistics statistics) {
        log.info("📊 ==================== MRP 更新统计报告 ====================");
        log.info("总记录数: {}", statistics.getTotalRecords());
        log.info("找到匹配记录数: {}", statistics.getFoundRecords());
        log.info("可更新记录数: {}", statistics.getUpdateReadyRecords());
        log.info("未找到匹配记录数: {}", statistics.getNotFoundRecords());
        log.info("匹配成功率: {}%", String.format("%.2f", statistics.getMatchSuccessRate()));
        log.info("更新准备率: {}%", String.format("%.2f", statistics.getUpdateReadyRate()));
        log.info("执行时间: {} ms ({} 秒)",
            statistics.getExecutionTimeMs(),
            String.format("%.2f", statistics.getExecutionTimeMs() / 1000.0));

        if (!statistics.getNotFoundSkuIds().isEmpty()) {
            log.info("未找到匹配的前10个 SKU ID:");
            statistics.getNotFoundSkuIds().stream()
                .limit(10)
                .forEach(skuId -> log.info("  - {}", skuId));

            if (statistics.getNotFoundSkuIds().size() > 10) {
                log.info("  ... 还有 {} 个未匹配的 SKU ID",
                    statistics.getNotFoundSkuIds().size() - 10);
            }
        }

        if (!statistics.getInvalidMrpSkuIds().isEmpty()) {
            log.info("MRP 值无效的前10个 SKU ID:");
            statistics.getInvalidMrpSkuIds().stream()
                .limit(10)
                .forEach(skuId -> log.info("  - {}", skuId));

            if (statistics.getInvalidMrpSkuIds().size() > 10) {
                log.info("  ... 还有 {} 个 MRP 值无效的 SKU ID",
                    statistics.getInvalidMrpSkuIds().size() - 10);
            }
        }
        log.info("============================================================");
    }

    /**
     * CSV 字段转义
     */
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 替换双引号为两个双引号
        return field.replace("\"", "\"\"");
    }

    /**
     * SQL 字符串转义
     */
    private String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        // 替换单引号为两个单引号，防止 SQL 注入
        return value.replace("'", "''");
    }

    /**
     * 验证结果
     */
    private void validateResults(List<SkuMrpDataItem> inputData, MrpUpdateStatistics statistics) {
        log.info("开始验证 MRP 更新查询结果");

        // 基本验证
        assertNotNull(inputData);
        assertNotNull(statistics);
        assertFalse(inputData.isEmpty(), "输入数据不能为空");
        assertTrue(statistics.getTotalRecords() > 0, "应该有查询记录");

        // 验证数据一致性
        assertTrue(statistics.getTotalRecords() == inputData.size(),
            "统计记录数应该等于输入数据数");

        int actualFoundCount = (int) inputData.stream().filter(SkuMrpDataItem::isFound).count();
        assertTrue(statistics.getFoundRecords() == actualFoundCount,
            "统计找到记录数应该与实际一致");

        int actualUpdateReadyCount = (int) inputData.stream().filter(SkuMrpDataItem::isUpdateReady).count();
        assertTrue(statistics.getUpdateReadyRecords() == actualUpdateReadyCount,
            "统计可更新记录数应该与实际一致");

        log.info("数据验证完成，总记录数: {}, 匹配成功数: {}, 可更新数: {}",
            statistics.getTotalRecords(), statistics.getFoundRecords(), statistics.getUpdateReadyRecords());
    }

    /**
     * SKU MRP 数据项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuMrpDataItem {

        private String skuId;           // 原始 SKU ID (platformSku)
        private String mrp;             // MRP 价格
        private String asin;            // ASIN 码
        private Long nayaSkuId;         // 查询到的 nayaSkuId (TzProductSku.id)
        private boolean found;          // 是否找到匹配记录
        private boolean updateReady;    // 是否准备更新（找到匹配且MRP有效）

        @Override
        public String toString() {
            return String.format("SkuMrpDataItem{skuId='%s', mrp='%s', asin='%s', nayaSkuId=%s, found=%s, updateReady=%s}",
                skuId, mrp, asin, nayaSkuId, found, updateReady);
        }
    }

    /**
     * MRP 更新结果统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MrpUpdateStatistics {

        private int totalRecords;           // 总记录数
        private int foundRecords;           // 找到匹配的记录数
        private int updateReadyRecords;     // 可更新的记录数
        private int notFoundRecords;        // 未找到匹配的记录数
        private double matchSuccessRate;    // 匹配成功率
        private double updateReadyRate;     // 更新准备率
        private long executionTimeMs;       // 执行时间（毫秒）
        private List<String> notFoundSkuIds;    // 未找到的SKU ID列表
        private List<String> invalidMrpSkuIds;  // MRP值无效的SKU ID列表
    }
}
