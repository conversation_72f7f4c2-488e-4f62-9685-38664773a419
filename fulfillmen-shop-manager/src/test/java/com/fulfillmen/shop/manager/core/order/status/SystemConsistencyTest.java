/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.order.OrderEventPublisher;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEventListener;
import com.fulfillmen.shop.manager.core.order.event.SupplierOrderStatusChangeEventListener;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

/**
 * 系统一致性测试
 *
 * <pre>
 * 验证订单状态流转系统的数据一致性：
 * 1. 三层状态聚合的一致性验证
 * 2. 并发场景下的数据一致性
 * 3. 异常场景下的状态恢复
 * 4. 长时间运行的稳定性测试
 * 5. 内存泄漏和性能监控
 * 6. 业务规则一致性验证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 系统一致性和稳定性验证测试
 * @since 1.0.0
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("系统一致性测试")
class SystemConsistencyTest {

    @Mock
    private TzOrderItemMapper orderItemMapper;
    @Mock
    private TzOrderSupplierMapper supplierOrderMapper;
    @Mock
    private TzOrderPurchaseMapper purchaseOrderMapper;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    private SupplierStatusCalculator supplierStatusCalculator;
    private OrderStatusAggregator orderStatusAggregator;
    private OrderEventPublisher orderEventPublisher;
    private OrderItemStatusChangeEventListener itemEventListener;
    private SupplierOrderStatusChangeEventListener supplierEventListener;

    // 一致性检查数据结构
    private ConsistencyTracker consistencyTracker;

    @BeforeEach
    void setUp() {
        // 初始化核心组件
        supplierStatusCalculator = new SupplierStatusCalculator();
        orderStatusAggregator = new OrderStatusAggregator();
        orderEventPublisher = new OrderEventPublisher(applicationEventPublisher);

        itemEventListener = new OrderItemStatusChangeEventListener(
            orderItemMapper, supplierOrderMapper, supplierStatusCalculator, orderEventPublisher);

        supplierEventListener = new SupplierOrderStatusChangeEventListener(
            supplierOrderMapper, purchaseOrderMapper, orderStatusAggregator, orderEventPublisher);

        // 初始化一致性跟踪器
        consistencyTracker = new ConsistencyTracker();
    }

    @Test
    @DisplayName("一致性测试：三层状态聚合算法一致性验证")
    void shouldMaintainConsistencyInThreeLayerAggregation() {
        log.info("开始三层状态聚合算法一致性验证");

        // 测试场景1：所有订单项完成 → 供应商订单应该完成
        List<TzOrderItem> allCompletedItems = Arrays.asList(
            createOrderItem(1L, 1L, TzOrderItemStatusEnum.COMPLETED),
            createOrderItem(2L, 1L, TzOrderItemStatusEnum.COMPLETED),
            createOrderItem(3L, 1L, TzOrderItemStatusEnum.COMPLETED)
        );

        TzOrderSupplierStatusEnum supplierStatus = supplierStatusCalculator.aggregateItemStatus(allCompletedItems, TzOrderSupplierStatusEnum.PENDING_PAYMENT);
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, supplierStatus, "所有订单项完成时，供应商订单应该完成");

        // 测试场景2：部分订单项完成 → 供应商订单应该是中间状态
        List<TzOrderItem> mixedItems = Arrays.asList(
            createOrderItem(1L, 1L, TzOrderItemStatusEnum.COMPLETED),
            createOrderItem(2L, 1L, TzOrderItemStatusEnum.SHIPPED),
            createOrderItem(3L, 1L, TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS)
        );

        TzOrderSupplierStatusEnum mixedStatus = supplierStatusCalculator.aggregateItemStatus(mixedItems, TzOrderSupplierStatusEnum.PENDING_PAYMENT);
        assertTrue(mixedStatus != TzOrderSupplierStatusEnum.COMPLETED, "部分完成时不应该是完成状态");
        assertTrue(mixedStatus != TzOrderSupplierStatusEnum.PENDING_PAYMENT, "部分完成时不应该是待支付状态");

        // 测试场景3：采购订单聚合一致性
        List<TzOrderSupplier> supplierOrders = Arrays.asList(
            createSupplierOrder(1L, 1L, "supplier1", TzOrderSupplierStatusEnum.COMPLETED),
            createSupplierOrder(2L, 1L, "supplier2", TzOrderSupplierStatusEnum.COMPLETED)
        );

        TzOrderPurchaseStatusEnum purchaseStatus = orderStatusAggregator.aggregateSupplierStatus(supplierOrders, TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS);
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, purchaseStatus, "所有供应商订单完成时，采购订单应该完成");

        log.info("✅ 三层状态聚合算法一致性验证通过");
    }

    @Test
    @DisplayName("一致性测试：高并发状态变更的数据一致性")
    void shouldMaintainConsistencyUnderHighConcurrency() throws Exception {
        log.info("开始高并发状态变更的数据一致性测试");

        int concurrentThreads = 20;
        int operationsPerThread = 10;
        ExecutorService executor = Executors.newFixedThreadPool(concurrentThreads);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(concurrentThreads);

        // 设置并发测试的Mock数据
        setupConcurrencyTestData();

        List<CompletableFuture<ConsistencyResult>> futures = new ArrayList<>();

        // 创建并发任务
        for (int threadId = 0; threadId < concurrentThreads; threadId++) {
            final int currentThreadId = threadId;

            CompletableFuture<ConsistencyResult> future = CompletableFuture.supplyAsync(() -> {
                ConsistencyResult result = new ConsistencyResult();
                result.setThreadId(currentThreadId);

                try {
                    // 等待统一开始
                    startLatch.await();

                    // 执行多个状态变更操作
                    for (int op = 0; op < operationsPerThread; op++) {
                        String eventChainId = String.format("consistency-thread%d-op%d-%d",
                            currentThreadId, op, System.currentTimeMillis());

                        try {
                            simulateStatusChangeOperation(eventChainId, currentThreadId, op);
                            result.incrementSuccess();
                        } catch (Exception e) {
                            result.incrementFailure();
                            result.addError(e);
                        }

                        // 短暂休息，增加竞争条件
                        Thread.sleep(1);
                    }

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    result.addError(e);
                } finally {
                    finishLatch.countDown();
                }

                return result;
            }, executor);

            futures.add(future);
        }

        // 启动所有并发任务
        startLatch.countDown();

        // 等待所有任务完成
        boolean finished = finishLatch.await(30, TimeUnit.SECONDS);
        assertTrue(finished, "所有并发任务应该在超时时间内完成");

        // 收集并发测试结果
        List<ConsistencyResult> results = new ArrayList<>();
        for (CompletableFuture<ConsistencyResult> future : futures) {
            results.add(future.get(1, TimeUnit.SECONDS));
        }

        // 验证一致性结果
        validateConcurrencyResults(results);

        executor.shutdown();
        log.info("✅ 高并发状态变更的数据一致性测试通过");
    }

    @Test
    @DisplayName("一致性测试：长时间稳定性和内存泄漏检测")
    void shouldMaintainStabilityInLongRunning() throws Exception {
        log.info("开始长时间稳定性和内存泄漏检测");

        // 设置长期运行测试数据
        setupLongRunningTestData();

        int totalOperations = 1000;
        long startTime = System.currentTimeMillis();
        Runtime runtime = Runtime.getRuntime();

        // 记录初始内存状态
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        log.info("初始内存使用: {} MB", initialMemory / 1024 / 1024);

        // 执行大量状态变更操作
        for (int i = 0; i < totalOperations; i++) {
            String eventChainId = "stability-test-" + i + "-" + System.currentTimeMillis();

            // 模拟不同类型的状态变更
            simulateVariousStatusChanges(eventChainId, i);

            // 每100次操作检查一次内存
            if (i > 0 && i % 100 == 0) {
                System.gc(); // 建议垃圾回收
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                long memoryIncrease = currentMemory - initialMemory;

                log.info("操作 {}: 内存使用: {} MB, 增长: {} MB",
                    i, currentMemory / 1024 / 1024, memoryIncrease / 1024 / 1024);

                // 检查内存增长是否合理（不超过100MB）
                assertTrue(memoryIncrease < 100 * 1024 * 1024,
                    String.format("内存增长过快: %d MB at operation %d", memoryIncrease / 1024 / 1024, i));
            }
        }

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 最终内存检查
        System.gc();
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long totalMemoryIncrease = finalMemory - initialMemory;

        log.info("稳定性测试完成:");
        log.info("- 总操作数: {}", totalOperations);
        log.info("- 执行时间: {} ms", executionTime);
        log.info("- 平均操作时间: {} ms", executionTime / (double) totalOperations);
        log.info("- 总内存增长: {} MB", totalMemoryIncrease / 1024 / 1024);

        // 验证性能和内存指标
        assertTrue(executionTime / (double) totalOperations < 10, "平均操作时间应该小于10ms");
        assertTrue(totalMemoryIncrease < 200 * 1024 * 1024, "总内存增长应该小于200MB");

        log.info("✅ 长时间稳定性和内存泄漏检测通过");
    }

    @Test
    @DisplayName("一致性测试：业务规则一致性验证")
    void shouldMaintainBusinessRuleConsistency() {
        log.info("开始业务规则一致性验证");

        // 测试业务规则1：订单状态流转的单向性
        validateStatusFlowUnidirectionality();

        // 测试业务规则2：状态转换的有效性
        validateStatusTransitionValidity();

        // 测试业务规则3：聚合状态的逻辑一致性
        validateAggregationLogicConsistency();

        // 测试业务规则4：异常状态的处理一致性
        validateExceptionStatusHandlingConsistency();

        log.info("✅ 业务规则一致性验证通过");
    }

    @Test
    @DisplayName("一致性测试：数据完整性和约束验证")
    void shouldMaintainDataIntegrityAndConstraints() {
        log.info("开始数据完整性和约束验证");

        // 验证必填字段约束
        validateRequiredFieldConstraints();

        // 验证外键关系一致性
        validateForeignKeyConsistency();

        // 验证枚举值有效性
        validateEnumValueConsistency();

        // 验证时间戳一致性
        validateTimestampConsistency();

        log.info("✅ 数据完整性和约束验证通过");
    }

    // ==================== 辅助方法 ====================

    private void setupConcurrencyTestData() {
        // 为并发测试设置Mock数据 - 使用待处理状态确保有状态变化
        TzOrderSupplier mockSupplierOrder = createSupplierOrder(1L, 1L, "supplier1", TzOrderSupplierStatusEnum.PENDING_PAYMENT);
        TzOrderItem mockOrderItem = createOrderItem(1L, 1L, TzOrderItemStatusEnum.PENDING);
        TzOrderPurchase mockPurchaseOrder = createPurchaseOrder(1L, TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS);

        when(supplierOrderMapper.selectById(anyLong())).thenReturn(mockSupplierOrder);
        when(orderItemMapper.selectList(any())).thenReturn(Arrays.asList(mockOrderItem));
        // 移除不必要的Mock stubbing 以避免UnnecessaryStubbingException
        // when(supplierOrderMapper.selectList(any())).thenReturn(Arrays.asList(mockSupplierOrder));
        // when(purchaseOrderMapper.selectById(anyLong())).thenReturn(mockPurchaseOrder);
    }

    private void setupLongRunningTestData() {
        // 为长期运行测试设置Mock数据
        setupConcurrencyTestData(); // 复用并发测试数据
    }

    private void simulateStatusChangeOperation(String eventChainId, int threadId, int opId) {
        OrderItemStatusChangeEvent event = new OrderItemStatusChangeEvent(
            (long) (threadId + 1), 1L, 1L,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.SHIPPED,
            String.format("并发测试-线程%d-操作%d", threadId, opId),
            LocalDateTime.now(),
            eventChainId,
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000", "732080147390572"
        );

        itemEventListener.handleOrderItemStatusChangeEvent(event);
        consistencyTracker.recordOperation(eventChainId, threadId, opId);
    }

    private void simulateVariousStatusChanges(String eventChainId, int operationId) {
        // 根据操作ID模拟不同类型的状态变更
        TzOrderItemStatusEnum fromStatus;
        TzOrderItemStatusEnum toStatus;

        int scenarioType = operationId % 4;
        switch (scenarioType) {
            case 0:
                fromStatus = TzOrderItemStatusEnum.PENDING;
                toStatus = TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS;
                break;
            case 1:
                fromStatus = TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS;
                toStatus = TzOrderItemStatusEnum.SHIPPED;
                break;
            case 2:
                fromStatus = TzOrderItemStatusEnum.SHIPPED;
                toStatus = TzOrderItemStatusEnum.DELIVERED;
                break;
            default:
                fromStatus = TzOrderItemStatusEnum.DELIVERED;
                toStatus = TzOrderItemStatusEnum.COMPLETED;
                break;
        }

        OrderItemStatusChangeEvent event = new OrderItemStatusChangeEvent(
            1L, 1L, 1L, fromStatus, toStatus,
            "稳定性测试-" + operationId, LocalDateTime.now(), eventChainId,
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO, "10000", "732080147390572"
        );

        itemEventListener.handleOrderItemStatusChangeEvent(event);
    }

    private void validateConcurrencyResults(List<ConsistencyResult> results) {
        int totalSuccess = results.stream().mapToInt(ConsistencyResult::getSuccessCount).sum();
        int totalFailure = results.stream().mapToInt(ConsistencyResult::getFailureCount).sum();
        int totalOperations = totalSuccess + totalFailure;

        log.info("并发测试结果统计:");
        log.info("- 总操作数: {}", totalOperations);
        log.info("- 成功操作: {}", totalSuccess);
        log.info("- 失败操作: {}", totalFailure);
        log.info("- 成功率: {}%", (totalSuccess * 100.0) / totalOperations);

        // 验证成功率应该很高（允许少量失败，因为可能有竞争条件）
        assertTrue(totalSuccess > totalOperations * 0.95, "成功率应该大于95%");

        // 验证没有线程未完成任务
        results.forEach(result -> {
            assertTrue(result.getSuccessCount() + result.getFailureCount() > 0,
                "每个线程都应该完成一些操作");
        });
    }

    private void validateStatusFlowUnidirectionality() {
        // 验证状态流转不能逆向进行
        TzOrderItemStatusEnum[] validFlow = {
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.COMPLETED
        };

        for (int i = 0; i < validFlow.length - 1; i++) {
            for (int j = i + 1; j < validFlow.length; j++) {
                // 正向转换应该是有效的
                assertTrue(isValidStatusTransition(validFlow[i], validFlow[j]),
                    String.format("%s -> %s 应该是有效的转换", validFlow[i], validFlow[j]));

                // 逆向转换应该是无效的（除了取消场景）
                if (validFlow[j] != TzOrderItemStatusEnum.CANCELLED) {
                    assertFalse(isValidStatusTransition(validFlow[j], validFlow[i]),
                        String.format("%s -> %s 不应该是有效的逆向转换", validFlow[j], validFlow[i]));
                }
            }
        }
    }

    private void validateStatusTransitionValidity() {
        // 验证逆向转换是无效的（除了取消和失败状态）
        assertFalse(isValidStatusTransition(TzOrderItemStatusEnum.SHIPPED, TzOrderItemStatusEnum.PENDING));
        assertFalse(isValidStatusTransition(TzOrderItemStatusEnum.DELIVERED, TzOrderItemStatusEnum.SHIPPED));

        // 验证有效的状态转换（包括跳跃转换）
        assertTrue(isValidStatusTransition(TzOrderItemStatusEnum.PENDING, TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS));
        assertTrue(isValidStatusTransition(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS, TzOrderItemStatusEnum.SHIPPED));
        assertTrue(isValidStatusTransition(TzOrderItemStatusEnum.PENDING, TzOrderItemStatusEnum.SHIPPED)); // 允许跳跃
    }

    private void validateAggregationLogicConsistency() {
        // 验证聚合逻辑的一致性
        List<TzOrderItem> consistentItems = Arrays.asList(
            createOrderItem(1L, 1L, TzOrderItemStatusEnum.COMPLETED),
            createOrderItem(2L, 1L, TzOrderItemStatusEnum.COMPLETED)
        );

        TzOrderSupplierStatusEnum currentStatus = TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
        TzOrderSupplierStatusEnum result1 = supplierStatusCalculator.aggregateItemStatus(consistentItems, currentStatus);
        TzOrderSupplierStatusEnum result2 = supplierStatusCalculator.aggregateItemStatus(consistentItems, currentStatus);

        assertEquals(result1, result2, "相同输入应该产生相同的聚合结果");
    }

    private void validateExceptionStatusHandlingConsistency() {
        // 验证异常状态处理的一致性
        List<TzOrderItem> emptyItems = new ArrayList<>();
        TzOrderSupplierStatusEnum currentStatus = TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
        TzOrderSupplierStatusEnum emptyResult = supplierStatusCalculator.aggregateItemStatus(emptyItems, currentStatus);
        assertNotNull(emptyResult, "空列表应该返回默认状态而不是null");

        List<TzOrderItem> nullItems = null;
        try {
            TzOrderSupplierStatusEnum nullResult = supplierStatusCalculator.aggregateItemStatus(nullItems, currentStatus);
            // 如果没有抛异常，结果应该是合理的默认值
            assertNotNull(nullResult, "null输入应该返回默认状态或抛出异常");
        } catch (Exception e) {
            // 抛出异常也是可接受的行为
            log.info("null输入正确抛出异常: {}", e.getMessage());
        }
    }

    private void validateRequiredFieldConstraints() {
        // 这里可以添加必填字段验证逻辑
        assertNotNull(supplierStatusCalculator, "核心组件不能为null");
        assertNotNull(orderStatusAggregator, "核心组件不能为null");
    }

    private void validateForeignKeyConsistency() {
        // 这里可以添加外键关系验证逻辑
        TzOrderItem item = createOrderItem(1L, 1L, TzOrderItemStatusEnum.COMPLETED);
        assertNotNull(item.getSupplierOrderId(), "订单项必须关联供应商订单");
    }

    private void validateEnumValueConsistency() {
        // 验证枚举值的有效性
        for (TzOrderItemStatusEnum status : TzOrderItemStatusEnum.values()) {
            assertNotNull(status.getDesc(), "状态枚举应该有描述");
            assertFalse(status.getDesc().isEmpty(), "状态描述不能为空");
        }
    }

    private void validateTimestampConsistency() {
        // 验证时间戳的一致性
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime earlier = now.minusMinutes(1);

        assertTrue(earlier.isBefore(now), "较早的时间应该在较晚的时间之前");
    }

    private boolean isValidStatusTransition(TzOrderItemStatusEnum from, TzOrderItemStatusEnum to) {
        // 基于业务规则的状态转换验证逻辑
        if (from == to)
            return true;

        // 允许从任何状态转换到取消状态
        if (to == TzOrderItemStatusEnum.CANCELLED)
            return true;

        // 允许从任何状态转换到失败状态
        if (to == TzOrderItemStatusEnum.FAILED)
            return true;

        // 允许正向转换（包括跳跃转换），但不允许逆向转换
        // 这符合业务场景：有时候需要跳过中间状态
        return from.getCode() < to.getCode();
    }

    // 创建测试实体的辅助方法
    private TzOrderItem createOrderItem(Long id, Long supplierOrderId, TzOrderItemStatusEnum status) {
        TzOrderItem item = new TzOrderItem();
        item.setId(id);
        item.setSupplierOrderId(supplierOrderId);
        item.setStatus(status);
        return item;
    }

    private TzOrderSupplier createSupplierOrder(Long id, Long purchaseOrderId, String supplierId, TzOrderSupplierStatusEnum status) {
        TzOrderSupplier order = new TzOrderSupplier();
        order.setId(id);
        order.setPurchaseOrderId(purchaseOrderId);
        order.setSupplierId(supplierId);
        order.setStatus(status);
        return order;
    }

    private TzOrderPurchase createPurchaseOrder(Long id, TzOrderPurchaseStatusEnum status) {
        TzOrderPurchase order = new TzOrderPurchase();
        order.setId(id);
        order.setOrderStatus(status);
        order.setTenantId(10000L);
        return order;
    }

    // ==================== 内部数据结构 ====================

    @Data
    private static class ConsistencyTracker {

        private Map<String, OperationRecord> operations = new HashMap<>();
        private long totalOperations = 0;

        public void recordOperation(String eventChainId, int threadId, int opId) {
            OperationRecord record = new OperationRecord();
            record.setEventChainId(eventChainId);
            record.setThreadId(threadId);
            record.setOperationId(opId);
            record.setTimestamp(System.currentTimeMillis());

            operations.put(eventChainId, record);
            totalOperations++;
        }

        @Data
        public static class OperationRecord {

            private String eventChainId;
            private int threadId;
            private int operationId;
            private long timestamp;
        }
    }

    @Data
    private static class ConsistencyResult {

        private int threadId;
        private int successCount = 0;
        private int failureCount = 0;
        private List<Exception> errors = new ArrayList<>();

        public void incrementSuccess() {
            successCount++;
        }

        public void incrementFailure() {
            failureCount++;
        }

        public void addError(Exception e) {
            errors.add(e);
        }
    }
}