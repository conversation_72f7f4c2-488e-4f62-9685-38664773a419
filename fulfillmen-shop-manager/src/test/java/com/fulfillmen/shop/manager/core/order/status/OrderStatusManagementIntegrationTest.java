/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.order.OrderEventPublisher;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.OrderItemStatusChangeEventListener;
import com.fulfillmen.shop.manager.core.order.event.SupplierOrderStatusChangeEvent;
import com.fulfillmen.shop.manager.core.order.event.SupplierOrderStatusChangeEventListener;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 订单状态管理系统集成测试
 *
 * <pre>
 * 验证三层状态聚合系统的端到端集成：
 * 1. 订单项状态变更 → 供应商订单状态聚合
 * 2. 供应商订单状态变更 → 采购订单状态聚合
 * 3. 事件链传播和防循环机制
 * 4. 状态聚合算法的正确性
 * 5. 事件监听器的协作机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 订单状态管理系统集成测试
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("订单状态管理系统集成测试")
class OrderStatusManagementIntegrationTest {

    @Mock
    private OrderEventPublisher orderEventPublisher;

    private SupplierStatusCalculator supplierStatusCalculator;
    private OrderStatusAggregator orderStatusAggregator;
    private OrderItemStatusChangeEventListener orderItemStatusChangeEventListener;
    private SupplierOrderStatusChangeEventListener supplierOrderStatusChangeEventListener;

    // 测试数据
    private TzOrderItem testOrderItem1;
    private TzOrderItem testOrderItem2;
    private TzOrderSupplier testSupplierOrder;
    private List<TzOrderSupplier> testSupplierOrders;

    @BeforeEach
    void setUp() {
        // 初始化状态聚合器
        supplierStatusCalculator = new SupplierStatusCalculator();
        orderStatusAggregator = new OrderStatusAggregator();

        // 注意：这里不初始化事件监听器，因为它们依赖于真实的Mapper
        // 实际使用中，这些监听器应该在Spring容器中自动装配

        // 创建测试数据
        setupTestData();
    }

    private void setupTestData() {
        // 创建测试订单项1
        testOrderItem1 = new TzOrderItem();
        testOrderItem1.setId(1001L);
        testOrderItem1.setSupplierOrderId(2001L);
        testOrderItem1.setStatus(TzOrderItemStatusEnum.PENDING);

        // 创建测试订单项2
        testOrderItem2 = new TzOrderItem();
        testOrderItem2.setId(1002L);
        testOrderItem2.setSupplierOrderId(2001L);
        testOrderItem2.setStatus(TzOrderItemStatusEnum.PENDING);

        // 创建测试供应商订单
        testSupplierOrder = new TzOrderSupplier();
        testSupplierOrder.setId(2001L);
        testSupplierOrder.setPurchaseOrderId(3001L);
        testSupplierOrder.setSupplierId("supplier-001");
        testSupplierOrder.setStatus(TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        // 创建供应商订单列表
        testSupplierOrders = List.of(testSupplierOrder);
    }

    @Test
    @DisplayName("测试单个订单项状态变更触发供应商订单聚合")
    void testSingleOrderItemStatusChangeTriggersSupplierAggregation() {
        // Given: 两个订单项，一个从PENDING变为PROCUREMENT_IN_PROGRESS
        List<TzOrderItem> orderItems = Arrays.asList(
            createOrderItem(1001L, TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS),
            createOrderItem(1002L, TzOrderItemStatusEnum.PENDING)
        );

        // When: 调用供应商状态聚合算法
        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(
            orderItems, TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        // Then: 应该聚合为PENDING_SHIPMENT（部分进行中）
        assertEquals(TzOrderSupplierStatusEnum.PENDING_SHIPMENT, result);

        // 验证聚合描述
        String description = supplierStatusCalculator.getAggregationDescription(orderItems, result);
        assertNotNull(description);
        assertTrue(description.contains("总数:2"));
        assertTrue(description.contains("采购中:1"));
        assertTrue(description.contains("待处理:1"));

        System.out.println("订单项聚合结果: " + description);
    }

    @Test
    @DisplayName("测试所有订单项完成后供应商订单状态聚合")
    void testAllOrderItemsCompletedTriggersSupplierCompletion() {
        // Given: 两个订单项都已完成
        List<TzOrderItem> orderItems = Arrays.asList(
            createOrderItem(1001L, TzOrderItemStatusEnum.COMPLETED),
            createOrderItem(1002L, TzOrderItemStatusEnum.COMPLETED)
        );

        // When: 调用供应商状态聚合算法
        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(
            orderItems, TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED);

        // Then: 应该聚合为COMPLETED
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, result);

        // 验证完成度计算
        double completionRate = supplierStatusCalculator.calculateCompletionRate(orderItems);
        assertEquals(100.0, completionRate, 0.01);

        System.out.println("供应商订单完成，完成度: " + completionRate + "%");
    }

    @Test
    @DisplayName("测试供应商订单状态变更触发采购订单聚合")
    void testSupplierOrderStatusChangeTriggersOrderAggregation() {
        // Given: 三个供应商订单，状态各不相同
        List<TzOrderSupplier> supplierOrders = Arrays.asList(
            createSupplierOrder(2001L, TzOrderSupplierStatusEnum.COMPLETED),
            createSupplierOrder(2002L, TzOrderSupplierStatusEnum.SHIPPED),
            createSupplierOrder(2003L, TzOrderSupplierStatusEnum.PENDING_SHIPMENT)
        );

        // When: 调用采购订单状态聚合算法
        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(
            supplierOrders, TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED);

        // Then: 应该聚合为PARTIALLY_PROCUREMENT（部分履约）
        assertEquals(TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT, result);

        // 验证聚合描述
        String description = orderStatusAggregator.getAggregationDescription(supplierOrders, result);
        assertNotNull(description);
        assertTrue(description.contains("总数:3"));
        assertTrue(description.contains("已完成:1"));
        assertTrue(description.contains("物流中:1"));

        System.out.println("采购订单聚合结果: " + description);
    }

    @Test
    @DisplayName("测试全部供应商订单完成后采购订单聚合")
    void testAllSupplierOrdersCompletedTriggersOrderCompletion() {
        // Given: 所有供应商订单都已完成
        List<TzOrderSupplier> supplierOrders = Arrays.asList(
            createSupplierOrder(2001L, TzOrderSupplierStatusEnum.COMPLETED),
            createSupplierOrder(2002L, TzOrderSupplierStatusEnum.COMPLETED),
            createSupplierOrder(2003L, TzOrderSupplierStatusEnum.COMPLETED)
        );

        // When: 调用采购订单状态聚合算法
        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(
            supplierOrders, TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT);

        // Then: 应该聚合为IN_STOCK（已入库）
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, result);

        System.out.println("所有供应商订单完成，采购订单状态聚合为: " + result.getDescription());
    }

    @Test
    @DisplayName("测试订单项取消对聚合算法的影响")
    void testOrderItemCancellationImpactOnAggregation() {
        // Given: 一个订单项取消，一个订单项正常进行
        List<TzOrderItem> orderItems = Arrays.asList(
            createOrderItem(1001L, TzOrderItemStatusEnum.CANCELLED),
            createOrderItem(1002L, TzOrderItemStatusEnum.COMPLETED)
        );

        // When: 调用供应商状态聚合算法
        TzOrderSupplierStatusEnum result = supplierStatusCalculator.aggregateItemStatus(
            orderItems, TzOrderSupplierStatusEnum.SHIPPED);

        // Then: 应该聚合为SHIPPED（已发货，根据算法逻辑）
        assertEquals(TzOrderSupplierStatusEnum.SHIPPED, result);

        // 验证完成度计算（取消的订单项计入总数，但不贡献完成分数）
        double completionRate = supplierStatusCalculator.calculateCompletionRate(orderItems);
        assertEquals(50.0, completionRate, 0.01); // 1个完成项 / 2个总项 = 50%

        System.out.println("部分取消场景下的聚合结果: " + result.getDescription() + ", 完成度: " + completionRate + "%");
    }

    @Test
    @DisplayName("测试状态聚合的验证机制")
    void testStatusAggregationValidation() {
        // Given: 测试订单项数据
        List<TzOrderItem> orderItems = Arrays.asList(
            createOrderItem(1001L, TzOrderItemStatusEnum.COMPLETED),
            createOrderItem(1002L, TzOrderItemStatusEnum.COMPLETED)
        );

        TzOrderSupplierStatusEnum originalStatus = TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED;
        TzOrderSupplierStatusEnum aggregatedStatus = TzOrderSupplierStatusEnum.COMPLETED;

        // When: 验证聚合结果
        boolean isValid = supplierStatusCalculator.validateAggregationResult(
            orderItems, originalStatus, aggregatedStatus);

        // Then: 验证应该通过
        assertTrue(isValid);

        // 测试逆向聚合验证（应该失败）
        TzOrderSupplierStatusEnum invalidStatus = TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        boolean isInvalid = supplierStatusCalculator.validateAggregationResult(
            orderItems, aggregatedStatus, invalidStatus);

        assertFalse(isInvalid);

        System.out.println("正向聚合验证: " + isValid);
        System.out.println("逆向聚合验证: " + isInvalid);
    }

    @Test
    @DisplayName("测试订单状态聚合的边界情况")
    void testOrderStatusAggregationEdgeCases() {
        // 测试空列表
        TzOrderPurchaseStatusEnum emptyResult = orderStatusAggregator.aggregateSupplierStatus(
            Arrays.asList(), TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED);
        assertEquals(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED, emptyResult);

        // 测试null列表
        TzOrderPurchaseStatusEnum nullResult = orderStatusAggregator.aggregateSupplierStatus(
            null, TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED);
        assertEquals(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED, nullResult);

        // 测试不可聚合状态
        List<TzOrderSupplier> suppliers = Arrays.asList(
            createSupplierOrder(2001L, TzOrderSupplierStatusEnum.COMPLETED)
        );
        TzOrderPurchaseStatusEnum nonAggregatable = orderStatusAggregator.aggregateSupplierStatus(
            suppliers, TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
        assertEquals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED, nonAggregatable);

        System.out.println("边界情况测试完成");
    }

    @Test
    @DisplayName("测试事件驱动的状态聚合流程模拟")
    void testEventDrivenStatusAggregationFlow() {
        // 这个测试模拟事件驱动的状态聚合流程
        // 注意：这只是逻辑验证，实际的事件发布需要Spring容器环境

        // Step 1: 模拟订单项状态变更
        OrderItemStatusChangeEvent itemEvent = createOrderItemStatusChangeEvent(
            1001L, 2001L, 3001L,
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            "开始采购"
        );

        // Step 2: 模拟供应商状态聚合（通常在事件监听器中执行）
        List<TzOrderItem> orderItems = Arrays.asList(
            createOrderItem(1001L, TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS),
            createOrderItem(1002L, TzOrderItemStatusEnum.PENDING)
        );

        TzOrderSupplierStatusEnum supplierResult = supplierStatusCalculator.aggregateItemStatus(
            orderItems, TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        // Step 3: 如果供应商状态有变化，模拟供应商状态变更事件
        if (!supplierResult.equals(TzOrderSupplierStatusEnum.PENDING_PAYMENT)) {
            SupplierOrderStatusChangeEvent supplierEvent = createSupplierOrderStatusChangeEvent(
                2001L, 3001L, "supplier-001",
                TzOrderSupplierStatusEnum.PENDING_PAYMENT,
                supplierResult,
                "订单项聚合触发"
            );

            // Step 4: 模拟采购订单状态聚合
            List<TzOrderSupplier> supplierOrders = Arrays.asList(
                createSupplierOrder(2001L, supplierResult)
            );

            TzOrderPurchaseStatusEnum purchaseResult = orderStatusAggregator.aggregateSupplierStatus(
                supplierOrders, TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED);

            // 验证整个流程
            assertEquals(TzOrderSupplierStatusEnum.PENDING_SHIPMENT, supplierResult);
            assertEquals(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS, purchaseResult);

            System.out.println("事件驱动聚合流程完成:");
            System.out.println("  订单项: PENDING → PROCUREMENT_IN_PROGRESS");
            System.out.println("  供应商订单: PENDING_PAYMENT → " + supplierResult.getDescription());
            System.out.println("  采购订单: PAYMENT_COMPLETED → " + purchaseResult.getDescription());
        }
    }

    // ==================== 辅助方法 ====================

    private TzOrderItem createOrderItem(Long id, TzOrderItemStatusEnum status) {
        TzOrderItem item = new TzOrderItem();
        item.setId(id);
        item.setSupplierOrderId(2001L);
        item.setStatus(status);
        return item;
    }

    private TzOrderSupplier createSupplierOrder(Long id, TzOrderSupplierStatusEnum status) {
        TzOrderSupplier supplier = new TzOrderSupplier();
        supplier.setId(id);
        supplier.setPurchaseOrderId(3001L);
        supplier.setSupplierId("supplier-" + id);
        supplier.setStatus(status);
        return supplier;
    }

    private OrderItemStatusChangeEvent createOrderItemStatusChangeEvent(
        Long orderItemId, Long supplierOrderId, Long purchaseOrderId,
        TzOrderItemStatusEnum originalStatus, TzOrderItemStatusEnum newStatus,
        String changeReason) {

        return new OrderItemStatusChangeEvent(
            orderItemId,
            supplierOrderId,
            purchaseOrderId,
            originalStatus,
            newStatus,
            changeReason,
            LocalDateTime.now(),
            "test-event-chain-" + System.currentTimeMillis(),
            OrderItemStatusChangeEvent.ChangeSource.USER_MANUAL,
            "10000",  // tenantId
            "732080147390572"   // userId
        );
    }

    private SupplierOrderStatusChangeEvent createSupplierOrderStatusChangeEvent(
        Long supplierOrderId, Long purchaseOrderId, String supplierId,
        TzOrderSupplierStatusEnum originalStatus, TzOrderSupplierStatusEnum newStatus,
        String changeReason) {

        return new SupplierOrderStatusChangeEvent(
            supplierOrderId,
            purchaseOrderId,
            supplierId,
            originalStatus,
            newStatus,
            changeReason,
            "test-event-chain-" + System.currentTimeMillis(),
            SupplierOrderStatusChangeEvent.ChangeSource.ITEM_AGGREGATION,
            "10000",  // tenantId
            "732080147390572",   // userId
            null // aggregationContext
        );
    }
}
