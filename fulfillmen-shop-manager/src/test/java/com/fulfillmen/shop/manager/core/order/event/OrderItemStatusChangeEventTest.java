/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import static org.junit.jupiter.api.Assertions.*;

import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * OrderItemStatusChangeEvent 单元测试
 *
 * <AUTHOR>
 * @date 2025/9/1
 * @description 测试订单项状态变更事件的功能和业务逻辑
 * @since 1.0.0
 */
@DisplayName("订单项状态变更事件测试")
class OrderItemStatusChangeEventTest {

    private OrderItemStatusChangeEvent testEvent;
    private LocalDateTime testTime;

    @BeforeEach
    void setUp() {
        testTime = LocalDateTime.now();
        testEvent = new OrderItemStatusChangeEvent(
            1001L,                                              // orderItemId
            2001L,                                              // supplierOrderId
            3001L,                                              // purchaseOrderId
            TzOrderItemStatusEnum.PENDING,                      // originalStatus
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,      // newStatus
            "开始采购",                                          // changeReason
            testTime,                                           // changeTime
            "test-event-chain-001",                            // eventChainId
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO, // changeSource
            "10000",                                           // tenantId
            "732080147390572"                                  // userId
        );
    }

    @Test
    @DisplayName("测试事件基本属性")
    void testEventBasicProperties() {
        // 验证所有基本属性
        assertEquals(1001L, testEvent.getOrderItemId());
        assertEquals(2001L, testEvent.getSupplierOrderId());
        assertEquals(3001L, testEvent.getPurchaseOrderId());
        assertEquals(TzOrderItemStatusEnum.PENDING, testEvent.getOriginalStatus());
        assertEquals(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS, testEvent.getNewStatus());
        assertEquals("开始采购", testEvent.getChangeReason());
        assertEquals(testTime, testEvent.getChangeTime());
        assertEquals("test-event-chain-001", testEvent.getEventChainId());
        assertEquals(OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO, testEvent.getChangeSource());
        assertEquals("10000", testEvent.getTenantId());
        assertEquals("732080147390572", testEvent.getUserId());
    }

    @Test
    @DisplayName("测试简化构造函数")
    void testSimplifiedConstructor() {
        // Given & When: 使用简化构造函数
        OrderItemStatusChangeEvent event = new OrderItemStatusChangeEvent(
            1002L,
            2002L,
            3002L,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderItemStatusEnum.SHIPPED,
            "发货完成",
            "test-event-chain-002",
            OrderItemStatusChangeEvent.ChangeSource.USER_MANUAL,
            "10000",
            "732080147390572"
        );

        // Then: 验证属性设置正确
        assertEquals(1002L, event.getOrderItemId());
        assertEquals(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS, event.getOriginalStatus());
        assertEquals(TzOrderItemStatusEnum.SHIPPED, event.getNewStatus());
        assertEquals("发货完成", event.getChangeReason());
        assertNotNull(event.getChangeTime());
        assertTrue(event.getChangeTime().isBefore(LocalDateTime.now().plusSeconds(1)));
    }

    @Test
    @DisplayName("测试关键状态变更判断")
    void testIsCriticalStatusChange() {
        // 测试完成状态
        OrderItemStatusChangeEvent completedEvent = createEventWithNewStatus(TzOrderItemStatusEnum.COMPLETED);
        assertTrue(completedEvent.isCriticalStatusChange());

        // 测试取消状态
        OrderItemStatusChangeEvent cancelledEvent = createEventWithNewStatus(TzOrderItemStatusEnum.CANCELLED);
        assertTrue(cancelledEvent.isCriticalStatusChange());

        // 测试失败状态
        OrderItemStatusChangeEvent failedEvent = createEventWithNewStatus(TzOrderItemStatusEnum.FAILED);
        assertTrue(failedEvent.isCriticalStatusChange());

        // 测试非关键状态
        OrderItemStatusChangeEvent pendingEvent = createEventWithNewStatus(TzOrderItemStatusEnum.PENDING);
        assertFalse(pendingEvent.isCriticalStatusChange());

        OrderItemStatusChangeEvent procurementEvent = createEventWithNewStatus(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
        assertFalse(procurementEvent.isCriticalStatusChange());

        OrderItemStatusChangeEvent shippedEvent = createEventWithNewStatus(TzOrderItemStatusEnum.SHIPPED);
        assertFalse(shippedEvent.isCriticalStatusChange());
    }

    @Test
    @DisplayName("测试是否触发供应商聚合判断")
    void testShouldTriggerSupplierAggregation() {
        // 测试系统自动触发
        OrderItemStatusChangeEvent systemEvent = createEventWithSource(OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO);
        assertTrue(systemEvent.shouldTriggerSupplierAggregation());

        // 测试用户手动触发
        OrderItemStatusChangeEvent userEvent = createEventWithSource(OrderItemStatusChangeEvent.ChangeSource.USER_MANUAL);
        assertTrue(userEvent.shouldTriggerSupplierAggregation());

        // 测试外部同步触发
        OrderItemStatusChangeEvent externalEvent = createEventWithSource(OrderItemStatusChangeEvent.ChangeSource.EXTERNAL_SYNC);
        assertTrue(externalEvent.shouldTriggerSupplierAggregation());

        // 测试测试场景不触发
        OrderItemStatusChangeEvent testEvent = createEventWithSource(OrderItemStatusChangeEvent.ChangeSource.TEST_SCENARIO);
        assertFalse(testEvent.shouldTriggerSupplierAggregation());
    }

    @Test
    @DisplayName("测试用户友好描述")
    void testGetUserFriendlyDescription() {
        assertEquals("订单项待处理", createEventWithNewStatus(TzOrderItemStatusEnum.PENDING).getUserFriendlyDescription());
        assertEquals("订单项采购中", createEventWithNewStatus(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS).getUserFriendlyDescription());
        assertEquals("订单项已发货", createEventWithNewStatus(TzOrderItemStatusEnum.SHIPPED).getUserFriendlyDescription());
        assertEquals("订单项已收货", createEventWithNewStatus(TzOrderItemStatusEnum.DELIVERED).getUserFriendlyDescription());
        assertEquals("订单项已完成", createEventWithNewStatus(TzOrderItemStatusEnum.COMPLETED).getUserFriendlyDescription());
        assertEquals("订单项已取消", createEventWithNewStatus(TzOrderItemStatusEnum.CANCELLED).getUserFriendlyDescription());
        assertEquals("订单项处理失败", createEventWithNewStatus(TzOrderItemStatusEnum.FAILED).getUserFriendlyDescription());
    }

    @Test
    @DisplayName("测试事件ID生成")
    void testGetEventId() {
        // Given: 创建事件
        LocalDateTime specificTime = LocalDateTime.of(2025, 9, 1, 10, 30, 45);
        OrderItemStatusChangeEvent event = new OrderItemStatusChangeEvent(
            1001L, 2001L, 3001L,
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            "测试",
            specificTime,
            "test-chain",
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572"
        );

        // When: 获取事件ID
        String eventId = event.getEventId();

        // Then: 验证事件ID格式
        assertNotNull(eventId);
        assertTrue(eventId.startsWith("ORDER_ITEM_STATUS_CHANGE-1001-PROCUREMENT_IN_PROGRESS-"));
        assertTrue(eventId.contains(String.valueOf(specificTime.toEpochSecond(java.time.ZoneOffset.UTC))));
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // When: 调用toString方法
        String eventString = testEvent.toString();

        // Then: 验证包含关键信息
        assertNotNull(eventString);
        assertTrue(eventString.contains("OrderItemStatusChangeEvent"));
        assertTrue(eventString.contains("orderItemId=1001"));
        assertTrue(eventString.contains("supplierOrderId=2001"));
        assertTrue(eventString.contains("purchaseOrderId=3001"));
        assertTrue(eventString.contains("待处理"));
        assertTrue(eventString.contains("采购中"));
        assertTrue(eventString.contains("开始采购"));
        assertTrue(eventString.contains("系统自动"));
        assertTrue(eventString.contains("test-event-chain-001"));
    }

    @Test
    @DisplayName("测试变更来源枚举")
    void testChangeSourceEnum() {
        // 验证所有变更来源枚举
        assertEquals("系统自动", OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO.getDescription());
        assertEquals("用户手动", OrderItemStatusChangeEvent.ChangeSource.USER_MANUAL.getDescription());
        assertEquals("外部同步", OrderItemStatusChangeEvent.ChangeSource.EXTERNAL_SYNC.getDescription());
        assertEquals("定时任务", OrderItemStatusChangeEvent.ChangeSource.SCHEDULED_TASK.getDescription());
        assertEquals("API调用", OrderItemStatusChangeEvent.ChangeSource.API_CALL.getDescription());
        assertEquals("测试场景", OrderItemStatusChangeEvent.ChangeSource.TEST_SCENARIO.getDescription());
        assertEquals("数据修复", OrderItemStatusChangeEvent.ChangeSource.DATA_REPAIR.getDescription());
    }

    @Test
    @DisplayName("测试批量订单项状态变更事件")
    void testBatchOrderItemStatusChangeEvent() {
        // Given: 创建状态变更列表
        List<OrderItemStatusChangeEvent.OrderItemStatusChange> changes = Arrays.asList(
            new OrderItemStatusChangeEvent.OrderItemStatusChange(
                1001L,
                TzOrderItemStatusEnum.PENDING,
                TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
                "批量处理1"
            ),
            new OrderItemStatusChangeEvent.OrderItemStatusChange(
                1002L,
                TzOrderItemStatusEnum.PENDING,
                TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
                "批量处理2"
            )
        );

        // When: 创建批量事件
        OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent batchEvent = new OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent(
            "batch-001",
            2001L,
            3001L,
            changes,
            "test-event-chain-batch",
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572"
        );

        // Then: 验证批量事件属性
        assertEquals("batch-001", batchEvent.getBatchId());
        assertEquals(2001L, batchEvent.getSupplierOrderId());
        assertEquals(3001L, batchEvent.getPurchaseOrderId());
        assertEquals(2, batchEvent.getChangeCount());
        assertEquals(changes, batchEvent.getStatusChanges());
        assertNotNull(batchEvent.getBatchTime());
        assertTrue(batchEvent.getBatchTime().isBefore(LocalDateTime.now().plusSeconds(1)));
    }

    @Test
    @DisplayName("测试批量事件是否包含关键变更")
    void testBatchEventContainsCriticalChanges() {
        // Given: 创建包含关键状态变更的列表
        List<OrderItemStatusChangeEvent.OrderItemStatusChange> criticalChanges = Arrays.asList(
            new OrderItemStatusChangeEvent.OrderItemStatusChange(
                1001L,
                TzOrderItemStatusEnum.PENDING,
                TzOrderItemStatusEnum.COMPLETED,
                "完成"
            ),
            new OrderItemStatusChangeEvent.OrderItemStatusChange(
                1002L,
                TzOrderItemStatusEnum.PENDING,
                TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
                "进行中"
            )
        );

        OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent batchEvent = new OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent(
            "batch-critical",
            2001L,
            3001L,
            criticalChanges,
            "test-chain",
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572"
        );

        // When & Then: 验证包含关键变更
        assertTrue(batchEvent.containsCriticalChanges());

        // Given: 创建不包含关键状态变更的列表
        List<OrderItemStatusChangeEvent.OrderItemStatusChange> nonCriticalChanges = Arrays.asList(
            new OrderItemStatusChangeEvent.OrderItemStatusChange(
                1001L,
                TzOrderItemStatusEnum.PENDING,
                TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
                "进行中"
            )
        );

        OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent nonCriticalBatchEvent = new OrderItemStatusChangeEvent.BatchOrderItemStatusChangeEvent(
            "batch-non-critical",
            2001L,
            3001L,
            nonCriticalChanges,
            "test-chain",
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572"
        );

        // When & Then: 验证不包含关键变更
        assertFalse(nonCriticalBatchEvent.containsCriticalChanges());
    }

    @Test
    @DisplayName("测试订单项状态变更记录")
    void testOrderItemStatusChange() {
        // Given & When: 创建状态变更记录
        OrderItemStatusChangeEvent.OrderItemStatusChange change = new OrderItemStatusChangeEvent.OrderItemStatusChange(
            1001L,
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.COMPLETED,
            "测试完成"
        );

        // Then: 验证状态变更记录属性
        assertEquals(1001L, change.getOrderItemId());
        assertEquals(TzOrderItemStatusEnum.PENDING, change.getOriginalStatus());
        assertEquals(TzOrderItemStatusEnum.COMPLETED, change.getNewStatus());
        assertEquals("测试完成", change.getChangeReason());
    }

    @Test
    @DisplayName("测试边界条件处理")
    void testBoundaryConditions() {
        // 测试null参数处理
        assertDoesNotThrow(() -> new OrderItemStatusChangeEvent.OrderItemStatusChange(
            null, null, null, null
        ));

        // 测试空字符串参数
        assertDoesNotThrow(() -> new OrderItemStatusChangeEvent.OrderItemStatusChange(
            1001L,
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.COMPLETED,
            ""
        ));
    }

    // ========== 辅助方法 ==========

    private OrderItemStatusChangeEvent createEventWithNewStatus(TzOrderItemStatusEnum newStatus) {
        return new OrderItemStatusChangeEvent(
            1001L, 2001L, 3001L,
            TzOrderItemStatusEnum.PENDING,
            newStatus,
            "测试状态变更",
            LocalDateTime.now(),
            "test-chain",
            OrderItemStatusChangeEvent.ChangeSource.SYSTEM_AUTO,
            "10000",
            "732080147390572"
        );
    }

    private OrderItemStatusChangeEvent createEventWithSource(OrderItemStatusChangeEvent.ChangeSource source) {
        return new OrderItemStatusChangeEvent(
            1001L, 2001L, 3001L,
            TzOrderItemStatusEnum.PENDING,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS,
            "测试来源",
            LocalDateTime.now(),
            "test-chain",
            source,
            "10000",
            "732080147390572"
        );
    }
}