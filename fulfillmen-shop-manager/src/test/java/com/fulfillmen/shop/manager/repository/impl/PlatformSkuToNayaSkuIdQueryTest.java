/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.tenant.ShopTenantContext;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.manager.TestConfiguration;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * PlatformSku 到 NayaSkuId 查询测试
 *
 * <pre>
 * 测试目标：
 * 1. 读取 platformsku_nayaskuIdv3.txt 中的产品信息（产品名称、SKU ID、MRP、ASIN）
 * 2. 根据 SKU ID (platformSku) 查询对应的 nayaSkuId (TzProductSku.id)
 * 3. 同时查询关联的 SPU ID 和 SKU 价格信息
 * 4. 生成 CSV 文件包含原始数据和查询结果（包含产品名称和MRP字段）
 * 5. 生成 UPDATE SQL 脚本用于更新 GFF_Goods 表
 * 6. 支持批量查询和性能优化
 * 7. 验证新增字段的正确性
 * </pre>
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("sealosDev")
class PlatformSkuToNayaSkuIdQueryTest {

    //    private static final String INPUT_FILE_PATH = "platformsku-nayaskuIdSimple.txt";
//    private static final String INPUT_FILE_PATH = "platformsku-nayaskuId.txt";
    //    private static final String INPUT_FILE_PATH = "platformsku_nayaskuIdv2.txt";
    private static final String INPUT_FILE_PATH = "platformsku_nayaskuIdv3.txt";
    private static final Long TARGET_TENANT_ID = 10000L;
    private static final int BATCH_SIZE = 500;
    private static final int GFF_CUSTOMER_ID = 13100;

    @Autowired
    private TzProductSkuMapper tzProductSkuMapper;

    @BeforeEach
    void setUp() {
        // 设置租户上下文
        ShopTenantContext.setTenantId(TARGET_TENANT_ID.toString());
        log.info("测试环境初始化完成，目标租户ID: {}", TARGET_TENANT_ID);
    }

    @Test
    @DisplayName("查询 platformSku 对应的 nayaSkuId（含产品名称和MRP）并生成 CSV 和 SQL 脚本")
    void testQueryPlatformSkuToNayaSkuIdAndGenerateFiles() {
        log.info("🎯 开始执行 platformSku 到 nayaSkuId 查询任务（含产品名称和MRP字段）");
        long startTime = System.currentTimeMillis();

        try {
            // 1. 读取输入文件
            List<SkuDataItem> skuDataItems = readInputFile();
            log.info("成功读取输入文件，共 {} 条记录（包含产品名称、SKU ID、MRP、ASIN字段）", skuDataItems.size());

            assertNotNull(skuDataItems);
            assertFalse(skuDataItems.isEmpty(), "输入数据不能为空");

            // 2. 批量查询数据库
            queryNayaSkuIds(skuDataItems);

            // 3. 生成统计信息
            QueryStatistics statistics = generateStatistics(skuDataItems, startTime);

            // 4. 导出 CSV 文件
            exportToCsvFile(skuDataItems);

            // 5. 生成 UPDATE SQL 脚本
            generateUpdateSqlScript(skuDataItems);

            // 6. 输出统计报告
            printStatisticsReport(statistics);

            // 7. 验证结果
            validateResults(skuDataItems, statistics);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("🎯 ===== platformSku 到 nayaSkuId 查询任务完成（含产品名称和MRP字段） =====");
            log.info("总执行时间: {} 秒", String.format("%.2f", totalTime / 1000.0));
            log.info("查询记录总数: {} 条（包含产品名称、SKU ID、MRP、ASIN字段）", skuDataItems.size());

        } catch (Exception e) {
            log.error("执行查询任务时发生错误", e);
            throw new RuntimeException("查询任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 读取输入文件数据
     */
    private List<SkuDataItem> readInputFile() throws IOException {
        Path filePath = Paths.get("src/test/resources", INPUT_FILE_PATH);
        log.info("尝试读取输入文件: {}", filePath.toAbsolutePath());

        if (!Files.exists(filePath)) {
            throw new IOException("输入文件不存在: " + filePath.toAbsolutePath());
        }

        List<String> lines = Files.readAllLines(filePath);
        log.info("成功读取文件，行数: {}", lines.size());

        List<SkuDataItem> inputData = new ArrayList<>();

        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i).trim();

            // 跳过空行和表头行
            if (StrUtil.isBlank(line) || line.startsWith("Final Product name") || line.startsWith("SKU ID")) {
                continue;
            }

            try {
                // 按制表符分割：Final Product name, SKU ID, MRP, ASIN
                String[] parts = line.split("\\t");
                if (parts.length >= 4) {
                    SkuDataItem item = SkuDataItem.builder()
                      .productName(parts[0].trim())
                      .skuId(parts[1].trim())
                      .mrp(parts[2].trim())
                      .asin(parts[3].trim())
                      .found(false)
                      .build();

                    inputData.add(item);
                    log.debug("解析第{}行成功: ProductName={}, SKU={}, MRP={}, ASIN={}",
                      i + 1, item.getProductName(), item.getSkuId(), item.getMrp(), item.getAsin());
                }
            } catch (Exception e) {
                log.warn("解析第{}行失败: {}, 错误: {}", i + 1, line, e.getMessage());
            }
        }

        log.info("解析完成，有效输入数据数量: {}", inputData.size());
        return inputData;
    }

    /**
     * 批量查询 nayaSkuId 和产品名称、MRP信息
     */
    private void queryNayaSkuIds(List<SkuDataItem> skuDataItems) {
        log.info("开始批量查询 nayaSkuId 和产品信息...");

        // 提取所有的 platformSku
        List<String> platformSkus = skuDataItems.stream()
          .map(SkuDataItem::getSkuId)
          .collect(Collectors.toList());

        // 批量查询（分批处理以避免 SQL IN 子句过长）
        int batchSize = 500;
        Map<String, SkuQueryResult> platformSkuToResultMap = new HashMap<>();

        for (int i = 0; i < platformSkus.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, platformSkus.size());
            List<String> batchPlatformSkus = platformSkus.subList(i, endIndex);

            // 查询SKU信息，同时关联SPU获取产品名称
            List<TzProductSku> skuList = tzProductSkuMapper.selectList(
              new LambdaQueryWrapper<TzProductSku>()
                .in(TzProductSku::getPlatformSku, batchPlatformSkus)
                .select(TzProductSku::getId, TzProductSku::getPlatformSku,
                  TzProductSku::getSpuId, TzProductSku::getPrice)
            );

            // 构建映射关系
            for (TzProductSku sku : skuList) {
                SkuQueryResult result = new SkuQueryResult();
                result.setNayaSkuId(sku.getId());
                result.setSpuId(sku.getSpuId());
                result.setSkuPrice(sku.getPrice());
                platformSkuToResultMap.put(sku.getPlatformSku(), result);
            }

            log.info("已查询第 {}-{} 批数据，查询到 {} 条匹配记录",
              i + 1, endIndex, skuList.size());
        }

        // 更新 skuDataItems
        for (SkuDataItem item : skuDataItems) {
            SkuQueryResult result = platformSkuToResultMap.get(item.getSkuId());
            if (result != null) {
                item.setNayaSkuId(result.getNayaSkuId());
                item.setSpuId(result.getSpuId());
                item.setSkuPrice(result.getSkuPrice());
                item.setFound(true);
            }
        }

        log.info("批量查询完成，共匹配到 {} 条记录", platformSkuToResultMap.size());
    }

    /**
     * 生成统计信息
     */
    private QueryStatistics generateStatistics(List<SkuDataItem> skuDataItems, long startTime) {
        long executionTime = System.currentTimeMillis() - startTime;

        int totalRecords = skuDataItems.size();
        int foundRecords = skuDataItems.stream().mapToInt(item -> item.isFound() ? 1 : 0).sum();
        int notFoundRecords = totalRecords - foundRecords;
        double successRate = totalRecords > 0 ? (double) foundRecords / totalRecords * 100 : 0;

        List<String> notFoundSkuIds = skuDataItems.stream()
          .filter(item -> !item.isFound())
          .map(SkuDataItem::getSkuId)
          .collect(Collectors.toList());

        return QueryStatistics.builder()
          .totalRecords(totalRecords)
          .foundRecords(foundRecords)
          .notFoundRecords(notFoundRecords)
          .successRate(successRate)
          .executionTimeMs(executionTime)
          .notFoundSkuIds(notFoundSkuIds)
          .build();
    }

    /**
     * 导出 CSV 文件
     */
    private void exportToCsvFile(List<SkuDataItem> skuDataItems) throws IOException {
        if (CollectionUtil.isEmpty(skuDataItems)) {
            log.warn("没有数据需要导出到CSV");
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "platformsku_nayaskuid_export_" + timestamp + ".csv";
        Path outputPath = Paths.get("src/test/resources", fileName);

        log.info("开始导出 CSV 数据到文件: {}", outputPath.toAbsolutePath());

        try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8))) {
            // 写入 CSV 头部
            writer.println("\"Product Name\",\"SKU ID\",\"MRP(Rs.)\",\"ASIN\",\"nayaSkuId\",\"SPU ID\",\"SKU Price\"");

            // 写入数据行
            for (SkuDataItem item : skuDataItems) {
                String nayaSkuIdStr = item.isFound() ? String.valueOf(item.getNayaSkuId()) : "NOT_FOUND";
                String spuIdStr = item.isFound() && item.getSpuId() != null ? String.valueOf(item.getSpuId()) : "NOT_FOUND";
                String skuPriceStr = item.isFound() && item.getSkuPrice() != null ? item.getSkuPrice().toString() : "NOT_FOUND";

                writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"%n",
                  escapeCsvField(item.getProductName()),
                  escapeCsvField(item.getSkuId()),
                  escapeCsvField(item.getMrp()),
                  escapeCsvField(item.getAsin()),
                  escapeCsvField(nayaSkuIdStr),
                  escapeCsvField(spuIdStr),
                  escapeCsvField(skuPriceStr));
            }
        }

        log.info("CSV 数据导出完成，文件: {}, 记录数: {}", outputPath.toAbsolutePath(), skuDataItems.size());
    }

    /**
     * 生成 UPDATE SQL 脚本
     */
    private void generateUpdateSqlScript(List<SkuDataItem> skuDataItems) throws IOException {
        if (CollectionUtil.isEmpty(skuDataItems)) {
            log.warn("没有数据需要生成SQL脚本");
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "update_gff_goods_script_" + timestamp + ".sql";
        Path outputPath = Paths.get("src/test/resources", fileName);

        log.info("开始生成 UPDATE SQL 脚本: {}", outputPath.toAbsolutePath());

        try (PrintWriter writer = new PrintWriter(Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8))) {
            // 写入脚本头部信息
            writer.println("-- GFF_Goods 表更新脚本");
            writer.println("-- 生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            writer.println("-- 说明: 根据 platformsku_nayaskuIdv3.txt 生成的批量更新脚本");
            writer.println("-- 数据格式: Product Name, SKU ID, MRP, ASIN");
            writer.println("-- 更新字段: SKU = platformSku, GoodsCode = ASIN");
            writer.printf("-- 更新条件: GFF_CustomerID = %d AND SKU = nayaSkuId%n", GFF_CUSTOMER_ID);
            writer.println();

            // 只为找到匹配的记录生成 SQL
            List<SkuDataItem> foundItems = skuDataItems.stream()
              .filter(SkuDataItem::isFound)
              .toList();

            if (foundItems.isEmpty()) {
                writer.println("-- 警告: 没有找到任何匹配的记录，无法生成更新脚本");
                log.warn("没有找到匹配的记录，SQL脚本为空");
                return;
            }

            writer.println("-- 开始事务");
            writer.println("BEGIN TRANSACTION;");
            writer.println();

            // 生成 UPDATE 语句
            int updateCount = 0;
            for (SkuDataItem item : foundItems) {
                writer.printf("UPDATE GFF_Goods SET SKU = '%s', GoodsCode = '%s' ,MRP='%s', EnName='%s' WHERE GFF_CustomerID = %d AND SKU = '%s';%n",
                  escapeSqlString(item.getSkuId()),
                  escapeSqlString(item.getAsin()),
                  escapeSqlString(item.getMrp()),
                  escapeSqlString(item.getProductName()),
                  9808,
                  item.getNayaSkuId());
                updateCount++;

                // 每100条语句添加一个注释
                if (updateCount % 100 == 0) {
                    writer.printf("-- 已处理 %d 条记录%n", updateCount);
                    writer.println();
                }
            }

            writer.println();
            writer.printf("-- 总共生成 %d 条 UPDATE 语句%n", updateCount);
            writer.println();
            writer.println("-- 提交事务（请在确认无误后取消注释）");
            writer.println("-- COMMIT;");
            writer.println();
            writer.println("-- 回滚事务（如果需要撤销更改）");
            writer.println("-- ROLLBACK;");
        }

        log.info("UPDATE SQL 脚本生成完成: {}, 生成 {} 条更新语句",
          outputPath.toAbsolutePath(),
          skuDataItems.stream().filter(SkuDataItem::isFound).count());
    }

    /**
     * 输出统计报告
     */
    private void printStatisticsReport(QueryStatistics statistics) {
        log.info("📊 ==================== 查询统计报告 ====================");
        log.info("总记录数: {}", statistics.getTotalRecords());
        log.info("找到匹配记录数: {}", statistics.getFoundRecords());
        log.info("未找到匹配记录数: {}", statistics.getNotFoundRecords());
        log.info("匹配成功率: {}%", String.format("%.2f", statistics.getSuccessRate()));
        log.info("执行时间: {} ms ({} 秒)",
          statistics.getExecutionTimeMs(),
          String.format("%.2f", statistics.getExecutionTimeMs() / 1000.0));

        if (!statistics.getNotFoundSkuIds().isEmpty()) {
            log.info("未找到匹配的前10个 SKU ID:");
            statistics.getNotFoundSkuIds().stream()
              .limit(10)
              .forEach(skuId -> log.info("  - {}", skuId));

            if (statistics.getNotFoundSkuIds().size() > 10) {
                log.info("  ... 还有 {} 个未匹配的 SKU ID",
                  statistics.getNotFoundSkuIds().size() - 10);
            }
        }
        log.info("=====================================================");
    }

    /**
     * CSV 字段转义
     */
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        // 替换双引号为两个双引号
        return field.replace("\"", "\"\"");
    }

    /**
     * SQL 字符串转义
     */
    private String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        // 替换单引号为两个单引号，防止 SQL 注入
        return value.replace("'", "''");
    }

    /**
     * 验证结果
     */
    private void validateResults(List<SkuDataItem> inputData, QueryStatistics statistics) {
        log.info("开始验证查询结果");

        // 基本验证
        assertNotNull(inputData);
        assertNotNull(statistics);
        assertFalse(inputData.isEmpty(), "输入数据不能为空");
        assertTrue(statistics.getTotalRecords() > 0, "应该有查询记录");

        // 验证数据一致性
        assertTrue(statistics.getTotalRecords() == inputData.size(),
          "统计记录数应该等于输入数据数");

        int actualFoundCount = (int) inputData.stream().filter(SkuDataItem::isFound).count();
        assertTrue(statistics.getFoundRecords() == actualFoundCount,
          "统计找到记录数应该与实际一致");

        // 验证新增字段的完整性
        for (SkuDataItem item : inputData) {
            // 验证所有输入数据都有产品名称和MRP
            assertNotNull(item.getProductName(), "产品名称不能为空");
            assertFalse(item.getProductName().trim().isEmpty(), "产品名称不能为空字符串");
            assertNotNull(item.getMrp(), "MRP不能为空");
            assertFalse(item.getMrp().trim().isEmpty(), "MRP不能为空字符串");

            // 验证找到匹配记录的数据完整性
            if (item.isFound()) {
                assertNotNull(item.getNayaSkuId(), "找到匹配记录时nayaSkuId不能为空");
                assertNotNull(item.getSpuId(), "找到匹配记录时spuId不能为空");
                assertTrue(item.getNayaSkuId() > 0, "nayaSkuId应该大于0");
                assertTrue(item.getSpuId() > 0, "spuId应该大于0");
                // SKU价格可能为空，不强制验证
            }
        }

        log.info("数据验证完成，总记录数: {}, 匹配成功数: {}, 新增字段验证通过",
          statistics.getTotalRecords(), statistics.getFoundRecords());
    }

    /**
     * SKU数据项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuDataItem {

        private String productName;     // 产品名称
        private String skuId;           // 原始 SKU ID (platformSku)
        private String mrp;             // MRP 价格
        private String asin;            // ASIN 码
        private Long nayaSkuId;         // 查询到的 nayaSkuId (TzProductSku.id)
        private Long spuId;             // 查询到的 spuId (TzProductSpu.id)
        private java.math.BigDecimal skuPrice; // SKU价格
        private boolean found;          // 是否找到匹配记录

        @Override
        public String toString() {
            return String.format("SkuDataItem{productName='%s', skuId='%s', mrp='%s', asin='%s', nayaSkuId=%s, spuId=%s, skuPrice=%s, found=%s}",
              productName, skuId, mrp, asin, nayaSkuId, spuId, skuPrice, found);
        }
    }

    /**
     * SKU查询结果辅助类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuQueryResult {

        private Long nayaSkuId;         // nayaSkuId (TzProductSku.id)
        private Long spuId;             // spuId (TzProductSpu.id)
        private java.math.BigDecimal skuPrice; // SKU价格
    }

    /**
     * 查询结果统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryStatistics {

        private int totalRecords;       // 总记录数
        private int foundRecords;       // 找到匹配的记录数
        private int notFoundRecords;    // 未找到匹配的记录数
        private double successRate;     // 成功率
        private long executionTimeMs;   // 执行时间（毫秒）
        private List<String> notFoundSkuIds; // 未找到的SKU ID列表
    }
}
