/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.status;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 订单状态聚合器测试
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 测试供应商订单状态到采购订单状态的聚合算法
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("订单状态聚合器测试")
class OrderStatusAggregatorTest {

    @InjectMocks
    private OrderStatusAggregator orderStatusAggregator;

    private TzOrderPurchaseStatusEnum currentStatus;

    @BeforeEach
    void setUp() {
        currentStatus = TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
    }

    @Test
    @DisplayName("空供应商订单列表应保持当前状态")
    void shouldKeepCurrentStatusWhenSupplierOrdersEmpty() {
        // 测试null列表
        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(null, currentStatus);
        assertEquals(currentStatus, result, "空列表应保持当前状态");

        // 测试空列表
        result = orderStatusAggregator.aggregateSupplierStatus(Collections.emptyList(), currentStatus);
        assertEquals(currentStatus, result, "空列表应保持当前状态");
    }

    @Test
    @DisplayName("不可聚合状态应保持原状态")
    void shouldKeepOriginalStatusWhenNotAggregatable() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        // 待支付状态不参与聚合
        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(
            suppliers, TzOrderPurchaseStatusEnum.PAYMENT_PENDING);
        assertEquals(TzOrderPurchaseStatusEnum.PAYMENT_PENDING, result);

        // 已取消状态不参与聚合
        result = orderStatusAggregator.aggregateSupplierStatus(
            suppliers, TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
        assertEquals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED, result);

        // 已完成状态不参与聚合
        result = orderStatusAggregator.aggregateSupplierStatus(
            suppliers, TzOrderPurchaseStatusEnum.IN_STOCK);
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, result);
    }

    @Test
    @DisplayName("全部取消应聚合为订单取消")
    void shouldAggregateToOrderCancelledWhenAllCancelled() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.CANCELLED,
            TzOrderSupplierStatusEnum.CANCELLED,
            TzOrderSupplierStatusEnum.CANCELLED);

        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(suppliers, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED, result,
            "所有供应商订单取消应聚合为订单取消");
    }

    @Test
    @DisplayName("全部完成应聚合为已入库")
    void shouldAggregateToInStockWhenAllCompleted() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED);

        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(suppliers, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, result,
            "所有供应商订单完成应聚合为已入库");
    }

    @Test
    @DisplayName("全部进行中应聚合为采购中")
    void shouldAggregateToProcurementInProgressWhenAllInProgress() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT);

        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(suppliers, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS, result,
            "全部供应商订单进行中应聚合为采购中");
    }

    @Test
    @DisplayName("大部分物流中应聚合为供应商已发货")
    void shouldAggregateToSupplierShippedWhenMostlyShipping() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED);

        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(suppliers, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED, result,
            "大部分供应商订单在物流中应聚合为供应商已发货");
    }

    @Test
    @DisplayName("高比例完成应聚合为仓库已收货")
    void shouldAggregateToWarehouseReceivedWhenHighCompletionRate() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED,
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(suppliers, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED, result,
            "高完成率应聚合为仓库已收货");
    }

    @Test
    @DisplayName("部分完成应聚合为部分履约")
    void shouldAggregateToPartiallyProcurementWhenPartiallyCompleted() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(suppliers, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT, result,
            "部分完成应聚合为部分履约");
    }

    @Test
    @DisplayName("测试聚合结果验证")
    void shouldValidateAggregationResult() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED);

        TzOrderPurchaseStatusEnum originalStatus = TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
        TzOrderPurchaseStatusEnum aggregatedStatus = TzOrderPurchaseStatusEnum.IN_STOCK;

        // 正常聚合应该通过验证
        boolean isValid = orderStatusAggregator.validateAggregationResult(
            suppliers, originalStatus, aggregatedStatus);
        assertTrue(isValid, "正常状态聚合应该通过验证");

        // 逆向聚合应该验证失败
        TzOrderPurchaseStatusEnum reverseStatus = TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
        boolean isValidReverse = orderStatusAggregator.validateAggregationResult(
            suppliers, originalStatus, reverseStatus);
        assertFalse(isValidReverse, "逆向状态聚合应该验证失败");
    }

    @Test
    @DisplayName("测试状态一致性验证")
    void shouldValidateStatusConsistency() {
        // 全部取消的供应商订单
        List<TzOrderSupplier> allCancelledSuppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.CANCELLED,
            TzOrderSupplierStatusEnum.CANCELLED);

        // 如果所有订单都取消了，聚合状态必须是取消
        boolean isValid = orderStatusAggregator.validateAggregationResult(
            allCancelledSuppliers, TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderPurchaseStatusEnum.IN_STOCK); // 错误的聚合结果
        assertFalse(isValid, "所有订单取消时聚合为其他状态应验证失败");

        // 全部完成的供应商订单
        List<TzOrderSupplier> allCompletedSuppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED);

        // 如果所有订单都完成了，聚合状态必须是完成
        isValid = orderStatusAggregator.validateAggregationResult(
            allCompletedSuppliers, TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS); // 错误的聚合结果
        assertFalse(isValid, "所有订单完成时聚合为其他状态应验证失败");
    }

    @Test
    @DisplayName("测试聚合描述生成")
    void shouldGenerateAggregationDescription() {
        List<TzOrderSupplier> suppliers = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.SHIPPED);

        String description = orderStatusAggregator.getAggregationDescription(
            suppliers, TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT);

        assertNotNull(description, "聚合描述不应为空");
        assertTrue(description.contains("总数:3"), "描述应包含总数");
        assertTrue(description.contains("部分履约"), "描述应包含聚合状态");
    }

    @Test
    @DisplayName("边界条件测试：单个供应商订单")
    void shouldHandleSingleSupplierOrder() {
        // 单个进行中订单
        List<TzOrderSupplier> singleInProgress = createSupplierOrders(
            TzOrderSupplierStatusEnum.PENDING_PAYMENT);
        TzOrderPurchaseStatusEnum result = orderStatusAggregator.aggregateSupplierStatus(
            singleInProgress, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS, result);

        // 单个完成订单
        List<TzOrderSupplier> singleCompleted = createSupplierOrders(
            TzOrderSupplierStatusEnum.COMPLETED);
        result = orderStatusAggregator.aggregateSupplierStatus(singleCompleted, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, result);

        // 单个取消订单
        List<TzOrderSupplier> singleCancelled = createSupplierOrders(
            TzOrderSupplierStatusEnum.CANCELLED);
        result = orderStatusAggregator.aggregateSupplierStatus(singleCancelled, currentStatus);
        assertEquals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED, result);
    }

    /**
     * 创建测试用的供应商订单列表
     */
    private List<TzOrderSupplier> createSupplierOrders(TzOrderSupplierStatusEnum... statuses) {
        List<TzOrderSupplier> suppliers = new ArrayList<>();

        for (int i = 0; i < statuses.length; i++) {
            TzOrderSupplier supplier = new TzOrderSupplier();
            supplier.setId((long) (i + 1));
            supplier.setStatus(statuses[i]);
            supplier.setPurchaseOrderId(1L);
            suppliers.add(supplier);
        }

        return suppliers;
    }
}
