/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.tenant.ShopTenantContext;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.manager.TestConfiguration;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 简化的SKU映射验证测试
 *
 * <pre>
 * 目的：验证 platformSku 到 nayaSkuId 的准确映射关系
 * 1. 验证数据库连接是否正常
 * 2. 验证 TzProductSku 表中是否存在对应的 platformSku 数据
 * 3. 验证查询逻辑的准确性
 * </pre>
 */
@Slf4j
@SpringBootTest(classes = TestConfiguration.class)
@ActiveProfiles("sealosDev")
class SimpleSkuMappingVerificationTest {

    private static final String INPUT_FILE_PATH = "platformsku-nayaskuIdSimple.txt";
    private static final Long TARGET_TENANT_ID = 10000L;

    @Autowired
    private TzProductSkuMapper tzProductSkuMapper;

    @BeforeEach
    void setUp() {
        // 设置租户上下文
        ShopTenantContext.setTenantId(TARGET_TENANT_ID.toString());
        log.info("测试环境初始化完成，目标租户ID: {}", TARGET_TENANT_ID);
    }

    @Test
    @DisplayName("验证数据库连接和表结构")
    void testDatabaseConnection() {
        log.info("🔍 开始验证数据库连接和表结构...");

        // 1. 测试数据库连接
        Long totalCount = tzProductSkuMapper.selectCount(null);
        log.info("TzProductSku 表总记录数: {}", totalCount);
        assertNotNull(totalCount);
        assertTrue(totalCount >= 0, "数据库连接应该正常");

        // 2. 查看前10条记录的 platformSku 示例
        List<TzProductSku> sampleSkus = tzProductSkuMapper.selectList(
            new LambdaQueryWrapper<TzProductSku>()
                .select(TzProductSku::getId, TzProductSku::getPlatformSku)
                .last("LIMIT 10")
        );

        log.info("数据库中前10条 TzProductSku 记录的 platformSku 示例:");
        for (int i = 0; i < sampleSkus.size(); i++) {
            TzProductSku sku = sampleSkus.get(i);
            log.info("  {}: nayaSkuId={}, platformSku={}",
                i + 1, sku.getId(), sku.getPlatformSku());
        }

        log.info("✅ 数据库连接验证完成");
    }

    @Test
    @DisplayName("验证输入文件前10个SKU的匹配情况")
    void testFirst10SkusMapping() throws IOException {
        log.info("🎯 开始验证输入文件前10个SKU的匹配情况...");

        // 1. 读取输入文件
        Path filePath = Paths.get("src/test/resources", INPUT_FILE_PATH);
        List<String> lines = Files.readAllLines(filePath);

        log.info("输入文件路径: {}", filePath.toAbsolutePath());
        log.info("输入文件行数: {}", lines.size());

        // 2. 验证前10个SKU（跳过表头）
        int verifyCount = Math.min(10, lines.size() - 1);
        int foundCount = 0;

        log.info("开始验证前{}个SKU ID的匹配情况:", verifyCount);
        log.info("格式: 行号 | SKU ID | MRP | ASIN | nayaSkuId | 匹配状态");
        log.info("--------------------------------------------------");

        for (int i = 1; i <= verifyCount; i++) { // 从第2行开始（跳过表头）
            String line = lines.get(i).trim();
            if (StrUtil.isBlank(line)) {
                continue;
            }

            String[] parts = line.split("\\t");
            if (parts.length >= 3) {
                String skuId = parts[0].trim();
                String mrp = parts[1].trim();
                String asin = parts[2].trim();

                // 查询数据库
                TzProductSku foundSku = tzProductSkuMapper.selectOne(
                    new LambdaQueryWrapper<TzProductSku>()
                        .eq(TzProductSku::getPlatformSku, skuId)
                        .select(TzProductSku::getId, TzProductSku::getPlatformSku)
                        .last("LIMIT 1")
                );

                if (foundSku != null) {
                    foundCount++;
                    log.info("{:2d} | {} | {} | {} | {} | ✅ 匹配",
                        i, skuId, mrp, asin, foundSku.getId());
                } else {
                    log.info("{:2d} | {} | {} | {} | {} | ❌ 未匹配",
                        i, skuId, mrp, asin, "NULL");
                }
            }
        }

        log.info("--------------------------------------------------");
        log.info("验证结果统计:");
        log.info("  - 验证总数: {}", verifyCount);
        log.info("  - 匹配成功: {}", foundCount);
        log.info("  - 匹配失败: {}", verifyCount - foundCount);
        log.info("  - 匹配成功率: {:.2f}%", (double) foundCount / verifyCount * 100);

        log.info("✅ 前10个SKU匹配验证完成");
    }

    @Test
    @DisplayName("测试批量查询性能和准确性")
    void testBatchQueryAccuracy() throws IOException {
        log.info("⚡ 开始测试批量查询性能和准确性...");

        // 1. 读取前50个SKU进行批量查询测试
        Path filePath = Paths.get("src/test/resources", INPUT_FILE_PATH);
        List<String> lines = Files.readAllLines(filePath);

        int testCount = Math.min(50, lines.size() - 1);
        List<String> testSkuIds = lines.stream()
            .skip(1) // 跳过表头
            .limit(testCount)
            .map(line -> {
                String[] parts = line.split("\\t");
                return parts.length > 0 ? parts[0].trim() : "";
            })
            .filter(StrUtil::isNotBlank)
            .toList();

        log.info("准备批量查询 {} 个SKU ID", testSkuIds.size());

        // 2. 执行批量查询
        long startTime = System.currentTimeMillis();

        List<TzProductSku> batchResults = tzProductSkuMapper.selectList(
            new LambdaQueryWrapper<TzProductSku>()
                .in(TzProductSku::getPlatformSku, testSkuIds)
                .select(TzProductSku::getId, TzProductSku::getPlatformSku)
        );

        long endTime = System.currentTimeMillis();

        log.info("批量查询完成:");
        log.info("  - 输入SKU数量: {}", testSkuIds.size());
        log.info("  - 查询结果数量: {}", batchResults.size());
        log.info("  - 查询耗时: {} ms", endTime - startTime);
        log.info("  - 匹配成功率: {:.2f}%",
            (double) batchResults.size() / testSkuIds.size() * 100);

        // 3. 验证批量查询结果的准确性
        log.info("批量查询结果验证（前10条）:");
        for (int i = 0; i < Math.min(10, batchResults.size()); i++) {
            TzProductSku sku = batchResults.get(i);
            log.info("  {}: platformSku={} -> nayaSkuId={}",
                i + 1, sku.getPlatformSku(), sku.getId());

            // 验证这个SKU确实在我们的输入列表中
            assertTrue(testSkuIds.contains(sku.getPlatformSku()),
                "查询结果中的 platformSku 应该在输入列表中");
        }

        log.info("✅ 批量查询性能和准确性测试完成");
    }
}
