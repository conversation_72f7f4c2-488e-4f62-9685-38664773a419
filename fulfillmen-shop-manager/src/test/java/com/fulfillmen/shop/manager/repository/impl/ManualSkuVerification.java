/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.repository.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 手动验证 SKU 映射关系的工具类
 *
 * <pre>
 * 不依赖 Spring 容器，用于验证数据结构和逻辑正确性
 * </pre>
 */
public class ManualSkuVerification {

    public static void main(String[] args) {
        System.out.println("🔍 开始手动验证 SKU 映射逻辑...");

        try {
            verifyInputFileStructure();
            verifyMappingLogic();
            System.out.println("✅ 手动验证完成");
        } catch (Exception e) {
            System.err.println("❌ 验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 验证输入文件结构
     */
    private static void verifyInputFileStructure() throws IOException {
        System.out.println("\n📁 验证输入文件结构...");

        Path filePath = Paths.get("src/test/resources/platformsku-nayaskuIdSimple.txt");
        System.out.println("文件路径: " + filePath.toAbsolutePath());

        if (!Files.exists(filePath)) {
            throw new IOException("输入文件不存在: " + filePath.toAbsolutePath());
        }

        List<String> lines = Files.readAllLines(filePath);
        System.out.println("文件总行数: " + lines.size());

        if (lines.isEmpty()) {
            throw new IOException("输入文件为空");
        }

        // 检查表头
        String header = lines.get(0);
        System.out.println("表头: " + header);

        String[] headerParts = header.split("\\t");
        System.out.println("表头列数: " + headerParts.length);
        for (int i = 0; i < headerParts.length; i++) {
            System.out.println("  第" + (i + 1) + "列: " + headerParts[i]);
        }

        // 检查前5条数据记录
        System.out.println("\n前5条数据记录:");
        int dataCount = Math.min(5, lines.size() - 1);
        for (int i = 1; i <= dataCount; i++) {
            String line = lines.get(i);
            String[] parts = line.split("\\t");
            System.out.println("第" + i + "行数据:");
            System.out.println("  原始数据: " + line);
            System.out.println("  列数: " + parts.length);
            if (parts.length >= 3) {
                System.out.println("  SKU ID: " + parts[0].trim());
                System.out.println("  MRP: " + parts[1].trim());
                System.out.println("  ASIN: " + parts[2].trim());
            }
            System.out.println();
        }
    }

    /**
     * 验证映射逻辑
     */
    private static void verifyMappingLogic() {
        System.out.println("\n🎯 验证映射逻辑...");

        // 模拟映射关系验证
        System.out.println("映射关系定义:");
        System.out.println("  输入文件第1列 (SKU ID) -> TzProductSku.platformSku");
        System.out.println("  TzProductSku.id -> 输出文件 nayaSkuId 列");

        // 验证查询逻辑
        System.out.println("\n查询逻辑验证:");
        System.out.println("  1. 读取输入文件，提取所有 SKU ID");
        System.out.println("  2. 批量查询: SELECT id, platform_sku FROM tz_product_sku WHERE platform_sku IN (skuIds)");
        System.out.println("  3. 构建映射: Map<String platformSku, Long nayaSkuId>");
        System.out.println("  4. 更新输出数据: skuDataItem.nayaSkuId = mapping.get(skuDataItem.skuId)");

        // 验证输出逻辑
        System.out.println("\n输出逻辑验证:");
        System.out.println("  CSV格式: SKU ID, MRP(Rs.), ASIN, nayaSkuId");
        System.out.println("  SQL格式: UPDATE GFF_Goods SET SKU=skuId, GoodsCode=asin WHERE GFF_CustomerID=13100 AND SKU=nayaSkuId");

        System.out.println("\n🔍 关键验证点:");
        System.out.println("  ✓ 确保 platformSku 字段名称正确 (TzProductSku::getPlatformSku)");
        System.out.println("  ✓ 确保 nayaSkuId 取值正确 (TzProductSku::getId)");
        System.out.println("  ✓ 确保批量查询性能优化 (分批500条)");
        System.out.println("  ✓ 确保租户上下文正确设置 (10000L)");
        System.out.println("  ✓ 确保SQL更新条件准确 (cuscode=13100 AND SKU=nayaSkuId)");
    }
}
