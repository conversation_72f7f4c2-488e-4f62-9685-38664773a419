create table GFF_Goods
(
    GFF_GoodsID              int identity
        constraint PK_GFF_GOODS
            primary key,
    GFF_CustomerID           int,
    GoodsCode                nvarchar(300),
    SKU                      nvarchar(300),
    PlatformSku              nvarchar(300),
    offerID                  nvarchar(500),
    Field1                   nvarchar(500),
    CnName                   nvarchar(500),
    EnName                   nvarchar(500),
    GoodsStyle               nvarchar(300),
    HasSku                   bit
        constraint DF_GFF_Goods_HasSku default 0             not null,
    IsQC                     bit
        constraint IsQC default 0,
    PackStyle                nvarchar(300),
    CustomcCode              nvarchar(100),
    ProducingArea            nvarchar(500),
    Style                    int,
    Weight                   float
        constraint DF__GFF_Goods__Weigh__4316F928 default 0  not null,
    t_Weight                 float
        constraint DF_GFF_Goods_t_Weight default 1,
    Length                   float
        constraint DF__GFF_Goods__Lengt__440B1D61 default 0  not null,
    Width                    float
        constraint DF__GFF_Goods__Width__44FF419A default 0  not null,
    High                     float
        constraint DF__GFF_Goods__High__45F365D3 default 0   not null,
    t_Length                 float,
    t_Width                  float,
    t_High                   float,
    Price                    decimal(13, 2)
        constraint DF__GFF_Goods__Price__46E78A0C default 0  not null,
    GoodsStatus              nvarchar(30),
    LowStock                 float
        constraint DF__GFF_Goods__LowSt__47DBAE45 default 0  not null,
    HighStock                float
        constraint DF__GFF_Goods__HighS__48CFD27E default 0  not null,
    PlanQuantity             float
        constraint DF__GFF_Goods__PlanQ__49C3F6B7 default 0  not null,
    FactQuantity             float
        constraint DF__GFF_Goods__FactQ__4AB81AF0 default 0  not null,
    UseQuantity              float
        constraint DF__GFF_Goods__UseQu__4BAC3F29 default 0  not null,
    skuAttributes            nvarchar(500),
    Office                   nvarchar(50),
    Field2                   nvarchar(500),
    Field3                   nvarchar(500),
    Field4                   nvarchar(500),
    Field5                   nvarchar(500),
    Field6                   nvarchar(500),
    Field7                   nvarchar(500),
    Field8                   nvarchar(500),
    Field9                   nvarchar(500),
    Field10                  nvarchar(500),
    Field11                  nvarchar(500),
    Field12                  nvarchar(500),
    Field13                  nvarchar(500),
    Field14                  nvarchar(500),
    Field15                  nvarchar(500),
    Field16                  nvarchar(500),
    Field17                  nvarchar(500),
    Field18                  nvarchar(500),
    Field19                  nvarchar(500),
    Field20                  nvarchar(500),
    Void_flag                bit
        constraint DF__GFF_Goods__Void___4CA06362 default 0  not null,
    Remark                   nvarchar(300),
    CreateUser               nvarchar(80),
    CreateTime               datetime,
    ModifyUser               nvarchar(80),
    ModifyTime               datetime,
    VoidUser                 nvarchar(80),
    VoidTime                 nvarchar(30),
    ApproveUser              nvarchar(80),
    ApproveTime              nvarchar(30),
    ExpectNum                int,
    isApproval               int
        constraint DF_GFF_Goods_isApproval default 0,
    Brand                    nvarchar(50),
    IIAA                     nvarchar(1000),
    D_purpose                nvarchar(50),
    D_texture                nvarchar(50),
    Sales_Num                int
        constraint DF_GFF_Goods_Sales_Num default 0          not null,
    Fee                      float
        constraint DF_GFF_Goods_Fee default 0                not null,
    IsBatch                  int
        constraint DF_GFF_Goods_IsBatch default 0            not null,
    IsProduction             int
        constraint DF_GFF_Goods_IsProduction default 0       not null,
    IsDueTo                  int
        constraint DF_GFF_Goods_IsDueTo default 0            not null,
    ProUnit                  int
        constraint DF_GFF_Goods_ProUnit default 0            not null,
    PurchasePrice            decimal(13, 2)
        constraint DF_GFF_Goods_PurchasePrice default 0,
    PurchaseCost             decimal(13, 2)
        constraint DF_GFF_Goods_PurchaseCost default 0,
    InventoryID              nvarchar(100),
    StoreName                nvarchar(100),
    specId                   nvarchar(100)
        constraint DF__GFF_Goods__specI__6D98D987 default '' not null,
    IsLinkOrder              bit
        constraint DF__GFF_Goods__IsLin__4BCDD168 default 0  not null,
    cat_id_level2            int,
    product_declared_name_cn nvarchar(500),
    product_declared_name    nvarchar(500),
    product_material         nvarchar(100),
    return_auth              int,
    sku_wrapper_type         int,
    type_of_goods            int,
    product_link             nvarchar(200),
    IsBrand                  int,
    product_model            nvarchar(50),
    verify                   int,
    IsForOverSeaWarehouse    int,
    InfoTips                 nvarchar(500),
    product_sku              nvarchar(50),
    product_barcode          nvarchar(50),
    MRP                      nvarchar(50)
)
go

exec sp_addextendedproperty 'MS_Description', N'产品信息记录了每个客户在系统中的所有的产品信息', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods'
go

exec sp_addextendedproperty 'MS_Description', N'表的PK，', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'GFF_GoodsID'
go

exec sp_addextendedproperty 'MS_Description', N'表的PK，，不能为空，前台控制', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'GFF_CustomerID'
go

exec sp_addextendedproperty 'MS_Description', N'产品代码,不能为空且唯一，UI长度为50个字符', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'GoodsCode'
go

exec sp_addextendedproperty 'MS_Description', N'SKU,不能为空且唯一，UI长度为100个字符', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'SKU'
go

exec sp_addextendedproperty 'MS_Description', N'产品中文名,UI长度为200个字符', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'CnName'
go

exec sp_addextendedproperty 'MS_Description', N'产品英文名,,UI长度为200个字符', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'EnName'
go

exec sp_addextendedproperty 'MS_Description', N'产品分类，来自类型资料StyleInfo里的2', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'GoodsStyle'
go

exec sp_addextendedproperty 'MS_Description', N' 包装类型，来自类型资料StyleInfo里的3', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'PackStyle'
go

exec sp_addextendedproperty 'MS_Description', N'海关编码', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'CustomcCode'
go

exec sp_addextendedproperty 'MS_Description', N'原产地', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'ProducingArea'
go

exec sp_addextendedproperty 'MS_Description', N' 所属类型:', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Style'
go

exec sp_addextendedproperty 'MS_Description', N'产品的重量，不能为空', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Weight'
go

exec sp_addextendedproperty 'MS_Description', N'真实重量', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 't_Weight'
go

exec sp_addextendedproperty 'MS_Description', N'长，不能为空', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Length'
go

exec sp_addextendedproperty 'MS_Description', N'宽，不能为空', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Width'
go

exec sp_addextendedproperty 'MS_Description', N'高，不能为空', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'High'
go

exec sp_addextendedproperty 'MS_Description', N'真实长', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 't_Length'
go

exec sp_addextendedproperty 'MS_Description', N'真实宽', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 't_Width'
go

exec sp_addextendedproperty 'MS_Description', N'真实高', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 't_High'
go

exec sp_addextendedproperty 'MS_Description', N'申报价值，不能为空', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Price'
go

exec sp_addextendedproperty 'MS_Description', N'产品状态，1：激活，2：停用', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'GoodsStatus'
go

exec sp_addextendedproperty 'MS_Description', N'最低库存，不能为空，低于这个数，系统会自动报警', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'LowStock'
go

exec sp_addextendedproperty 'MS_Description', N'最高库存，不能为空，高于这个数，系统会自动报警', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'HighStock'
go

exec sp_addextendedproperty 'MS_Description', N'计划总数量', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'PlanQuantity'
go

exec sp_addextendedproperty 'MS_Description', N'实际库存数量 ,指目前该产品在仓库有多少实存数量。', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'FactQuantity'
go

exec sp_addextendedproperty 'MS_Description', N'占用数量 ,指再进行出库时，而未出库成功的时候使用', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'UseQuantity'
go

exec sp_addextendedproperty 'MS_Description', N'所属公司，用来区分属于哪个公司(客户)的数据', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Office'
go

exec sp_addextendedproperty 'MS_Description', N'发货方式价格', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Field5'
go

exec sp_addextendedproperty 'MS_Description', N'作废标志，0表示未void,1表示已voided', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Void_flag'
go

exec sp_addextendedproperty 'MS_Description', N'备注', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Remark'
go

exec sp_addextendedproperty 'MS_Description', N'创建人', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'CreateUser'
go

exec sp_addextendedproperty 'MS_Description', N'创建时间', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'CreateTime'
go

exec sp_addextendedproperty 'MS_Description', N'最后修改人', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'ModifyUser'
go

exec sp_addextendedproperty 'MS_Description', N'最后修改时间', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'ModifyTime'
go

exec sp_addextendedproperty 'MS_Description', N'作废人', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'VoidUser'
go

exec sp_addextendedproperty 'MS_Description', N'作废时间', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'VoidTime'
go

exec sp_addextendedproperty 'MS_Description', N'审核人', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'ApproveUser'
go

exec sp_addextendedproperty 'MS_Description', N'审核时间', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'ApproveTime'
go

exec sp_addextendedproperty 'MS_Description', N'isApproval 1是已审核  0 未审核', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'isApproval'
go

exec sp_addextendedproperty 'MS_Description', N'销售预警', 'SCHEMA', 'dbo', 'TABLE', 'GFF_Goods', 'COLUMN', 'Sales_Num'
go

create index Index_GoodsCode
    on GFF_Goods (GoodsCode) include (SKU)
go

create index px_GFF_CustomerID
    on GFF_Goods (GFF_CustomerID)
go

create index px_SKU
    on GFF_Goods (SKU)
go

create index px_GoodsStatus
    on GFF_Goods (GoodsStatus)
go

create unique index idx_CustomerID_SKU
    on GFF_Goods (GFF_CustomerID, SKU)
go

create index px_CreateTime
    on GFF_Goods (CreateTime)
go

create index IX_GFF_GoodsGFF_CustomerID_Brand_202409281150
    on GFF_Goods (GFF_CustomerID, Brand)
go

create index IX_GFF_GoodsGFF_CustomerID_GoodsStatus_202409281150
    on GFF_Goods (GFF_CustomerID, GoodsStatus)
go

create index IX_GFF_GoodsGFF_CustomerID_Void_flag_202409281150
    on GFF_Goods (GFF_CustomerID, Void_flag)
go

create index IX_GFF_GoodsPlatformSku_202409281150
    on GFF_Goods (PlatformSku)
go

create index IX_GFF_GoodsGFF_CustomerID_StoreName_202409281150
    on GFF_Goods (GFF_CustomerID, StoreName)
go

create index IX_GFF_GoodsGFF_CustomerID_isApproval_202409281150
    on GFF_Goods (GFF_CustomerID, isApproval)
go

create index IX_GFF_GoodsspecId_202409281150
    on GFF_Goods (specId)
go

create index IX_GFF_GoodsGFF_CustomerID_InventoryID_202409281150
    on GFF_Goods (GFF_CustomerID, InventoryID)
go

create index IX_GFF_GoodsField18_202409281150
    on GFF_Goods (Field18)
go

create index IX_GFF_GoodsofferID_20241128164246
    on GFF_Goods (offerID)
go

create index IX_GFF_GoodsGFF_CustomerID_20241128164736
    on GFF_Goods (GFF_CustomerID)
go

