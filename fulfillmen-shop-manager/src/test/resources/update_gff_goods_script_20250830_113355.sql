-- GFF_Goods 表更新脚本
-- 生成时间: 2025-08-30 11:33:55
-- 说明: 根据 platformsku-nayaskuIdSimple.txt 生成的批量更新脚本
-- 更新字段: SKU = platformSku, GoodsCode = ASIN
-- 更新条件: GFF_CustomerID = 13100 AND SKU = nayaSkuId

-- 开始事务
BEGIN TRANSACTION;

UPDATE GFF_Goods
SET SKU       = '4817665076954',
    GoodsCode = 'B0FNWTQVWQ'
WHERE GFF_CustomerID = 13100
  AND SKU = '726787689001034';
UPDATE GFF_Goods
SET SKU       = '4817665076957',
    GoodsCode = 'B0FNWMKFDL'
WHERE GFF_CustomerID = 13100
  AND SKU = '726787689001037';
UPDATE GFF_Goods
SET SKU       = '4817665076958',
    GoodsCode = 'B0FNWV8N6H'
WHERE GFF_CustomerID = 13100
  AND SKU = '726787689001038';
UPDATE GFF_Goods
SET SKU       = '5062649050502',
    GoodsCode = 'B0FNWM9FKF'
WHERE GFF_CustomerID = 13100
  AND SKU = '728629354672153';
UPDATE GFF_Goods
SET SKU       = '5062649050504',
    GoodsCode = 'B0FNWJNXSR'
WHERE GFF_CustomerID = 13100
  AND SKU = '728629354672155';
UPDATE GFF_Goods
SET SKU       = '5286051376860',
    GoodsCode = 'B0FNWLNFX2'
WHERE GFF_CustomerID = 13100
  AND SKU = '728183916490837';
UPDATE GFF_Goods
SET SKU       = '5286051376858',
    GoodsCode = 'B0FNWVD415'
WHERE GFF_CustomerID = 13100
  AND SKU = '728183916490838';
UPDATE GFF_Goods
SET SKU       = '5436802604605',
    GoodsCode = 'B0FNWL2WFJ'
WHERE GFF_CustomerID = 13100
  AND SKU = '728188686921820';
UPDATE GFF_Goods
SET SKU       = '4987963081014',
    GoodsCode = 'B0FNWLD29K'
WHERE GFF_CustomerID = 13100
  AND SKU = '731773669602184';
UPDATE GFF_Goods
SET SKU       = '4999762989537',
    GoodsCode = 'B0FNWTFNKK'
WHERE GFF_CustomerID = 13100
  AND SKU = '731773690876426';
UPDATE GFF_Goods
SET SKU       = '5321900041489',
    GoodsCode = 'B0FNWMP9R2'
WHERE GFF_CustomerID = 13100
  AND SKU = '731773697696383';
UPDATE GFF_Goods
SET SKU       = '6003446563642',
    GoodsCode = 'B0FNWM2YK9'
WHERE GFF_CustomerID = 13100
  AND SKU = '732032370668381';
UPDATE GFF_Goods
SET SKU       = '6003446563643',
    GoodsCode = 'B0FNWJYK62'
WHERE GFF_CustomerID = 13100
  AND SKU = '732032370668385';

-- 总共生成 13 条 UPDATE 语句

-- 提交事务（请在确认无误后取消注释）
-- COMMIT;

-- 回滚事务（如果需要撤销更改）
-- ROLLBACK;
