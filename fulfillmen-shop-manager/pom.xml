<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fulfillmen.shop</groupId>
    <artifactId>fulfillmen-shop</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>fulfillmen-shop-manager</artifactId>
  <name>Fulfillmen Shop Manager ${project.version}</name>
  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <!-- 内部模块依赖 -->
    <dependency>
      <groupId>com.fulfillmen.shop</groupId>
      <artifactId>fulfillmen-shop-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fulfillmen.shop</groupId>
      <artifactId>fulfillmen-shop-dao</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fulfillmen.shop</groupId>
      <artifactId>fulfillmen-shop-domain</artifactId>
    </dependency>

    <!-- 接入 alibaba api -->
    <dependency>
      <groupId>com.fulfillmen.support</groupId>
      <artifactId>fulfillmen-support-alibaba</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fulfillmen.support</groupId>
      <artifactId>fulfillmen-support-wms</artifactId>
    </dependency>

    <!-- Redisson 缓存 - 用于 TenantResolverService -->
    <!--<dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
    </dependency>
-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>

    <!-- data core -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-data-core</artifactId>
    </dependency>
    <!-- mybatisPlus -->
    <dependency>
      <groupId>com.fulfillmen.starter</groupId>
      <artifactId>fulfillmen-starter-data-mp</artifactId>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skipTests>false</skipTests>
        </configuration>
      </plugin>
    </plugins>
  </build>



</project>
