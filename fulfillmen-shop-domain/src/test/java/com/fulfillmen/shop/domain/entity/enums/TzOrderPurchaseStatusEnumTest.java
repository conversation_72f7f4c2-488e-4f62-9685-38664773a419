/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 采购订单状态枚举测试类 重点测试客户取消订单权限控制逻辑
 *
 * <AUTHOR>
 * @date 2025/8/30
 * @description 验证订单状态权限控制方法
 * @since 1.0.0
 */
@DisplayName("采购订单状态枚举测试")
class TzOrderPurchaseStatusEnumTest {

    @Test
    @DisplayName("测试客户取消订单权限控制")
    void testCancellableStatus() {
        // 只有待支付状态允许客户取消
        assertTrue(TzOrderPurchaseStatusEnum.PAYMENT_PENDING.isCancellable(),
            "待支付状态应该允许取消");

        // 所有其他状态都不允许客户取消
        assertFalse(TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED.isCancellable(),
            "临时保存状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED.isCancellable(),
            "支付完成状态不应该允许取消（关键业务规则）");
        assertFalse(TzOrderPurchaseStatusEnum.PENDING_REVIEW.isCancellable(),
            "待审核状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS.isCancellable(),
            "采购中状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT.isCancellable(),
            "部分履约状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.PROCUREMENT_COMPLETED.isCancellable(),
            "采购完成状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED.isCancellable(),
            "供应商已发货状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED.isCancellable(),
            "仓库待收货状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED.isCancellable(),
            "仓库已收货状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.IN_STOCK.isCancellable(),
            "已入库状态不应该允许取消");
        assertFalse(TzOrderPurchaseStatusEnum.ORDER_CANCELLED.isCancellable(),
            "已取消状态不应该允许取消");
    }

    @Test
    @DisplayName("测试支付权限控制")
    void testPayableStatus() {
        // 只有特定状态允许支付
        assertTrue(TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED.isPayable(),
            "临时保存状态应该允许支付");
        assertTrue(TzOrderPurchaseStatusEnum.PAYMENT_PENDING.isPayable(),
            "待支付状态应该允许支付");

        // 其他状态不允许支付
        assertFalse(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED.isPayable(),
            "支付完成状态不应该允许重复支付");
        assertFalse(TzOrderPurchaseStatusEnum.ORDER_CANCELLED.isPayable(),
            "已取消状态不应该允许支付");
    }

    @Test
    @DisplayName("测试订单完成状态判断")
    void testCompletedStatus() {
        // 只有已入库状态被认为是完成
        assertTrue(TzOrderPurchaseStatusEnum.IN_STOCK.isCompleted(),
            "已入库状态应该被认为是完成");

        // 其他状态都不是完成状态
        assertFalse(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED.isCompleted(),
            "仓库已收货状态不应该被认为是完成");
        assertFalse(TzOrderPurchaseStatusEnum.ORDER_CANCELLED.isCompleted(),
            "已取消状态不应该被认为是完成");
    }

    @Test
    @DisplayName("测试进行中状态判断")
    void testInProgressStatus() {
        // 进行中的状态
        assertTrue(TzOrderPurchaseStatusEnum.PENDING_REVIEW.isInProgress(),
            "待审核状态应该被认为是进行中");
        assertTrue(TzOrderPurchaseStatusEnum.PARTIALLY_PROCUREMENT.isInProgress(),
            "部分履约状态应该被认为是进行中");
        assertTrue(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS.isInProgress(),
            "采购中状态应该被认为是进行中");
        assertTrue(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED.isInProgress(),
            "供应商已发货状态应该被认为是进行中");
        assertTrue(TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED.isInProgress(),
            "仓库待收货状态应该被认为是进行中");

        // 非进行中的状态
        assertFalse(TzOrderPurchaseStatusEnum.PAYMENT_PENDING.isInProgress(),
            "待支付状态不应该被认为是进行中");
        assertFalse(TzOrderPurchaseStatusEnum.IN_STOCK.isInProgress(),
            "已入库状态不应该被认为是进行中");
        assertFalse(TzOrderPurchaseStatusEnum.ORDER_CANCELLED.isInProgress(),
            "已取消状态不应该被认为是进行中");
    }

    @Test
    @DisplayName("测试业务规则一致性")
    void testBusinessRuleConsistency() {
        // 关键业务规则：支付完成后不能取消订单
        for (TzOrderPurchaseStatusEnum status : TzOrderPurchaseStatusEnum.values()) {
            if (status.getValue() >= TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED.getValue()
                && status != TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
                assertFalse(status.isCancellable(),
                    String.format("状态 %s (值=%d) 支付完成后不应该允许客户取消",
                        status.getDescription(), status.getValue()));
            }
        }
    }
}
