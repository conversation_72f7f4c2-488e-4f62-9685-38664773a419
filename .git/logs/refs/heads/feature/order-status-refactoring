0000000000000000000000000000000000000000 52ac09b000eaa7c174b01ceca0d9b67e9c32b739 james <<EMAIL>> 1756562527 +0800	branch: Created from develop^0
52ac09b000eaa7c174b01ceca0d9b67e9c32b739 460cf31fa1032ca3b5a0289d0f0d34921210a51b james <<EMAIL>> 1756692498 +0800	commit: docs(fulfillmen-shop): 添加订单状态流程设计和重构任务文档- 新增订单状态流程设计文档，详细描述了订单状态流转的完整设计
460cf31fa1032ca3b5a0289d0f0d34921210a51b b50fe1a6020db629130beaced280bba5e01f65cb james <<EMAIL>> 1756707740 +0800	commit: test:重构测试代码并删除冗余测试类
b50fe1a6020db629130beaced280bba5e01f65cb 968b1c1420a0938b55c81c13702e7e5e7e63d6af james <<EMAIL>> 1756707943 +0800	commit: feat(order): 完成订单状态流转系统重构第二阶段
968b1c1420a0938b55c81c13702e7e5e7e63d6af 7cae8537ef50252519c00c86639343fee5b8f03d james <<EMAIL>> 1756734314 +0800	commit: refactor(order): 优化订单状态流转系统架构设计
7cae8537ef50252519c00c86639343fee5b8f03d 7620848d46b9a62b2aff7db504a10b240ff99c7c james <<EMAIL>> 1756802170 +0800	commit (merge): Merge branch 'develop' into feature/order-status-refactoring
