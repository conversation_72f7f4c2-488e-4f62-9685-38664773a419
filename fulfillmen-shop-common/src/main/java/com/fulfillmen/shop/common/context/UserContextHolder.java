/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.context;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.starter.core.util.ExceptionUtils;
import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户上下文 Holder
 *
 * <pre>
 * 用户上下文和额外上下文，用于在系统中传递用户信息。
 * {@link UserContext} 用户上下文用于传递用户基本信息，如用户ID、用户名、所属租户ID、密码重置时间、密码过期天数等。
 * {@link UserExtraContext} 额外上下文用于传递用户额外信息，如IP、地址、浏览器、操作系统、登录时间等。
 *
 * 所有用户信息会自动添加到 MDC 中，便于日志追踪和问题排查。
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/4/27 14:22
 * @description: 用户上下文持有者，支持 MDC 自动管理
 * @since 1.0.0
 */
@Slf4j
public class UserContextHolder {

    /**
     * 用户ID
     */
    public static final String USER_ID = "userId";
    private static final ThreadLocal<UserContext> CONTEXT_HOLDER = new TransmittableThreadLocal<>();
    private static final ThreadLocal<UserExtraContext> EXTRA_CONTEXT_HOLDER = new TransmittableThreadLocal<>();
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static BigDecimal DEFAULT_SERVICE_FEE_RATE = new BigDecimal("0.15");

    private UserContextHolder() {
    }

    /**
     * 设置上下文
     *
     * @param context  上下文
     * @param isUpdate 是否更新 Session
     */
    public static void setContext(UserContext context, boolean isUpdate) {
        CONTEXT_HOLDER.set(context);
        if (isUpdate) {
            StpUtil.getSessionByLoginId(context.id()).set(SaSession.USER, context);
        }
        // 设置用户基本信息到 MDC
        putUserInfoToMDC(context);
    }

    /**
     * 获取上下文
     *
     * @return 上下文
     */
    public static UserContext getContext() {
        UserContext context = CONTEXT_HOLDER.get();
        if (null == context) {
            context = StpUtil.getSession().getModel(SaSession.USER, UserContext.class);
            CONTEXT_HOLDER.set(context);
            // 确保 MDC 中有用户信息
            putUserInfoToMDC(context);
        }
        return context;
    }

    /**
     * 设置上下文
     *
     * @param context 上下文
     */
    public static void setContext(UserContext context) {
        setContext(context, true);
    }

    /**
     * 获取指定用户的上下文
     *
     * @param userId 用户 ID
     * @return 上下文
     */
    public static UserContext getContext(Long userId) {
        SaSession session = StpUtil.getSessionByLoginId(userId, false);
        if (null == session) {
            return null;
        }
        return session.getModel(SaSession.USER, UserContext.class);
    }

    /**
     * 获取额外上下文
     *
     * @return 额外上下文
     */
    public static UserExtraContext getExtraContext() {
        UserExtraContext context = EXTRA_CONTEXT_HOLDER.get();
        if (null == context) {
            context = getExtraContext(StpUtil.getTokenValue());
            EXTRA_CONTEXT_HOLDER.set(context);
        }
        return context;
    }

    /**
     * 设置额外上下文
     *
     * @param context 额外上下文
     */
    public static void setExtraContext(UserExtraContext context) {
        EXTRA_CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取额外上下文
     *
     * @param token 令牌
     * @return 额外上下文
     */
    public static UserExtraContext getExtraContext(String token) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return UserExtraContext.build(request);
        }
        return null;
    }

    /**
     * 设置完整的用户上下文（包括基本信息和额外信息）
     *
     * @param userContext  用户基本上下文
     * @param extraContext 用户额外上下文
     */
    public static void setFullContext(UserContext userContext, UserExtraContext extraContext) {
        setContext(userContext, true);
        setExtraContext(extraContext);
        log.debug("设置完整用户上下文 - 用户ID: {}, 用户名: {}, IP: {}",
            userContext.id(), userContext.username(), extraContext.getIp());
    }

    /**
     * 从当前请求自动构建并设置完整的用户上下文
     *
     * @param userContext 用户基本上下文
     */
    public static void setContextFromRequest(UserContext userContext) {
        setContext(userContext, true);

        // 尝试从当前请求构建额外上下文
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            UserExtraContext extraContext = UserExtraContext.build(request);
            setExtraContext(extraContext);

            log.debug("从请求自动设置用户上下文 - 用户ID: {}, IP: {}, 浏览器: {}",
                userContext.id(), extraContext.getIp(), extraContext.getBrowser());
        }
    }

    /**
     * 清除上下文
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
        EXTRA_CONTEXT_HOLDER.remove();
        // 清除 MDC 中的用户信息
        clearUserInfoFromMDC();
        log.debug("清除用户上下文和 MDC 信息");
    }

    /**
     * 将用户基本信息放入 MDC
     *
     * @param context 用户上下文
     */
    private static void putUserInfoToMDC(UserContext context) {
        if (context != null) {
            MDC.put(USER_ID, context.id() != null ? context.id().toString() : "");
        }
    }

    /**
     * 从 MDC 中清除用户信息
     */
    private static void clearUserInfoFromMDC() {
        MDC.remove(USER_ID);
    }

    /**
     * 手动刷新 MDC 信息（当上下文信息更新时调用）
     */
    public static void refreshMDC() {
        UserContext userContext = CONTEXT_HOLDER.get();
        UserExtraContext extraContext = EXTRA_CONTEXT_HOLDER.get();

        if (userContext != null) {
            putUserInfoToMDC(userContext);
        }

        log.debug("刷新 MDC 用户信息");
    }

    /**
     * 获取用户 ID
     *
     * @return 用户 ID
     */
    public static Long getUserId() {
        return ExceptionUtils.exToNull(() -> getContext().id());
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public static String getUsername() {
        return ExceptionUtils.exToNull(() -> getContext().username());
    }

    /**
     * 获取当前用户所属租户
     *
     * @return 租户 ID
     */
    public static Long getTenantId() {
        return ExceptionUtils.exToNull(() -> getContext().tenantId());
    }

    /**
     * 获取用户 IP 地址
     *
     * @return IP 地址
     */
    public static String getUserIp() {
        return ExceptionUtils.exToNull(() -> getExtraContext().getIp());
    }

    /**
     * 获取用户 IP 归属地
     *
     * @return IP 归属地
     */
    public static String getUserAddress() {
        return ExceptionUtils.exToNull(() -> getExtraContext().getAddress());
    }

    /**
     * 获取用户浏览器信息
     *
     * @return 浏览器信息
     */
    public static String getUserBrowser() {
        return ExceptionUtils.exToNull(() -> getExtraContext().getBrowser());
    }

    /**
     * 获取用户操作系统信息
     *
     * @return 操作系统信息
     */
    public static String getUserOs() {
        return ExceptionUtils.exToNull(() -> getExtraContext().getOs());
    }

    /**
     * 检查当前是否有用户上下文
     *
     * @return 是否有用户上下文
     */
    public static boolean hasUserContext() {
        return getUserId() != null;
    }

    /**
     * 检查当前是否有用户额外上下文
     *
     * @return 是否有用户额外上下文
     */
    public static boolean hasExtraContext() {
        return EXTRA_CONTEXT_HOLDER.get() != null;
    }

    /**
     * 获取用户 wms cusCode 客户码
     * <p>
     * 注意： 1. 如果用户未绑定 WMS 账户，则返回 null 2. 如果为了创建订单，可以考虑使用 {@link #getWmsCusCodeOrTenantCusCode()}
     * </p>
     *
     * @return 客户码
     */
    public static String getWmsCusCode() {
        return getContext().wmsCusCode();
    }

    /**
     * 获取用户 wms cusCode 客户码或租户 cusCode
     *
     * @return wms cusCode
     */
    public static String getWmsCusCodeOrTenantCusCode() {
        String cusCode = getContext().wmsCusCode();
        if (!StringUtils.hasText(cusCode)) {
            log.info("用户 {} 未绑定 WMS 账户，使用租户 cusCode: {}", getUserId(), cusCode);
            cusCode = EnhancedTenantContextHolder.getCusCode();
        }
        return cusCode;
    }

    /**
     * 获取用户api key
     *
     * @return 用户api key
     */
    public static String getWmsApiKey() {
        return getContext().wmsApiKey();
    }

    /**
     * 获取用户wms服务费
     * <pre>
     * 1. 如果用户有绑定 wms 账户，则使用用户的 wms 服务费
     * 2. 如果用户没有绑定 wms 账户，则使用租户的 wms 服务费
     * 3. 默认租户都统一设定服务费，默认 15%
     * </pre>
     *
     * @return 收取当前用户服务费率
     */
    public static BigDecimal getServiceFeeRate() {
        // wms 服务费默认是整数 8 表示 8% 需要 除以 100
        return Optional.ofNullable(getContext().wmsServiceFee()).map(service -> NumberUtil.div(service, 100)).orElseGet(EnhancedTenantContextHolder::getServiceFeeRate);
    }

}
