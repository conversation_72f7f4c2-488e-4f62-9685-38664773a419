/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.tenant;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext.TenantDetailInfo;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * 增强的租户上下文持有者 在线程本地存储完整的租户信息，配合 TenantFilter 使用
 *
 * <AUTHOR>
 * @date 2025/7/3 11:00
 * @description: 增强的租户上下文持有者
 * @since 2025-07-03
 */
@Slf4j
public class EnhancedTenantContextHolder {

    /**
     * 线程本地存储增强的租户上下文
     */
    private static final TransmittableThreadLocal<EnhancedTenantContext> CONTEXT_HOLDER = new TransmittableThreadLocal<>();

    /**
     * 获取当前线程的增强租户上下文
     *
     * @return 增强的租户上下文，如果不存在则返回空
     */
    public static Optional<EnhancedTenantContext> getEnhancedTenantContext() {
        return Optional.ofNullable(CONTEXT_HOLDER.get());
    }

    /**
     * 设置当前线程的增强租户上下文
     *
     * @param context 增强的租户上下文
     */
    public static void setEnhancedTenantContext(EnhancedTenantContext context) {
        CONTEXT_HOLDER.set(context);
        log.debug("设置增强租户上下文: tenantId={}, tenantName={}",
            context != null ? context.getTenantId() : null,
            context != null ? context.getTenantName() : null);
    }

    /**
     * 获取当前租户ID
     *
     * @return 租户ID，如果不存在则返回null
     */
    public static String getCurrentTenantId() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getTenantId)
            .orElse(null);
    }

    /**
     * 获取当前租户名称
     *
     * @return 租户名称，如果不存在则返回null
     */
    public static String getCurrentTenantName() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getTenantName)
            .orElse(null);
    }

    /**
     * 获取当前租户 WmsCusCode
     *
     * @return 租户 Wms CusCode，如果不存在则返回null
     */
    public static String getCusCode() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getDetailInfo)
            .map(TenantDetailInfo::getWmsCusCode)
            .orElse(null);
    }

    /**
     * 获取当前租户 WmsApiKey
     *
     * @return 租户 Wms ApiKey，如果不存在则返回null
     */
    public static String getWmsApiKey() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getDetailInfo)
            .map(TenantDetailInfo::getWmsApiKey)
            .orElse(null);
    }

    /**
     * 获取当前租户 WmsCusName
     *
     * @return 租户 Wms CusName，如果不存在则返回null
     */
    public static String getWmsCusName() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getDetailInfo)
            .map(TenantDetailInfo::getWmsCusName)
            .orElse(null);
    }

    /**
     * 获取当前租户 WmsCusId
     *
     * @return 租户 Wms CusId，如果不存在则返回null
     */
    public static String getWmsCusId() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getDetailInfo)
            .map(TenantDetailInfo::getWmsCusId)
            .orElse(null);
    }

    /**
     * 获取当前租户 服务费
     * <pre>
     * 获取租户的服务费费率。
     * 1. 获取当前租户的服务费率
     * 2. 如果不存在，则返回默认值 0.15
     * </pre>
     *
     * @return 租户服务费，如果不存在则返回null
     */
    public static BigDecimal getServiceFeeRate() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getDetailInfo)
            .map(TenantDetailInfo::getServiceFee)
            .map(serviceFee -> NumberUtil.div(BigDecimal.valueOf(serviceFee), 100))
            // 后期整改通过配置 来定义
            .orElse(NumberUtil.round("0.15", 2));
    }

    /**
     * 获取当前租户的默认仓库
     *
     * @return 默认仓库信息，如果不存在则返回null
     */
    public static EnhancedTenantContext.TenantWarehouseInfo getCurrentDefaultWarehouse() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getDefaultWarehouse)
            .orElse(null);
    }

    /**
     * 获取当前租户的主域名
     *
     * @return 主域名，如果不存在则返回null
     */
    public static String getCurrentPrimaryDomain() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::getPrimaryDomain)
            .orElse(null);
    }

    /**
     * 检查当前租户的套餐是否过期
     *
     * @return 如果过期返回true，否则返回false
     */
    public static boolean isCurrentTenantPlanExpired() {
        return getEnhancedTenantContext()
            .map(EnhancedTenantContext::isPlanExpired)
            .orElse(false);
    }

    /**
     * 清理当前线程的增强租户上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
        log.debug("清理增强租户上下文");
    }

    /**
     * 检查当前线程是否有增强租户上下文
     *
     * @return 如果存在返回true，否则返回false
     */
    public static boolean hasEnhancedTenantContext() {
        return CONTEXT_HOLDER.get() != null;
    }
}
